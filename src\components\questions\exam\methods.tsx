import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { javaCode206, javaCode210, javaCode211 } from "./snips/basicSnips";

export const methods : Questions[] = [
    {
        id: 206,
        category: 6,
        isImage: false,
        title: "What should be added instead?",
        image: javaCode206,
        answers: [
            {
                id: 1,
                content: '-----',
                correct: true
            }, {
                id: 2,
                content: 'private',
                correct: true
            }, {
                id: 3,
                content: 'String',
                correct: false
            }, {
                id: 4,
                content: 'public',
                correct: true
            }
        ],
        explanation: '----- or nothing, private and public are access modifiers, which can be used at ' +
                'the given space.\n\nString is not an access modifier, but rather the return typ' +
                'e, methods java should have only one return type already have a return type, in ' +
                'this case it is void.'
    }, {
        id: 207,
        category: 6,
        isImage: false,
        title: "What will compile?",
        answers: [
            {
                id: 1,
                content: 'public static void hurry(){}',
                correct: true
            }, {
                id: 2,
                content: 'public final static String void main(){}',
                correct: false
            }, {
                id: 3,
                content: 'static final void run(){}',
                correct: true
            }, {
                id: 4,
                content: 'public void return(){}',
                correct: false
            }, {
                id: 5,
                content: 'void public run(){}',
                correct: false
            }, {
                id: 6,
                content: 'public static run(){}',
                correct: false
            }
        ],
        explanation: 'Line 1 -> This code snippet is a valid method header, so true.\n\nLine 2 -> This me' +
                'thod contains 2 return types: void and String which is not allowed, false.\n\nLine ' +
                '3 -> This is a valid static final method with return type, so false.\n\nLine 4 -> r' +
                'eturn cannot be used as name for a function since it is reserved keyword, theref' +
                'ore false.\n\nLine 5 -> Each method starts either with keywords such static, final ' +
                'or with access modifiers. It is also possible that both of them are absent so th' +
                'en return type steps forward and then the name of the method. But it is violatio' +
                'n to start with return type and continue with access modifiers, so it is false.\n\n' +
                'Line 6 -> Absence of return type, so false.'
    }, {
        id: 208,
        category: 6,
        isImage: false,
        title: "What will compile?",
        answers: [
            {
                id: 1,
                content: 'public void a(){return null;}',
                correct: false
            }, {
                id: 2,
                content: 'public final static Integer void b(){return;}',
                correct: false
            }, {
                id: 3,
                content: 'protected final void c(){return;}',
                correct: true
            }, {
                id: 4,
                content: 'public int d(){return null;}',
                correct: false
            }, {
                id: 5,
                content: 'public int e(){return 7;}',
                correct: true
            }, {
                id: 6,
                content: 'public void f(){}',
                correct: true
            }
        ],
        explanation: 'a() -> When you have void return type, you are not allowed to return statement wi' +
                'th a value together causes compile error, null inclusively, so therefore false.\n\nb() -> This method i' +
                's forced to return Integer object, but does not return anything, false.\n\nc() -> Th' +
                'is is a valid method, with valid body. With void as a return type, you can use r' +
                'eturn with nothing, so true.\n\nd()-> Primitive return type arenot allowed to retur' +
                'n null, if this function had object Integer, then returning null, was allowed. B' +
                'ut in this case false.\n\ne() -> This method has no issues, it has return type int ' +
                'and return literal 7, which is processed as int, so therefore true.\n\nf() -> Method with void as retu' +
                'rn type, with no body, is allowed and donot cause any errors -> true.'
    }, {
        id: 209,
        category: 6,
        isImage: false,
        title: "What will not compile?",
        answers: [
            {
                id: 1,
                content: 'public void a(int... vals){}',
                correct: false
            }, {
                id: 2,
                content: 'public void b(String[] names, int...vals){}',
                correct: false
            }, {
                id: 3,
                content: 'public void c(String name, int...vals){}',
                correct: false
            }, {
                id: 4,
                content: 'public void d(int... vals, String... names){}',
                correct: true
            }, {
                id: 5,
                content: 'public void e(int... vals, String[] names){}',
                correct: true
            }, {
                id: 6,
                content: 'public void f(int vals, ...String names){}',
                correct: true
            }
        ],
        explanation: 'a() -> This is a correct method declaration. The ellipsis (...) in the parameter' +
                ' list indicates that the method can accept a variable number of integer argument' +
                's.\n\nb() ->  In this method declaration, you have two parameters: String[] names a' +
                'nd int...vals. The order of parameters is correct; you have a regular array para' +
                'meter (String[] names) followed by a varargs parameter (int...vals).\n\nc() ->  The' +
                ' method has two parameters: String name and int...vals. The order of parameters ' +
                'is correct; the regular parameter String name comes before the varargs parameter' +
                ' int...vals.\n\nd() -> In Java, a method can have only one variable arguments param' +
                'eter. Having two variable arguments parameters like int...vals, String... names ' +
                'is not allowed.\n\ne() ->  is not valid Java code. In Java, when using variable-len' +
                'gth arguments (varargs), the varargs parameter must be the last parameter in the' +
                ' method declaration. The code you provided violates this rule by placing int... ' +
                'vals before the regular array parameter String[] names.\n\nf() ->  is not valid Jav' +
                'a code. The ellipsis (...) for variable-length arguments (varargs) should be pla' +
                'ced before the parameter type, not after the parameter name.\n\nTo sum up -> d e f'
    }, {
        id: 210,
        category: 6,
        isImage: false,
        title: "Which method call returns 3 ?",
        image: javaCode210,
        answers: [
            {
                id: 1,
                content: 'calculate(100);',
                correct: false
            }, {
                id: 2,
                content: 'calculate(100, 75);',
                correct: false
            }, {
                id: 3,
                content: 'calculate(100, 75, 50);',
                correct: false
            }, {
                id: 4,
                content: 'calculate(100, 75, 50, 30);',
                correct: true
            }, {
                id: 5,
                content: 'calculate(100, new Integer[2]);',
                correct: false
            }, {
                id: 6,
                content: 'calculate(100, new Integer[3]);',
                correct: true
            }
        ],
        explanation: 'In order to fit the requirements we have to have atleast 4 parameters in the met' +
                'hod, or a parameter and array of size 3 and more.\n \nOnly 2 options fits to the giv' +
                'en criteria:\n \ncalculate(100, 75, 50, 30);\ncalculate(100, new Integer[3]); '
    }, {
        id: 211,
        category: 6,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode211,
        answers: [
            {
                id: 1,
                content: 'intintint',
                correct: false
            }, {
                id: 2,
                content: 'charshortint',
                correct: false
            }, {
                id: 3,
                content: 'intintObject',
                correct: false
            }, {
                id: 4,
                content: 'intshortObject',
                correct: true
            }, {
                id: 5,
                content: 'shortshortint',
                correct: false
            }, {
                id: 6,
                content: 'shortintobject',
                correct: false
            }
        ],
        explanation: 'print(ch) -> Due to the absence of char method, we are promoting it to int.\n \nprin' +
                't(sh) -> Here the logic is straightforward, we have method designed for shorts a' +
                'nd it is called.\n \nprint(new Integer(7)) -> Here, you are passing an Integer objec' +
                't. There is an overload defined for Object arguments print(Object x), so that ' +
                'is the method that gets called. The output is "Object".\n \nSumming up -> intshortOb' +
                'ject'
    }
]