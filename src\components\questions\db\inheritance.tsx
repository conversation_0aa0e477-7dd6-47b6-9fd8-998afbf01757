import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import {javaCode227, javaCode228, javaCode229, javaCode230} from "../exam/snips/basicSnips";
export const inherDB : Questions[] = [
    {
        id: 227,
        category: 7,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode227,
        answers: [
            {
                id: 1,
                content: 'Human is eating',
                correct: true
            }, {
                id: 2,
                content: 'Parent is eating',
                correct: false
            }, {
                id: 3,
                content: 'Compilation error because of public void eat(int weight); in Parent class',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error because of public void eat(); in Human class',
                correct: false
            }
        ],
        explanation: 'This java snippet responses Human is eating because here we have method overload' +
                'ing instead of overriding.\nOverloading means having multiple methods with the s' +
                'ame name but different parameters.\n \nTo override a method, the subclass method' +
                ' must have the same signature as the superclass method.\n \nIf this criteria was' +
                ' fulfilled then we would have printed Parent is eating.'
    }, {
        id: 228,
        category: 7,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode228,
        answers: [
            {
                id: 1,
                content: 'Human is eating',
                correct: false
            }, {
                id: 2,
                content: 'Parent is eating',
                correct: false
            }, {
                id: 5,
                content: 'Compilation error because of public void eat(); in Human class',
                correct: false
            }, {
                id: 6,
                content: 'Compilation error because of public void sleep(); in Human class',
                correct: true
            }, {
                id: 3,
                content: 'Compilation error because of public void eat(int weight); in Parent class',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error because of public void sleep(); in Parent class',
                correct: false
            }
        ],
        explanation: 'This java code snippet has only 1 compilation error, abstract methods in abstrac' +
                't classes cannot have bodies, abstract method sleep() in Human class violates th' +
                'is rule and therefore compilation sends us error notification.'
    }, {
        id: 229,
        category: 7,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode229,
        answers: [
            {
                id: 1,
                content: 'Human is sleeping',
                correct: false
            }, {
                id: 2,
                content: 'Parent is sleeping',
                correct: false
            }, {
                id: 3,
                content: 'Compilation error because of public void sleep(); in Parent class',
                correct: true
            }, {
                id: 4,
                content: 'Compilation error because of public final void sleep(); in Human class',
                correct: false
            }
        ],
        explanation: 'This java snippet produces compilation error because final methods cannot be ove' +
                'rriden. \n \npublic void sleep(); in Parent class tries to override public final' +
                ' void sleep(); in Human class and therefore compiler throws compilation error.'
    }, {
        id: 230,
        category: 7,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode230,
        answers: [
            {
                id: 1,
                content: '30',
                correct: false
            }, {
                id: 2,
                content: 'Compilation error because of Double getAge(); in IGrow interface',
                correct: false
            }, {
                id: 3,
                content: '25',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error because of  Integer getAge() method in Human class',
                correct: true
            }, {
                id: 5,
                content: '7',
                correct: false
            }, {
                id: 6,
                content: 'Compilation error because of new Human().getAge(7) in main method',
                correct: false
            }
        ],
        explanation: 'There is a conflict in methods overriden from interface, specifically class Huma' +
                'n didnot properly override the getAge(int val) method.\n \nReturn type of the me' +
                'thod is Integer, but it is required to be Double so compiler informs with the fo' +
                'llowing error: the return type is incompatible with IGrow.getAge(int)'
    }
];