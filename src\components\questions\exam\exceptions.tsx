import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { javaCode236, javaCode237, javaCode238 } from "./snips/basicSnips";

export const exceptions : Questions[] = [
    {
        id: 235,
        category: 8,
        isImage: false,
        title: "What is/are true?",
        answers: [
            {
                id: 1,
                content: '<PERSON><PERSON><PERSON> can declare only checked exceptions',
                correct: false
            }, {
                id: 2,
                content: '<PERSON><PERSON><PERSON> can declare only unchecked exceptions',
                correct: false
            }, {
                id: 3,
                content: 'Runtime exceptions are equivalent of checked exceptions',
                correct: false
            }, {
                id: 4,
                content: 'Runtime exceptions are equivalent of unchecked exceptions',
                correct: true
            }, {
                id: 5,
                content: 'Only subclasses of class Exception can be handled',
                correct: false
            }
        ],
        explanation: 'Runtime exceptions are the same thing as unchecked exceptions.\n \nThere are a t' +
                'housands unchecked exceptions, they are allowed to be declared, but they donot h' +
                'ave to be declared. Checked exceptions are either handled or declared.'
    }, {
        id: 236,
        category: 8,
        isImage: false,
        title: "What should be inserted in blanks?",
        image: javaCode236,
        answers: [
            {
                id: 1,
                content: 'Line 1 -> throw',
                correct: false
            }, {
                id: 2,
                content: 'Line 1 -> throws',
                correct: true
            }, {
                id: 3,
                content: 'Line 1 -> throw new',
                correct: false
            }, {
                id: 4,
                content: 'Line 2 -> throw',
                correct: false
            }, {
                id: 5,
                content: 'Line 2 -> throws',
                correct: false
            }, {
                id: 6,
                content: 'Line 2 -> throw new',
                correct: true
            }
        ],
        explanation: 'On line 1 keyword throws is used, The throws keyword is used in the method signa' +
                'ture to declare that a method can throw certain types of exceptions. This inform' +
                's the caller of the method that they need to handle or propagate these exception' +
                's. It does not actually throw the exception but rather indicates the potential f' +
                'or exceptions to be thrown.\n \nLine 2 needs throw which is used inside the meth' +
                'od body to actually throw an exception and since in our method Exception is not ' +
                'static and not initialized we need to add new -> throw new Exception();'
    }, {
        id: 237,
        category: 8,
        isImage: false,
        title: "What is/are thrown exceptions?",
        image: javaCode237,
        answers: [
            {
                id: 1,
                content: 'ArrayIndexOutOfBoundsException',
                correct: true
            }, {
                id: 2,
                content: 'NumberFormatException',
                correct: false
            }, {
                id: 3,
                content: 'ArithmeticException',
                correct: true
            }, {
                id: 4,
                content: 'IllegalArgumentException',
                correct: false
            }, {
                id: 5,
                content: 'Another exception',
                correct: false
            }
        ],
        explanation: 'There are 2 exception thrown by the code:\n \nint val = arr[0] / x * arr[2]\n \n' +
                'In the equation if we go from left to right, first exception that we meet is div' +
                'ision to 0, leading us to ArithmeticException, if this part is fixed then we are' +
                ' headed to ArrayIndexOutOfBoundsException due to request to arr[2] which violate' +
                's the size of declared array. '
    }, {
        id: 238,
        category: 8,
        isImage: false,
        title: "What exception is thrown first?",
        image: javaCode238,
        answers: [
            {
                id: 1,
                content: 'Code will not compile',
                correct: false
            },  {
                id: 2,
                content: 'ArithmeticException',
                correct: false
            }, {
                id: 3,
                content: 'ArrayIndexOutOfBoundsException',
                correct: true
            }, {
                id: 4,
                content: 'ElementNotFoundException',
                correct: false
            }
        ],
        explanation: 'This code do not contain any compilation errors. On the other hand this code thr' +
                'ows ArrayIndexOutOfBoundsException since the code accessing arr[2].\n \nThis cod' +
                'e throws 2 exceptions sequentially, so if we remove arr[2] reference the code wi' +
                'll throw ArithmeticException (division by zero), but question asked what excepti' +
                'on will be thrown first.'
    }
]