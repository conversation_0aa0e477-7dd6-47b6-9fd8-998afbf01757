import AsyncStorage from '@react-native-async-storage/async-storage';

export const markedData = "marked";
export const statsData = "stats";
export const initExam = "initExam630";
export const oldInitExam = "initExam";
export const answerSheet = "answerSheet";




class StorageService {

    static async getData(key : string){
        try {
            const value = await AsyncStorage.getItem(key);
            return  JSON.parse(value);
        } catch (e) {
            console.log('Error reading value from AsyncStorage');
        }
    };
    

    static async getAll() {
        try {
          const keys = await AsyncStorage.getAllKeys();
          const items = await AsyncStorage.multiGet(keys);
          return items.map(item => JSON.parse(item[1]));
        } catch (error) {
          console.log(error);
          return null;
        }
    }

    static async post(key:string, value:any) {
      try {
        await AsyncStorage.setItem(key, JSON.stringify(value));
        return true;
      } catch (error) {
        console.log(error);
        return false;
      }
    }

  
    static async delete(key:string) {
      try {
        await AsyncStorage.removeItem(key);
        return true;
      } catch (error) {
        console.log(error);
        return false;
      }
    }
  
    static async clean(){
        try {
            await AsyncStorage.clear();
            return true;
        } catch (error) {
            console.log(error)
            return false;
        }
    }
  }
  
  export default StorageService;