import React, { Component } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';

import { Ionicons, MaterialCommunityIcons, Zocial } from '@expo/vector-icons';

interface AdState {

}

interface CustomModalWindowProps {
  closeModal?: () => void;
}

const deviceHeight = Dimensions.get('window').height;
const deviceWidth = Dimensions.get('window').width;


class AdLoading extends Component<CustomModalWindowProps, AdState> {

  constructor(props) {
    super(props);
    this.state = {};
  }


  closeModal = () =>{
    this.props.closeModal(); 
  }


  render() {
    return (
      <View style={styles.container}>
        <View style={styles.form}>
            <View style={{flexDirection:'row', alignContent:'center'}}>
                <Ionicons name="logo-google" size={45} color="#215e9d" style={{alignSelf:'center'}} />
                <MaterialCommunityIcons name="google-chrome" size={45} color="#215e9d" style={{alignSelf:'center'}} />
                <MaterialCommunityIcons name="google-chrome" size={45} color="#215e9d" style={{alignSelf:'center'}}/>
                <Zocial name="google" size={45} color="#215e9d" style={{alignSelf:'center'}}  />
                <Text style={{fontSize:45, color:'#215e9d'}}>&#8467;&#8496;</Text>                
            </View>
            
            <Text style={styles.textStyles}>
                Ad is still loading.
            </Text>          
          <View style={styles.buttonContainer}>
              <TouchableOpacity style={styles.button} onPress={() => this.closeModal()}>
                  <Text style={styles.buttonText}>Close</Text>
                  <MaterialCommunityIcons name="google-downasaur" size={20} color="white" style={{paddingLeft:'5%'}}/>
              </TouchableOpacity>
              
          </View>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',    
    height:deviceHeight*0.9,
    width:deviceWidth*0.9,
  },
  form: {
    width: '100%',
    marginHorizontal:15,
    padding:'8%',
    backgroundColor:'white',
    justifyContent: 'center', 
    alignItems: 'center',
  },
  textStyles:{
    fontFamily:'monospace',
    textAlign:'center',
    marginTop:'5%'
  },
  buttonContainer:{
    flexDirection: 'row',
    alignItems:'center',
    justifyContent: 'space-around',
  },

  button: {
    marginTop: 20,
    marginLeft:10,
    marginRight:10,
    backgroundColor: '#215e9d',
    alignItems:'center',
    flexDirection:'row',
    borderRadius: 5,
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  buttonText: {
    color: '#fff',
    fontSize: 15,
    fontFamily:'monospace'
  },
});

export default AdLoading;