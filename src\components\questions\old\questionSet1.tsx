//
import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import {
    task1,
    task10,
    task11,
    task12,
    task14,
    task15,
    task16,
    task17,
    task18,
    task19,
    task2,
    task20,
    task21,
    task22,
    task23,
    task24,
    task25,
    task26,
    task28,
    task29,
    task3,
    task30,
    task31,
    task32,
    task4,
    task5,
    task7,
    task8,
    task9
} from "./codeSnippets/questionSet1Snippets";
export const passedSetPart1 : Questions[] = [
    {
        id: 1,
        category: 4,
        isImage: false,
        title: "Which of these are valid array declarations?",
        image: task1,
        answers: [
            {
                id: 1,
                content: 'All of them',
                correct: true
            }, {
                id: 2,
                content: '1',
                correct: false
            }, {
                id: 3,
                content: '2',
                correct: false
            }, {
                id: 4,
                content: '3',
                correct: false
            }
        ],
        explanation: 'All these are valid two-dimensional array declarations.'
    }, {
        id: 2,
        category: 6,
        isImage: false,
        title: "What will be printed in console?",
        image: task2,
        answers: [
            {
                id: 1,
                content: 'Compilation fails',
                correct: false
            }, {
                id: 2,
                content: 'Exception is thrown at runtime',
                correct: false
            }, {
                id: 3,
                content: '2',
                correct: true
            }, {
                id: 4,
                content: '1',
                correct: false
            }, {
                id: 5,
                content: '0',
                correct: false
            }
        ],
        explanation: 'Method public void Question() isnot a constructor, but it is a method. The only ' +
                'constructor here is marked private and therefore the answer 2'
    }, {
        id: 3,
        category: 2,
        isImage: false,
        title: "What will be printed in console?",
        image: task3,
        answers: [
            {
                id: 1,
                content: '6566',
                correct: false
            }, {
                id: 2,
                content: 'ab',
                correct: false
            }, {
                id: 3,
                content: 'Compilation fails',
                correct: true
            }, {
                id: 4,
                content: 'Exception is thrown at runtime',
                correct: false
            }
        ],
        explanation: 'Cannot convert from int[] to char[]'
    }, {
        id: 4,
        category: 4,
        isImage: false,
        title: "What will be printed in console?",
        image: task4,
        answers: [
            {
                id: 1,
                content: "15",
                correct: true
            }, {
                id: 2,
                content: "10",
                correct: false
            }, {
                id: 3,
                content: "5",
                correct: false
            }, {
                id: 4,
                content: "0",
                correct: false
            }, {
                id: 5,
                content: "Compilation Error",
                correct: false
            }
        ],
        explanation: "The code initializes an array 'numbers' and calculates the sum using an enhanced" +
                " for loop. The correct answer is 15, as it sums up all the elements (1 + 2 + 3 +" +
                " 4 + 5)."
    }, {
        id: 5,
        category: 2,
        isImage: false,
        title: "What will be printed in console?",
        image: task5,
        answers: [
            {
                id: 1,
                content: '0 /  / 0.0',
                correct: false
            }, {
                id: 2,
                content: 'null / null / null',
                correct: false
            }, {
                id: 3,
                content: '0 / null / 0.0',
                correct: false
            }, {
                id: 4,
                content: '0 / null / null',
                correct: true
            }
        ],
        explanation: 'number is primitive int type and therefore = 0, String and Double both are objec' +
                'ts (please be aware that double is primitive type = 0.0 whether Double is an obj' +
                'ect = null) and therefore null and null.'
    }, {
        id: 6,
        category: 1,
        isImage: false,
        title: '\n 1. Compiled java file can run on any platform, but virtual machine requires p' +
                'latform. \n 2. Java has WORA principle. Which of these are true?',

        answers: [
            {
                id: 1,
                content: '1 and 2',
                correct: true
            }, {
                id: 2,
                content: 'only 1',
                correct: false
            }, {
                id: 3,
                content: 'only 2',
                correct: false
            }, {
                id: 4,
                content: 'neither',
                correct: false
            }
        ],
        explanation: 'WORA is refers to the fact that an application written is Java can be run on any' +
                ' platform. JVM requires installation specific to the platform. Concluding both o' +
                'f the statements are true.'
    }, {
        id: 7,
        category: 1,
        isImage: false,
        title: "What will happen if this code is executed with no parameters?",
        image: task7,
        answers: [
            {
                id: 1,
                content: 'It will print empty string',
                correct: false
            }, {
                id: 2,
                content: 'It will print null',
                correct: false
            }, {
                id: 3,
                content: 'It will not compile',
                correct: false
            }, {
                id: 4,
                content: 'It will throw exception',
                correct: true
            }
        ],
        explanation: 'This code snippet will throw exception because args[0] is accessed when the args' +
                ' array is empty.'
    }, {
        id: 8,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task8,
        answers: [
            {
                id: 1,
                content: '30...0',
                correct: false
            }, {
                id: 2,
                content: 'Compilation fails',
                correct: true
            }, {
                id: 3,
                content: '0...0',
                correct: false
            }, {
                id: 4,
                content: '30...20',
                correct: false
            }, {
                id: 5,
                content: 'Exception is thrown at runtime',
                correct: false
            }
        ],
        explanation: 'This snippet throws compilation failure, because variable value is declared only' +
                ' inside of if brackets and therefore not visible outside of it'
    }, {
        id: 9,
        category: 4,
        isImage: false,
        title: "What will be printed in console?",
        image: task9,
        answers: [
            {
                id: 1,
                content: '@3fee733d...null...null...null',
                correct: false
            }, {
                id: 2,
                content: 'null...null...null...null',
                correct: false
            }, {
                id: 3,
                content: '@3fee733d... ... ... ',
                correct: false
            }, {
                id: 4,
                content: 'Compilation fails',
                correct: false
            }, {
                id: 5,
                content: 'Exception is thrown at runtime',
                correct: true
            }
        ],
        explanation: ' Declared array strs with a size of 2 (new String[2]), which means it can hold t' +
                'wo elements at indices 0 and 1. However, you are trying to access strs[2], which' +
                ' is outside the bounds of the array which will throw exception during run time'
    }, {
        id: 10,
        category: 2,
        isImage: false,
        title: "What code line causes any compilation or run time error?",
        image: task10,
        answers: [
            {
                id: 1,
                content: 'Nothing is wrong with this code',
                correct: true
            }, {
                id: 2,
                content: '1',
                correct: false
            }, {
                id: 3,
                content: '1, 2',
                correct: false
            }, {
                id: 4,
                content: '3',
                correct: false
            }, {
                id: 5,
                content: '4',
                correct: false
            }
        ],
        explanation: '1 =>  Declares a double variable named pi and assigns the value 3.14 to it. 2 =>' +
                ' Declares an int variable named pi1 and assigns the truncated integer value 3 to' +
                ' it by explicitly casting the double value 3.14. 3 => Declares a float variable ' +
                'named fl and assigns the value 10.0 as a single-precision floating-point literal' +
                '. 4 => Declares a double variable named dl and assigns the value 14.3 as a doubl' +
                'e-precision floating-point literal after implicit promotion from float to double' +
                '. So nothing is wrong with the code and everything will compile.'
    }, {
        id: 11,
        category: 4,
        isImage: false,
        title: "Which line is a valid array declaration, which don't cause any compilation error" +
                "?",
        image: task11,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '2',
                correct: false
            }, {
                id: 3,
                content: '3',
                correct: false
            }, {
                id: 4,
                content: '4',
                correct: true
            }
        ],
        explanation: 'General knowledge is in primitive arrays, we cannot assign one array to another ' +
                'array. Line 1 will result in a compilation error because you cannot directly ass' +
                'ign an array of a wider type (char) to an array of a narrower type (int). Line 2' +
                ' will result in a compilation error because int and long are different primitive' +
                ' types, and their arrays are not covariant due to the type mismatch. Line 3 will' +
                ' result in a compilation error because you cannot directly assign an array of na' +
                'rrower type (byte) to an array of wider type (char). Line 4 is allowed because a' +
                'll classes in Java inherit from the Object class, so you can assign an array of ' +
                'a derived class to an array of its parent class type.'
    }, {
        id: 12,
        category: 6,
        isImage: false,
        title: "What will be printed in console?",
        image: task12,
        answers: [
            {
                id: 1,
                content: '0',
                correct: false
            }, {
                id: 2,
                content: '15',
                correct: true
            }, {
                id: 3,
                content: '20',
                correct: false
            }, {
                id: 4,
                content: 'Nothing wrong with the code',
                correct: true
            }, {
                id: 5,
                content: 'Compilation error',
                correct: false
            }, {
                id: 6,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'There are no syntax errors and exceptions which will be thrown in the code. The ' +
                'value of variable value isnot 0 or 20, it remains 15. This is because the functi' +
                'on doesnot return any value. It is a tricky question'
    }, {
        id: 13,
        category: 6,
        isImage: false,
        title: 'Which variables have been provided with default values?',
        answers: [
            {
                id: 1,
                content: 'Local variables',
                correct: false
            }, {
                id: 2,
                content: 'Static variables',
                correct: true
            }, {
                id: 3,
                content: 'Instance variables',
                correct: true
            }, {
                id: 4,
                content: 'None of them',
                correct: false
            }, {
                id: 5,
                content: 'All of them',
                correct: false
            }
        ],
        explanation: 'Only instance and static variables are initialized by JVM. Locals on the other h' +
                'and arenot and therefore required explicit initialization'
    }, {
        id: 14,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task14,
        answers: [
            {
                id: 1,
                content: 'bc',
                correct: false
            }, {
                id: 2,
                content: '197',
                correct: true
            }, {
                id: 3,
                content: 'Compilation error',
                correct: false
            }, {
                id: 4,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'In order to solve this we need to know that + operator among chars will always l' +
                'ead to int type and considering the unicode values of both of them (98 + 99) => ' +
                '197'
    }, {
        id: 15,
        category: 2,
        isImage: false,
        title: "What will be printed in console?",
        image: task15,
        answers: [
            {
                id: 1,
                content: "Result 1: false\nResult 2: true\nResult 3: true",
                correct: true
            }, {
                id: 2,
                content: "Result 1: true\nResult 2: true\nResult 3: false",
                correct: false
            }, {
                id: 3,
                content: "Result 1: true\nResult 2: false\nResult 3: true",
                correct: false
            }, {
                id: 4,
                content: "Result 1: false\nResult 2: false\nResult 3: false",
                correct: false
            }, {
                id: 5,
                content: "Compilation Error",
                correct: false
            }
        ],
        explanation: "In Java, the '==' operator checks reference equality for objects, while the 'equ" +
                "als' method checks content equality for strings. Result 1 is false because 'str1" +
                "' and 'str2' are different objects, but Result 2 and Result 3 are true because t" +
                "he content is the same."
    }, {
        id: 16,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task16,
        answers: [
            {
                id: 1,
                content: 'null / true / false',
                correct: false
            }, {
                id: 2,
                content: 'false / false / false',
                correct: false
            }, {
                id: 3,
                content: 'null / null / null',
                correct: false
            }, {
                id: 4,
                content: 'false / true / false',
                correct: true
            }, {
                id: 5,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'Boolean constructor parses null into false. parseBoolean(variable) and valueOf(v' +
                'ariable) methods, both parse their variables into lowercase and them compares wi' +
                'th true or false, in our case they return true , false. Combining we have => fal' +
                'se / true / false'
    }, {
        id: 17,
        category: 2,
        isImage: false,
        title: "What will be printed in console?",
        image: task17,

        answers: [
            {
                id: 1,
                content: 'd1',
                correct: false
            }, {
                id: 2,
                content: 'd2',
                correct: true
            }, {
                id: 3,
                content: 'd3',
                correct: false
            }, {
                id: 4,
                content: 'd4',
                correct: true
            }
        ],
        explanation: ' The underscore _ is used as a visual separator, but it cannot appear at the beg' +
                'inning, end, or directly before or after the decimal point. Therefore our answer' +
                ' is d2 and d4'
    }, {
        id: 18,
        category: 4,
        isImage: false,
        title: "What is wrong with the code?",
        image: task18,
        answers: [
            {
                id: 1,
                content: '20.0',
                correct: false
            }, {
                id: 2,
                content: 'Compilation error at line 2',
                correct: false
            }, {
                id: 3,
                content: 'Compilation error at line 3',
                correct: false
            }, {
                id: 4,
                content: 'Code compiles fine',
                correct: true
            }, {
                id: 5,
                content: 'Exception is thrown at runtime',
                correct: true
            }
        ],
        explanation: 'There is no compilation error because line 2 and 3 correctly assigns values to e' +
                'lements.However at runtime, when trying to access arr[3] which causes exception ' +
                'to be thrown. Hint => The ` d ` at the end of a number or literal indicates that' +
                ' the number is of the double. '
    }, {
        id: 19,
        category: 2,
        isImage: false,
        title: "What will be printed in console?",
        image: task19,

        answers: [
            {
                id: 1,
                content: '3 / 4 / 7',
                correct: false
            }, {
                id: 2,
                content: '3 / 4 / true',
                correct: false
            }, {
                id: 3,
                content: '7 / 10 / true',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: true
            }, {
                id: 5,
                content: 'Exception is thrown at runtime',
                correct: false
            }
        ],
        explanation: 'This code snippet provide compilation error at the last line of code. This error' +
                ' occurs because the code tries to assign an int value to a boolean variable, whi' +
                'ch is not allowed in Java. '
    }, {
        id: 20,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task20,
        answers: [
            {
                id: 1,
                content: 'Java Questions',
                correct: false
            }, {
                id: 2,
                content: 'Java Questions and Answers',
                correct: true
            }, {
                id: 3,
                content: 'Compilation error',
                correct: false
            }, {
                id: 4,
                content: 'Exception is thrown at runtime',
                correct: false
            }
        ],
        explanation: 'This code defines a simple Java program that demonstrates the concept of passing' +
                ' objects and modifying their properties.  The Book objects title property is cha' +
                'nged inside the bookRenamed() method, and this change is reflected when printing' +
                ' the title property in the main() method.'
    }, {
        id: 21,
        category: 4,
        isImage: false,
        title: "Which are valid array declarations?",
        image: task21,
        answers: [
            {
                id: 1,
                content: 'db1',
                correct: false
            }, {
                id: 2,
                content: 'db2',
                correct: true
            }, {
                id: 3,
                content: 'db3',
                correct: false
            }, {
                id: 4,
                content: 'db4',
                correct: true
            }
        ],
        explanation: 'd1 has a compilation error because it attempts to create an array without specif' +
                'ying dimensions. d3 has a compilation error because it specifies only the number' +
                ' of columns while leaving the number of rows unspecified which isnot allowed. d2' +
                ' and d4 are valid and create 2D arrays with specific dimensions.'
    }, {
        id: 22,
        category: 4,
        isImage: false,
        title: "What will be printed in console?",
        image: task22,
        answers: [
            {
                id: 1,
                content: '020',
                correct: true
            }, {
                id: 2,
                content: '1020',
                correct: false
            }, {
                id: 3,
                content: '01020',
                correct: false
            }, {
                id: 4,
                content: '102030',
                correct: false
            }
        ],
        explanation: 'arr1 = arr2; line assigns the reference of the array arr2 to the array variable ' +
                'arr1. Now, both arr1 and arr2 point to the same array object in memory, which co' +
                'ntains elements 10 and 20. arr2[0] = 0; line modifies the first element of the a' +
                'rray pointed to by arr2. Since arr1 and arr2 point to the same array, this chang' +
                'e affects both arrays. Therefore answer is 020'
    }, {
        id: 23,
        category: 2,
        isImage: false,
        title: "What will be printed in console?",
        image: task23,
        answers: [
            {
                id: 1,
                content: 'false false',
                correct: false
            }, {
                id: 2,
                content: 'false true',
                correct: false
            }, {
                id: 3,
                content: 'true false',
                correct: false
            }, {
                id: 4,
                content: 'true true',
                correct: true
            }
        ],
        explanation: 'When valueOf(null) is called, it returns the constant Boolean.FALSE because null' +
                ' is treated as false in this context. Same logic applies by calling valueOf(fals' +
                'e).  In this case, since both b1 and b2 are set to Boolean.FALSE, they reference' +
                ' the same object (which is a constant Boolean.FALSE object), so b1 == b2 evaluat' +
                'es to true. Since both b1 and b2 are set to Boolean.FALSE, they have the same bo' +
                'olean value, so b1.equals(b2) evaluates to true.'
    }, {
        id: 24,
        category: 2,
        isImage: false,
        title: "What will be printed in console?",
        image: task24,
        answers: [
            {
                id: 1,
                content: 's1 s2',
                correct: true
            }, {
                id: 2,
                content: 's2 s4',
                correct: false
            }, {
                id: 3,
                content: 's1 s2 s3',
                correct: false
            }, {
                id: 4,
                content: 's3 s4',
                correct: false
            }
        ],
        explanation: 's1 and s2 are valid because they are declared as 2D arrays and initialized corre' +
                'ctly; s3 is invalid because it is declared as a 1D array but initialized as 2D; ' +
                's4 is invalid for the same reason; {{null, null}}; is invalid because it is an o' +
                'rphaned expression with no assignment'
    }, {
        id: 25,
        category: 2,
        isImage: false,
        title: "Which variable/(s) have error?",
        image: task25,
        answers: [
            {
                id: 1,
                content: 'c1',
                correct: false
            }, {
                id: 2,
                content: 'c2',
                correct: true
            }, {
                id: 3,
                content: 'c3',
                correct: false
            }, {
                id: 4,
                content: 'c4',
                correct: false
            }, {
                id: 4,
                content: 'c5',
                correct: true
            }
        ],
        explanation: 'Range of values for char data type is: 0 to 65535. So c2 and c5 are our answers'
    }, {
        id: 26,
        category: 2,
        isImage: false,
        title: "What will be printed in console?",
        image: task26,
        answers: [
            {
                id: 1,
                content: 'intVal = 0',
                correct: true
            }, {
                id: 2,
                content: 'strVal = null',
                correct: true
            }, {
                id: 3,
                content: 'chVal = ',
                correct: true
            }, {
                id: 4,
                content: 'dblVal = 0',
                correct: false
            }, {
                id: 5,
                content: 'dblVal = 0.0',
                correct: true
            }
        ],
        explanation: 'Default value for int is 0, String variable is null, double = 0.0 and char repre' +
                'sents the "empty" character.'
    }, {
        id: 27,
        category: 2,
        isImage: false,
        title: 'Which of these represents the number of elements in array?',
        answers: [
            {
                id: 1,
                content: 'arr.size',
                correct: false
            }, {
                id: 2,
                content: 'arr.size()',
                correct: false
            }, {
                id: 3,
                content: 'arr.length',
                correct: true
            }, {
                id: 4,
                content: 'arr.length()',
                correct: false
            }
        ],
        explanation: 'n Java, arr.size, arr.size(), and arr.length() are invalid because arrays use le' +
                'ngth as a field (not a method), while size() is a method for collections like Ar' +
                'rayList, and length() is a method for String, making them incompatible with arra' +
                'ys. Only with length variable, we can find number of elements'
    }, {
        id: 28,
        category: 2,
        isImage: false,
        title: "What will be printed in console?",
        image: task28,
        answers: [
            {
                id: 1,
                content: '5 / 5',
                correct: false
            }, {
                id: 2,
                content: '10 / 10',
                correct: false
            }, {
                id: 3,
                content: '10 / 20',
                correct: true
            }, {
                id: 4,
                content: '20 / 20',
                correct: false
            }, {
                id: 5,
                content: 'Compilation error',
                correct: false
            }
        ],
        explanation: 'When you print q1.val, you get 10, and when you print q1.statVal, you get 20, wh' +
                'ich leads to the output "10 / 20". This behavior occurs because the statVal stat' +
                'ic variable is shared among all instances of the class, while each instance has ' +
                'its own independent val instance variable.'
    }, {
        id: 29,
        category: 2,
        isImage: false,
        title: "What will be printed in console?",
        image: task29,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '2',
                correct: false
            }, {
                id: 3,
                content: '3',
                correct: true
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }
        ],
        explanation: 'Since arr[2] is not explicitly assigned a value, it takes on the default value f' +
                'or integers, which is 0. Therefore, the sum arr[0] + arr[1] + arr[2] becomes 1 +' +
                ' 2 + 0, which equals 3.'
    }, {
        id: 30,
        category: 4,
        isImage: false,
        title: "What will be printed in console?",
        image: task30,
        answers: [
            {
                id: 1,
                content: "First Fruit: Apple\nContains Banana: true",
                correct: false
            }, {
                id: 2,
                content: "First Fruit: Apple\nContains Banana: false",
                correct: false
            }, {
                id: 3,
                content: "First Fruit: Orange\nContains Banana: true",
                correct: true
            }, {
                id: 4,
                content: "First Fruit: Orange\nContains Banana: false",
                correct: false
            }, {
                id: 5,
                content: "Compilation Error",
                correct: false
            }
        ],
        explanation: `The code creates an ArrayList of fruits, adds "Apple", "Orange", and "Banana", stores "Apple" in firstFruit, removes "Apple", checks if "Banana" exists (which is true), and prints "First Fruit: Apple" and "Contains Banana: true".`
    }, {
        id: 31,
        category: 8,
        isImage: false,
        title: "What will be printed in console?",
        image: task31,
        answers: [
            {
                id: 1,
                content: "Result: 0\nException: Division by zero",
                correct: false
            }, {
                id: 2,
                content: "Result: Infinity\nException: null",
                correct: false
            }, {
                id: 3,
                content: "Compilation Error",
                correct: false
            }, {
                id: 4,
                content: "Exception: Division by zero",
                correct: true
            }, {
                id: 5,
                content: "Result: Infinity\nException: Division by zero",
                correct: false
            }
        ],
        explanation: "In Java, integer division by zero (/) results in an ArithmeticException. Since t" +
                "he exception is caught in the catch block, the program does not crash and instea" +
                "d prints the error message. The result isn't printed because an exception occurs before System.out.println('Result: ' + result); is executed. "
    }, {
        id: 32,
        category: 10,
        isImage: false,
        title: "What is the output of the following Java code?",
        image: task32,
        answers: [
            {
                id: 1,
                content: "Number of month: 0",
                correct: false
            }, {
                id: 2,
                content: "Number of month: 1",
                correct: false
            }, {
                id: 3,
                content: "Number of month: 2",
                correct: false
            }, {
                id: 4,
                content: "Compilation Error",
                correct: true
            }, {
                id: 5,
                content: "Runtime Error",
                correct: false
            }
        ],
        explanation: "The code you provided has a compilation error because the constructor LocalDate(" +
                "int year, Month month, int dayOfMonth) is not defined in the LocalDate class in " +
                "Java. To create a LocalDate object, you should use the of() method."
    }
];