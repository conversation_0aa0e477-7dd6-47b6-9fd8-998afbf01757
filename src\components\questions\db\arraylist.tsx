import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { javaCode196, javaCode198, javaCode199, javaCode200, javaCode201, javaCode202 } from "../exam/snips/basicSnips";

export const arraylists : Questions[] = [
    {
        id: 196,
        category: 4,
        isImage: false,
        title: "Which line of code is valid?",
        image: javaCode196,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '2',
                correct: false
            }, {
                id: 3,
                content: '3',
                correct: true
            }, {
                id: 4,
                content: '4',
                correct: false
            }, {
                id: 5,
                content: '5',
                correct: false
            }, {
                id: 6,
                content: '6',
                correct: false
            }
        ],
        explanation: 'Arraylists have only 1 method which is valid -> .size() .\n \n.length() -> retur' +
                'ns length of a String.\n \n.length field returns length of an array.\n \nCapacit' +
                'ies are not part of ArrayList or Arrays (they are used in StringBuffer).\n \n Ou' +
                'r answer is -> 3'
    }, {
        id: 197,
        category: 4,
        isImage: false,
        title: 'Which is/are false?',
        answers: [
            {
                id: 1,
                content: 'Arraylist has fix size',
                correct: true
            }, {
                id: 2,
                content: 'Array has fix size',
                correct: false
            }, {
                id: 3,
                content: 'Arrays can have multiple dimensions',
                correct: false
            }, {
                id: 4,
                content: 'Arraylist is immutable',
                correct: true
            }, {
                id: 5,
                content: 'Array is immutable',
                correct: true
            }
        ],
        explanation: 'Arrays are fixed with size, this size is provided during declaration process.\nA' +
                'rraylist can be extended at any time during execution, without causing any error' +
                's.\n \nArrays are multidimensional.\n \nArrays and arraylist are mutable, since ' +
                'the value of their elements can be changed.'
    }, {
        id: 198,
        category: 4,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode198,
        answers: [
            {
                id: 1,
                content: 'false / false / true / true',
                correct: false
            }, {
                id: 2,
                content: 'false / true / false / true',
                correct: false
            }, {
                id: 3,
                content: 'false / false / false / false',
                correct: false
            }, {
                id: 4,
                content: 'false / false / false / true',
                correct: true
            }, {
                id: 5,
                content: 'true / true / true / true',
                correct: false
            }
        ],
        explanation: 'boolean b1 = a == b; This compares the references of the arrays a and b. Since a' +
                ' and b are distinct array objects, even though their content is the same, leadin' +
                'g to the false value.\n \nboolean b2 = a.equals(b); This comparison attempts to ' +
                'use the equals() method of the array, but arrays in Java do not have an overridd' +
                'en equals() method that compares their content. Instead, the default equals() me' +
                'thod inherited from the Object class is used, which checks for reference equalit' +
                'y (similar to ==). So, this comparison will also be false.\n \nboolean b3 = arr1' +
                ' == arr2; This compares the references of the ArrayList objects arr1 and arr2. S' +
                'ince they are distinct objects, this comparison will be false.\n \nboolean b4 = ' +
                'arr1.equals(arr2); This comparison uses the equals() method of ArrayList, which ' +
                'compares the content of the lists. In this case, the content of arr1 and arr2 is' +
                ' the same (both contain 1 and 2), so this comparison will be true.\n \nThe outpu' +
                't of the code will be: false / false / false / true'
    }, {
        id: 199,
        category: 4,
        isImage: false,
        title: "What will be printed in console?",
        image:javaCode199,
        answers: [
            {
                id: 1,
                content: '121',
                correct: false
            }, {
                id: 2,
                content: '12',
                correct: false
            }, {
                id: 3,
                content: '12 then an exception',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: true
            }
        ],
        explanation: 'The provided code snippet has a compilation error because it is trying to add a ' +
                'String to an ArrayList<Integer>, which is not type-compatible.\n \nThe ArrayList' +
                '<Integer> is supposed to hold only Integer values, and adding a String violates ' +
                'this type constraint.'
    }, {
        id: 200,
        category: 4,
        isImage: false,
        title: "What elements will be printed in the console?",
        image: javaCode200,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '2',
                correct: true
            }, {
                id: 3,
                content: '3',
                correct: false
            }, {
                id: 4,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'This code snippet is tricky since it is hard to define if the code is removing o' +
                'bject or removing by index.\n \nInitially, the arr1 ArrayList is populated with ' +
                'three elements: [1, 2, 3].\n \narr1.remove(0); removes the element at index 0, w' +
                'hich is 1. After this step, the ArrayList becomes [2, 3].\n \narr1.remove(1); re' +
                'moves the element at index 1, which is now 3 (since we previously removed the or' +
                'iginal element at index 0). After this step, the ArrayList becomes [2].\n \nSo, ' +
                'after removing the elements, the ArrayList arr1 only contains the value 2. \n \n' +
                'It is also important to note that remove method here takes parameter as index, b' +
                'ut if we want to remove specific object, we should type .remove(new Integer(3)),' +
                ' only then it will remove object 3.'
    }, {
        id: 201,
        category: 4,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode201,
        answers: [
            {
                id: 1,
                content: 'true / true',
                correct: false
            }, {
                id: 2,
                content: 'false / true',
                correct: false
            }, {
                id: 3,
                content: 'false / false',
                correct: false
            }, {
                id: 4,
                content: 'true / false',
                correct: false
            }, {
                id: 5,
                content: 'Compilation error',
                correct: true
            }
        ],
        explanation: 'This code snippet throws compilation error because we are trying to compare (arr1 == list1) inco' +
                'mpatible types.'
    }, {
        id: 202,
        category: 4,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode202,
        answers: [
            {
                id: 1,
                content: '0',
                correct: false
            }, {
                id: 2,
                content: '1',
                correct: true
            }, {
                id: 3,
                content: '2',
                correct: false
            }, {
                id: 4,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'Initially, the list contains the elements [0, 1, 2, 3].\nlist1.set(1, 0); sets th' +
                'e element at index 1 (which is 1) to 0, so the list becomes [0, 0, 2, 3].\n \nlist1.' +
                'set(0, 1); sets the element at index 0 (which is now 0 due to the previous set o' +
                'peration) to 1, so the list becomes [1, 0, 2, 3].\n \nSystem.out.println(list1.get(0' +
                ')); prints the element at index 0, which is 1.'
    }
]