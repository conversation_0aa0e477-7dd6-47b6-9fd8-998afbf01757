import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { javaCode218, javaCode219, javaCode220 } from "../exam/snips/basicSnips";
export const timeDB : Questions[] = [
    {
        id: 218,
        category: 10,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode218,
        answers: [
            {
                id: 1,
                content: '28 / FEBRUARY / 2015',
                correct: false
            }, {
                id: 2,
                content: '4 / MARCH / 2015',
                correct: false
            }, {
                id: 3,
                content: '4 / FEBRUARY / 2015',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }, {
                id: 5,
                content: 'Runtime exception is thrown',
                correct: true
            }
        ],
        explanation: 'February contains only 28 days, not 32.\nTherefore this code throws runtime exce' +
                'ption'
    }, {
        id: 219,
        category: 10,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode219,
        answers: [
            {
                id: 1,
                content: '10 / FEBRUARY / 2015',
                correct: false
            }, {
                id: 2,
                content: '17 / FEBRUARY / 2016',
                correct: false
            }, {
                id: 3,
                content: '17 / FEBRUARY / 2015',
                correct: true
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }, {
                id: 5,
                content: 'Runtime exception is thrown',
                correct: false
            }
        ],
        explanation: 'localDate.plusDays(7) adds 7 days to the date, resulting in February 17, 2015.\n \nl' +
                'ocalDate.plusYears(1), which returns a new LocalDate instance with 1 year added,' +
                ' but not assign it to any variable.\nThe original localDate remains unchanged. Th' +
                'erefore our final output will be 17 / FEBRUARY / 2015'
    }, {
        id: 220,
        category: 10,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode220,
        answers: [
            {
                id: 1,
                content: '3:22 AM',
                correct: false
            }, {
                id: 2,
                content: '3:22:35 AM',
                correct: false
            }, {
                id: 3,
                content: 'Feb 3, 2001, 3:22:35 AM',
                correct: false
            }, {
                id: 4,
                content: 'February 3, 2001',
                correct: true
            }, {
                id: 5,
                content: '2/3/01, 3:22 AM',
                correct: false
            }
        ],
        explanation: 'You create a LocalDateTime instance with the date and time 2000-01-02T03:22:35.\n \n' +
                'You create a Period instance p1 representing a period of 1 year, 1 month, and 1 ' +
                'day. You add the period p1 to the dateTime instance using dateTime.plus(p1).\nThi' +
                's changes the dateTime value to 2001-02-03T03:22:35. You call dateTime.minus(p1)' +
                '. However, the minus method doesnot modify the original dateTime instance.\nIt re' +
                'turns a new LocalDateTime instance with the specified period subtracted. But you' +
                ' are not assigning this new value back to dateTime, so dateTime still holds the ' +
                'value 2001-02-03T03:22:35.\nYou create a DateTimeFormatter using the ofLocalizedD' +
                'ate method with FormatStyle.LONG. You format the dateTime instance using the spe' +
                'cified formatter.\n \nThis results in the output February 3, 2001.'
    }
];