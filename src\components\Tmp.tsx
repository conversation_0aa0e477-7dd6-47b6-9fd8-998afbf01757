import React, {Component } from 'react';
import {
    Text,
    View,
    StyleSheet,
    ScrollView,
    ActivityIndicator,
    } from 'react-native';
import {AppBar, IconButton} from "@react-native-material/core";
import BouncyCheckbox from "react-native-bouncy-checkbox";
import {Divider} from "@react-native-material/core";
import { Feather, Octicons} from "@expo/vector-icons";

import { generateRandomCode } from '../service/RandomService';

import { Answer, Questions } from './models/Questions';
import { QuestionService } from '../service/QuestionService';

import { getCategory } from './models/Categories';

import { appColors } from '../utils/appColors';
import CodeEditor, { CodeEditorSyntaxStyles } from '@rivascva/react-native-code-editor';
import { scale } from 'react-native-size-matters';


interface QuestionState {

    currentQuestion:Questions,
    marked:boolean,
    fullScreenImage:boolean;
    isModalVisible:boolean;
    adModalVisible:boolean;
    codeFontSize:number;
}





class Tmp extends Component < {}, QuestionState > {
    constructor(props : {}) {
        super(props);
        this.state = {
            currentQuestion: null,
            marked:false,
            fullScreenImage:false,
            isModalVisible:false,
            adModalVisible:false,
            codeFontSize:scale(12),
        }
    }


    componentDidMount() {
        this.loadQuestion = this.loadQuestion.bind(this)
        this.loadQuestion();
        this.props.navigation.addListener('focus', this.loadQuestion);
    }


    async loadQuestion() { 
        this.setState({currentQuestion:QuestionService.getQuestionById(38,0)});
    }

  

    adIsLoading = () => {
        this.setState({adModalVisible:true});
    }

    closeAdModal = () => {
        this.setState({adModalVisible:false})
    }


    clickIncreaseFontSize = () => {
        let {codeFontSize} = this.state;
        this.setState({codeFontSize:codeFontSize + 1});
    }

    clickDecreaseFontSize = () => {
        let {codeFontSize} = this.state;
        this.setState({codeFontSize:codeFontSize - 1});
    }




    addAnswer = (id: number, isChecked:boolean) => {

    }

    isSelectedAnswer = (id: number) => {
        
    }



    renderItem = (answer : Answer) => {
        return (
            <React.Fragment key={generateRandomCode()}> 
            <Divider style = {{ marginVertical: 15 }}/>
            <BouncyCheckbox
                key={answer.id}
                size={25}
                textContainerStyle={{minWidth:'10%', maxWidth:'90%'}}
                fillColor={appColors.blue}
                unfillColor="#FFFFFF"
                text={answer.content}
                iconStyle={{borderColor: "black"}}
                innerIconStyle={{borderWidth: 2}}
                isChecked={this.isSelectedAnswer(answer.id)}
                onPress={(isChecked : boolean) => this.addAnswer(answer.id, isChecked)}
                textStyle={{
                textDecorationLine: "none",
                color: (answer.correct ? appColors.white : appColors.black),
                fontFamily: 'LatoRegular',
                fontSize:16,
                backgroundColor: (answer.correct ?  appColors.blue : null)
                }}
                /> 
            < Divider style = {{ marginVertical: 15 }}/>
        </React.Fragment>
         );
    };

    renderImage(reactComponent: any){
        return reactComponent;
    }

    

    render() {
        const {currentQuestion, codeFontSize} = this.state;    
        if (currentQuestion === undefined || currentQuestion === null) {
            return   <ActivityIndicator size="large" color="#215e9d" />
        }
        var title ="#"+currentQuestion.id +" "+getCategory(currentQuestion.category).title;
        return (
            <React.Fragment>
                <AppBar
                    contentContainerStyle={{ marginTop:scale(35) }}
                    title={title}
                    centerTitle={true}
                    color="#215e9d"
                    titleStyle={{fontFamily:'monospace'}}
                    leading={() => (
                        <IconButton
                        icon={() => (
                          <Octicons name="codescan" color={"white"} size={30} />
                        )}
                      />
                    )}
                
                />
            <ScrollView contentContainerStyle={{flexGrow: 1}}>
                <View style={styles.header} key={generateRandomCode()}>
                <View style={styles.topBar}>
                        <View>
                            <Text key={generateRandomCode()} style={styles.subText}>#{currentQuestion.id} {currentQuestion.title}</Text>
                            <Text style={styles.multiple}>Multiple answers are allowed</Text>
                            <Divider style = {{ marginVertical: 5, backgroundColor:'black' }}/>
                            
                                <View style={styles.columnOne}>
                                    <View style={styles.iconRow}>
                                        <IconButton 
                                            icon={props => <Feather name="zoom-in" size={40} color="black" />} 
                                            onPress={ () => this.clickIncreaseFontSize()}
                                        />
                                        <IconButton 
                                            icon={props => <Feather name="zoom-out" size={40} color="black" />} 
                                            onPress={ () => this.clickDecreaseFontSize()}
                                        />
                                    </View>
                                    <ScrollView>
                                        <CodeEditor
                                            style={{
                                                fontSize: codeFontSize,
                                            }}
                                            readOnly={true}
                                            initialValue={String(currentQuestion.image)}
                                            language="java"
                                          syntaxStyle={CodeEditorSyntaxStyles.googlecode}
                                        />
                                    </ScrollView>
                                </View>                                
                            
                            
                        </View>
                </View>
                <View style={styles.bottomBar}>
                    {currentQuestion.answers.map(this.renderItem)}
                </View>

                        <View style={styles.explanationBar}>
                            <Text style={styles.explanationText}>{currentQuestion.explanation}</Text>
                        </View>
                </View>
            </ScrollView>
            </React.Fragment>
   
        );
    }
}

const styles = StyleSheet.create({
    fullScreen:{ 
        flexDirection: 'row', 
        alignItems: 'center',
        height:'100%', 
        width:'100%' 
    },
    columnOne:{
        flexDirection:'column',
    },
    iconRow:{
        flexDirection:'row',
        justifyContent:'space-evenly',
    },
    codeStyle:{
        fontFamily:'MonocodeRegular',
    },
    rightIcon:{
        alignSelf:'flex-end',
    },

    loadedImage:{

    },

    header: {
        flex: 1,
        alignItems: 'center',
        backgroundColor: 'white',
        paddingHorizontal: scale(10),
    },
    topBar: {
        width: '100%',
        flexDirection: 'column',
    },
    appBarMenuStyle:{
        position:'absolute',
        alignSelf: "center", 
        flexDirection:'row', 
        width:"50%",
        justifyContent:'space-evenly'
    },
    explanationBar:{
        width: '95%',
        flex: 1,
    },
    explanationText:{
        color: 'black',
        fontSize: 15,
        fontFamily: "LatoLight",
        textAlign:'justify'
    },
    bottomBar: {
        marginTop: scale(10),
        flexDirection: 'column',
        padding: 5,
        width: '95%',
        flex: 1
    },
    subText: {
        marginTop: '3%',
        color: 'black',
        fontSize: 17,
        fontWeight:'bold',
        fontFamily: "LatoRegular"
    },
    multiple:{
        color: '#215E9D',
        fontSize: 12,
        fontFamily: 'LatoLight'
    },
    buttonBar2: {
        width: "100%",
        flexDirection: "row",
        justifyContent: "space-around",
        backgroundColor: 'white',
        paddingVertical: scale(15),
        maxHeight: scale(60),
        flex: 1,
    },
    buttonStyle: {
        backgroundColor: appColors.blue,
    },
    buttonTitle:{
        fontFamily:'LatoLight'
    },

    ads:{
        maxHeight:'5%',
        width:'100%',
    },
});

export default Tmp;
