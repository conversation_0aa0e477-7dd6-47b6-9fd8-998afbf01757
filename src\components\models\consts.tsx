import { Feather, MaterialIcons, MaterialCommunityIcons ,FontAwesome} from '@expo/vector-icons';
import {mindMazeExam, codeMingleExam, codeQuestExam, byteWizExam, javaZen} from '../questions/allQuestions';
import { ExamHead, ExamBody, TypeOfExam } from './Exams';
import { QuestionService } from '../../service/QuestionService';
import { scale } from 'react-native-size-matters';



export const examHead : ExamHead[] = [
    {
        id: 1,
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Exam,
    }, {
        id: 2,
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Exam,
    }, {
        id: 3,
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Exam,
    }, {
        id: 4,
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Exam,
    }, {
        id: 5,
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Exam,
    }, {
        id: 6,
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Exam,
    }, {
        id: 7,
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Exam,
    },    
    {
        id: 10, 
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Chapter,
    },
    {
        id: 11, 
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Chapter,
    },
    {
        id: 12, 
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Chapter,
    },
    {
        id: 13, 
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Chapter,
    },
    {
        id: 14, 
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Chapter,
    },
    {
        id: 15, 
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Chapter,
    },
    {
        id: 16, 
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Chapter,
    },
    {
        id: 17, 
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Chapter,
    },
    {
        id: 18, 
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Chapter,
    },
    {
        id: 19, 
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Chapter,
    },    
    {
        id: 100, 
        completed: false,
        ongoing: false,
        typeOfExam: TypeOfExam.Random,
    }
];




export var mindMaze:ExamBody = {
    name: 'Mind Maze',
    descr: mindMazeExam.length + ' questions',// '65 questions'
    icon: <MaterialCommunityIcons name="approximately-equal" size={scale(30)} color="black" />,
    enabled: true
};

export var codeMingle:ExamBody = {
    name: 'Code Mingle',
    descr: codeMingleExam.length + ' questions',// '65 questions'
    icon: <MaterialCommunityIcons name="arch" size={scale(30)} color="black" />,
    enabled: true
};

export var codeQuest:ExamBody = {
    name: 'Code Quest',
    descr: codeQuestExam.length + ' questions',// '65 questions'
    icon: <MaterialCommunityIcons name="arm-flex" size={scale(30)} color="black" />,
    enabled: true
};

export var byteWiz:ExamBody = {
    name: 'Byte Wiz',
    descr: byteWizExam.length + ' questions',
    icon: <Feather name='cloud-lightning' color={'black'} size={scale(30)}/>,
    enabled: true
};

export var javazZen:ExamBody = {
    name: 'Java Zen',
    descr: javaZen.length + ' questions',
    icon: <Feather name='book-open' color={'black'} size={scale(30)}/>,
    enabled: true
};

export var cognitionCarnival:ExamBody = {
    name: 'Code Guru',
    descr: 'Coming soon',
    icon: <MaterialIcons name='blur-circular' color={'black'} size={scale(30)}/>,
    enabled: false
};

export var acumenOdyssey:ExamBody = {
    name: 'Skill Java',
    descr: 'Coming soon',
    icon: <Feather name='compass' color={'black'} size={scale(30)}/>,
    enabled: false
};



export var basics:ExamBody = {
    name:"Basics",
    descr: QuestionService.getQuestionsByCategory(1).length + ' questions',
    icon:<MaterialCommunityIcons name='beaker' color={'black'}  size={scale(30)} />,
    enabled: true
};

export var dataTypes:ExamBody = {
    name:"Data Types",
    descr: QuestionService.getQuestionsByCategory(2).length + ' questions',
    icon:<FontAwesome name='address-book' color={'black'}  size={scale(30)} />,
    enabled: true
};

export var operators:ExamBody = {
    name:"Operators",
    descr: QuestionService.getQuestionsByCategory(3).length + ' questions',
    icon:<MaterialCommunityIcons name='ab-testing' color={'black'}  size={scale(30)} />,
    enabled: true
};

export var arrays:ExamBody = {
    name:"Arrays/Lists",
    descr: QuestionService.getQuestionsByCategory(4).length + ' questions',
    icon:<FontAwesome name='list' color={'black'}  size={scale(30)} />,
    enabled: true
};

export var loops:ExamBody = {
    name:"Loops",
    descr: QuestionService.getQuestionsByCategory(5).length + ' questions',
    icon:<MaterialCommunityIcons name='circle-slice-1' color={'black'}  size={scale(30)} />,
    enabled: true
};

export var constructors:ExamBody = {
    name:"Methods",
    descr: QuestionService.getQuestionsByCategory(6).length + ' questions',
    icon:<MaterialCommunityIcons name='waveform' color={'black'}  size={scale(30)} />,
    enabled: true 
};

export var inheritance:ExamBody = {
    name:"Inheritance",
    descr: QuestionService.getQuestionsByCategory(7).length + ' questions',
    icon:<MaterialCommunityIcons name='account-supervisor' color={'black'}  size={scale(30)} />,
    enabled: true
};

export var exceptions:ExamBody = {
    name:"Exceptions",
    descr: QuestionService.getQuestionsByCategory(8).length + ' questions',
    icon:<MaterialCommunityIcons name='sprinkler' color={'black'}  size={scale(30)} />,
    enabled: true
};

export var lambda:ExamBody = {
    name:"Lambda",
    descr: QuestionService.getQuestionsByCategory(9).length + ' questions',
    icon:<FontAwesome name='lastfm' color={'black'}  size={scale(30)} />,
    enabled: true
};

export var time:ExamBody = {
    name:"Date/Time",
    descr: QuestionService.getQuestionsByCategory(10).length + ' questions',
    icon:<MaterialCommunityIcons name='calendar-multiselect' color={'black'}  size={scale(30)} />,
    enabled: true
};

export var randomQuestion:ExamBody = {
    name:"Random",
    descr: "Randomly selected questions",
    icon:<MaterialCommunityIcons name='shuffle' color={'black'}  size={scale(30)} />,
    enabled:true
};


export var generalExam = new Map<number, ExamBody>([
    [1, mindMaze],
    [2, codeMingle],
    [3, codeQuest],
    [4, byteWiz],
    [5, javazZen],
    [6, cognitionCarnival],
    [7, acumenOdyssey],
    [10, basics],
    [11, dataTypes],
    [12, operators],
    [13, arrays],
    [14, loops],
    [15, constructors],
    [16, inheritance],
    [17, exceptions],
    [18, lambda],
    [19, time],
    [100, randomQuestion],
]);