export const basic:string[] = [
    'Define the scope of variables',
    'Define the structure of a Java class',
    'Create executable Java applications with a main method; run a Java program from the command line; produce console output',
    'Import other Java packages to make them accessible in your code',
    'Compare and contrast the features and components of Java such as: platform independence, object orientation, encapsulation, etc.',
    'Missing package and import statements',
    'Unintended line breaks'
];
export const dataTypes:string[] = [
    'Declare and initialize variables',
    'Differentiate between object reference variables and primitive variables',
    'Experience with wrapper classes such as Boolean, Double, and Integer',
    'Create and manipulate Strings',
    'Manipulate data using the StringBuilder class and its methods',
    'Declare and use an ArrayList of a given type'
];
export const operators:string[] = [
    'Use Java operators; use parentheses to override operator precedence',
    'Test equality between Strings and other objects using == and equals ()',
    'Create if and if/else and ternary constructs',
    'Use a switch statement'
];
export const arrays:string[] = [
    'Declare, instantiate, initialize and use a one-dimensional array',
    'Declare, instantiate, initialize and use multi-dimensional arrays'
];
export const loops:string[] = [
    'Create and use while loops',
    'Create and use for loops including the enhanced for loop',
    'Create and use do/while loops',
    'Compare loop constructs',
    'Use break and continue'
];
export const constructors:string[] = [
    'Create methods with arguments and return values; including overloaded methods',
    'Apply the static keyword to methods and fields',
    'Create and overload constructors; differentiate between default and user defined constructors',
    'Apply access modifiers',
    'Apply encapsulation principles to a class',
    'Determine the effect upon object references and primitive values when they are passed into methods that change the values'
];
export const inheritance:string[] = [
    'Describe inheritance and its benefits',
    'Develop code that makes use of polymorphism; develop code that overrides methods; differentiate between the type of a reference and the type of an object',
    'Casting and its necessity',
    'super and this to access objects and constructors',
    'Abstract classes and interfaces'
];
export const exceptions:string[] = [
    'Differentiate among checked exceptions, unchecked exceptions, and Errors',
    'Create a try-catch block and determine how exceptions alter normal program flow',
    'Create and invoke a method that throws an exception',
    'Recognize common exception classes (such as NullPointerException, ArithmeticException, ArrayIndexOutOfBoundsException, ClassCastException)'
];
export const lambda:string[] = [
    'Write a simple Lambda expression that consumes a Lambda Predicate expression'
];
export const dateTime:string[] = [
    'Create and manipulate calendar data using classes from java.time.LocalDateTime, java.time.LocalDate, java.time.LocalTime, java.time.format.DateTimeFormatter, java.time.Period'
];
