import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import {
    task1015,
    task1016,
    task1017,
    task1018,
    task1019,
    task1020,
    task1021
} from "./snippets/code";
import {javaCode1022, javaCode1023, javaCode1024, javaCode1025, javaCode1027, javaCode1028, javaCode1029, javaCode1030, javaCode1031} from "./snippets/codeSnip";

//taken from revision
export const operatorsDB : Questions[] = [
    {
        id: 1015,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task1015,
        answers: [
            {
                id: 1,
                content: '-2',
                correct: false
            }, {
                id: 2,
                content: '-1',
                correct: true
            }, {
                id: 3,
                content: '0',
                correct: false
            }, {
                id: 4,
                content: '1',
                correct: false
            }, {
                id: 5,
                content: '2',
                correct: false
            }
        ],
        explanation: 'q is an array of size 10, and p is set to 1.\n \nr is assigned the value at inde' +
                'x 1 in the q array. Since the array is newly initialized, all elements are 0, so' +
                ' r becomes 0.\n \ns is calculated as the difference between r and p, which is 0 ' +
                '- 1 = -1.\n \nThe value of s is then assigned to r, so r becomes -1. The output ' +
                'of the println statement is the value of r, which is -1.'
    }, {
        id: 1016,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task1016,

        answers: [
            {
                id: 1,
                content: 'Running A',
                correct: false
            }, {
                id: 2,
                content: 'Running B',
                correct: false
            }, {
                id: 3,
                content: '0',
                correct: false
            }, {
                id: 4,
                content: 'null',
                correct: false
            }, {
                id: 5,
                content: 'Compilation error',
                correct: true
            }, {
                id: 5,
                content: 'Exception will be thrown during runtime',
                correct: false
            }
        ],
        explanation: 'Given code has a compilation error because the ternary conditional operator (? :' +
                ') requires both of its branches to have compatible types.\n \nIn your case, the ' +
                'two branches have different types: int for runA() and void for runB(). The terna' +
                'ry operator is used to choose between the return values of runA() and runB(). Ho' +
                'wever, the return type of runB() is void, which means it doesnot return a value,' +
                ' whereas runA() returns an int.\n \nThis mismatch in return types causes a compi' +
                'lation error.'
    }, {
        id: 1017,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task1017,
        answers: [
            {
                id: 1,
                content: '0 arguments = 9',
                correct: false
            }, {
                id: 2,
                content: '0 arguments = 10',
                correct: true
            }, {
                id: 3,
                content: '1 argument = 10',
                correct: true
            }, {
                id: 4,
                content: '1 argument = 11',
                correct: false
            }, {
                id: 5,
                content: 'Compilation error',
                correct: false
            }, {
                id: 5,
                content: 'Exception will be thrown during runtime',
                correct: false
            }
        ],
        explanation: 'Case 1: args.length = 0, increment(args.length) is called. Since args.length is ' +
                '0, the first branch of the increment method is taken, and it returns 1. Amount b' +
                'ecomes 1. amount += 7 + ++amount; can be broken down as follows: 7 + ++amount be' +
                'comes 7 + (amount + 1). amount is 1 at this point, so the expression becomes 7 +' +
                ' 2. amount += 7 + 2; increments amount by 9, so amount becomes 10.\n \nFinally, ' +
                'System.out.println(amount); prints 10.\n \nCase 2: args.length = 1. In this case' +
                ', args.length is 1. increment(args.length) is called. Since args.length is 1, th' +
                'e second branch of the increment method is taken. i++ returns the current value ' +
                'of i and then increments it, so it returns 1. amount becomes 1. amount += 7 + ++' +
                'amount; can be broken down as follows: 7 + ++amount becomes 7 + (amount + 1). am' +
                'ount is 1 at this point, so the expression becomes 7 + 2. amount += 7 + 2; incre' +
                'ments amount by 9, so amount becomes 10.\n \nFinally, System.out.println(amount)' +
                '; prints 10. In both cases, the value of amount becomes 10 and the output is 10.' +
                ' The behavior is the same regardless of whether args.length is 0 or 1.'
    }, {
        id: 1018,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task1018,
        answers: [
            {
                id: 1,
                content: 'Not evaluated',
                correct: false
            }, {
                id: 2,
                content: 'Good',
                correct: true
            }, {
                id: 3,
                content: 'Very Good',
                correct: true
            }, {
                id: 4,
                content: 'Best',
                correct: false
            }, {
                id: 5,
                content: 'Compilation error',
                correct: false
            }, {
                id: 5,
                content: 'Exception will be thrown during runtime',
                correct: false
            }
        ],
        explanation: 'tc.evaluateGrade("B"); is called.\n \nThe input is "B", so the code enters the "' +
                'B" case. It prints "Good".\n \nSince there is no break after "Good", the executi' +
                'on continues to the next case. It prints "Very Good" as well because the executi' +
                'on falls through to the next case.\n \nThe break statement after "B+": is encoun' +
                'tered, and the switch block ends. '
    }, {
        id: 1019,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task1019,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '2',
                correct: true
            }, {
                id: 3,
                content: '3',
                correct: false
            }, {
                id: 5,
                content: 'Compilation error',
                correct: false
            }, {
                id: 5,
                content: 'Exception will be thrown during runtime',
                correct: false
            }
        ],
        explanation: 'In the given code, there is a method called crazyIf that takes a boolean value a' +
                's input.\n \nInside this method, there are two nested if statements. The tricky ' +
                'part is that instead of checking if the input value is true or false, the code u' +
                'ses the = sign, which is usually used for assignments.\n \nSo, the code actually' +
                ' changes the value of the input variable inside the if conditions. As a result, ' +
                'when you call crazyIf(true), it prints "2" because the value is changed to false' +
                ' inside the conditions.\n \nAnd when you call crazyIf(false), it again prints "2' +
                '" because the value is changed to true in the same way.'
    }, {
        id: 1020,
        category: 3,
        isImage: false,
        title: "What is the value of the total?",
        image: task1020,

        answers: [
            {
                id: 1,
                content: '3',
                correct: false
            }, {
                id: 2,
                content: '4',
                correct: false
            }, {
                id: 3,
                content: '5',
                correct: false
            }, {
                id: 4,
                content: '6',
                correct: true
            }, {
                id: 5,
                content: '7',
                correct: false
            }
        ],
        explanation: 'For res1, the calculation 2 * 3 is performed first, resulting in 6.\n \nThen, 6 ' +
                '/ 2 is calculated, which is 3.\n \nFinally, 1 + 3 is evaluated, giving the value' +
                ' 4 for res1. For res2, the calculation 2 * 3 is performed, resulting in 6.\n \nT' +
                'hen, 6 % 4 (the remainder of 6 divided by 4) is calculated, which is 2.\n \nThe ' +
                'total is calculated by adding res1 (which is 4) and res2 (which is 2), resulting' +
                ' in 6.'
    }, {
        id: 1021,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task1021,
        answers: [
            {
                id: 1,
                content: 'Nothing is printed',
                correct: true
            }, {
                id: 2,
                content: 'True',
                correct: false
            }, {
                id: 3,
                content: 'False',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error because of syntax in the if statements are not correct',
                correct: false
            }, {
                id: 5,
                content: 'Compilation error because of value in brackets',
                correct: false
            }
        ],
        explanation: 'It is important to note that this code is completely valid, it is nested if cond' +
                'itions. if(){ if(){} else{} }.\n \nSince first if is assigned to false(not compa' +
                'ring,but assigning) and therefore we are not entering inside of the body and the' +
                'refore do not writing anything.\n \nThe same logic applies when bool is passed e' +
                'ither true or false.'
    }, {
        id: 1022,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode1022,
        answers: [
            {
                id: 1,
                content: 'crazySwitch(2) -> Nothing is printed',
                correct: false
            }, {
                id: 2,
                content: 'crazySwitch(2) -> def',
                correct: false
            }, {
                id: 3,
                content: 'crazySwitch(2) -> best',
                correct: true
            }, {
                id: 4,
                content: 'crazySwitch(100) -> def',
                correct: false
            }, {
                id: 5,
                content: 'crazySwitch(100) -> best',
                correct: true
            }
        ],
        explanation: `
The method crazySwitch(int grade) takes an integer grade and uses a switch statement to perform different actions based on the value of grade.

There is a constant bestGrade with a value of 100.

Calling crazySwitch(2):

The switch statement matches case 2, but since there is no code associated with it and no break statement after case 2, the code falls through to the default block. In the default block, val is set to "def". Then, the code continues to case bestGrade, where val is reassigned to "best". Finally, the break statement ends the switch, and the method prints "best".

Calling crazySwitch(100):

The switch statement matches case bestGrade (since 100 equals bestGrade). It sets val to "best", and then the break statement ends the switch. The method prints "best".

Important: There is a missing break after case 2: and default:. This means that no matter what value is given to the crazySwitch() method it will always print "best".

Conclusion:
Calling crazySwitch(100) prints "best".
Calling crazySwitch(2) also prints "best" because of the fall-through behavior and the reassignment of val to "best" in case bestGrade.`
    }, {
        id: 1023,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode1023,

        answers: [
            {
                id: 1,
                content: 'true',
                correct: false
            }, {
                id: 2,
                content: 'false',
                correct: false
            }, {
                id: 3,
                content: 'Compilation error',
                correct: true
            }, {
                id: 4,
                content: 'Runtime error',
                correct: false
            }
        ],
        explanation: 'This snippet produces compile error at the if condition.\n \nThe issue here is t' +
                'hat localBoolean = !passedBoolean is an assignment operation, not a comparison. ' +
                'Firstly passedBoolean != localBoolean returns false.\n \nSo next step would be f' +
                'alse = !passedBoolean, this, as it was stated assignment not a comparison.'
    }, {
        id: 1024,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode1024,
        answers: [
            {
                id: 1,
                content: 'crazySwitch(2) -> no params too many params 1 param',
                correct: false
            }, {
                id: 4,
                content: 'crazySwitch(1) ->  1 param',
                correct: true
            }, {
                id: 3,
                content: 'crazySwitch(1) ->  too many params 1 param',
                correct: false
            }, {
                id: 5,
                content: 'crazySwitch(0) ->  no params too many params 1 param',
                correct: true
            }, {
                id: 2,
                content: 'crazySwitch(null) -> no params',
                correct: false
            }
        ],
        explanation: 'crazySwitch(new String[2]); -> The input array has a length of 2, so the code en' +
                'ters the default: block and prints "too many params". The code then falls throug' +
                'h to the case 1: block and prints "1 param".\nFull output is: "too many params 1' +
                ' param" \n \ncrazySwitch(new String[1]); -> The input array has a length of 1, s' +
                'o the code enters the case 1: block and prints "1 param". Since this is the last' +
                ' case it exists the method.\n \ncrazySwitch(new String[0]); -> The input array h' +
                'as a length of 0, so the code enters the case 0: block and prints "no params". S' +
                'ince there is no break statement after the case 0: block, the code continues to ' +
                'execute the subsequent blocks.\nIn this case, the default: block is executed, wh' +
                'ich prints "too many params".\nThe code then falls through to the case 1: block ' +
                'and prints "1 param".\nFull output is: "no params too many params 1 param" \n \n' +
                'crazySwitch(null); -> will throw runtime exception because we cannot have .lengt' +
                'h of a null.'
    }, {
        id: 1025,
        category: 3,
        isImage: false,
        title: "What will be printed in console if we call Student.crazySwitch(5) method?",
        image: javaCode1025,
        answers: [
            {
                id: 1,
                content: 'Nothing will be printed',
                correct: false
            }, {
                id: 2,
                content: '~',
                correct: false
            }, {
                id: 3,
                content: '~3',
                correct: false
            }, {
                id: 4,
                content: '~34',
                correct: true
            }, {
                id: 5,
                content: '~3412',
                correct: false
            }
        ],
        explanation: 'There is no explicit case 5 in the switch statement therefore the default: block' +
                ' is executed.\n \nIt prints "~". The fall-through behavior continues to the case' +
                ' 3: block, which prints "3".\n \nThe fall-through also continues to the case 4: ' +
                'block, which prints "4".\n \nHowever, there is a break; statement after this blo' +
                'ck, so the fall-through behavior is stopped. The code exits the switch statement' +
                ' and the method.'
    }, {
        id: 1026,
        category: 3,
        isImage: false,
        title: 'Which of the following has compilation error?',
        answers: [
            {
                id: 1,
                content: 'if(3!=5) {}',
                correct: false
            }, {
                id: 2,
                content: 'if(true) {} else {System.out.println();}',
                correct: false
            }, {
                id: 3,
                content: 'if(5!=true? true : false) {}',
                correct: true
            }, {
                id: 4,
                content: 'if(false) {}',
                correct: false
            }, {
                id: 5,
                content: 'if (7 == 777) {}',
                correct: false
            }
        ],
        explanation: 'Iron rule of if is that it needs boolean in its brackets, almost all of them pro' +
                'vide it, only 5!=true will cause compilation error because we cannot compare int' +
                ' with booleans.\n \nSo only 1 snippet has compilation error.'
    }, {
        id: 1027,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode1027,
        answers: [
            {
                id: 1,
                content: 'crazyIf(true) -> false',
                correct: false
            }, {
                id: 2,
                content: 'crazyIf(true) -> true',
                correct: true
            }, {
                id: 3,
                content: 'crazyIf(false) -> false',
                correct: true
            }, {
                id: 4,
                content: 'crazyIf(false) -> true',
                correct: false
            }, {
                id: 5,
                content: 'Compilation error',
                correct: false
            }
        ],
        explanation: 'crazyIf(true) -> localBoolean != passedBoolean evaluates to true, because localB' +
                'oolean is false and passedBoolean is true.\n \nThen, passedBoolean = true is exe' +
                'cuted. This assignment sets the value of passedBoolean to true. So, the entire c' +
                'ondition in the if statement evaluates to true.\n \nSince the condition in the i' +
                'f statement evaluates to true, the code enters the if block and prints "true".\n' +
                ' \ncrazyIf(false) -> localBoolean != passedBoolean evaluates to false, because b' +
                'oth localBoolean and passedBoolean are false. Then, passedBoolean = false is exe' +
                'cuted. This assignment sets the value of passedBoolean to false.\n \nSo, the ent' +
                'ire condition in the if statement evaluates to false.'
    }, {
        id: 1028,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode1028,
        answers: [
            {
                id: 1,
                content: '1',
                correct: true
            }, {
                id: 2,
                content: '2',
                correct: false
            }, {
                id: 3,
                content: '3',
                correct: true
            }, {
                id: 4,
                content: '4',
                correct: true
            }, {
                id: 5,
                content: 'None of the above',
                correct: false
            }
        ],
        explanation: ' (bool = (bool2 & alwaysTrue(i++));), bool2 is true, and the alwaysTrue(i++) met' +
                'hod is called. This prints "1" and returns true.\n \nThe & (bitwise AND) operato' +
                'r evaluates both operands. (bool = (bool && alwaysTrue(++i));), bool is currentl' +
                'y true, and alwaysTrue(++i) is called. This prints "3" and returns true.\n \nThe' +
                ' && (logical AND) operator performs short-circuit evaluation, meaning if the lef' +
                't operand is false, the right operand is not evaluated. \n \n(bool = (bool | alw' +
                'aysTrue(++i));), bool is currently true, and alwaysTrue(++i) is called. This pri' +
                'nts "4" and returns true.\n \nThe | (bitwise OR) operator evaluates both operand' +
                's. (bool = (bool1 || alwaysTrue(i++));), bool1 is true, and alwaysTrue(i++) is n' +
                'ot called.\n \nThe || (logical OR) operator performs short-circuit evaluation me' +
                'aning if the left operand is true, the right operand is not evaluated.\n \nHence' +
                ' => 1 3 4'
    }, {
        id: 1029,
        category: 3,
        isImage: false,
        title: "What will crazyIncrements() method return?",
        image: javaCode1029,
        answers: [
            {
                id: 1,
                content: '-21',
                correct: false
            }, {
                id: 2,
                content: '-15',
                correct: false
            }, {
                id: 3,
                content: '-23',
                correct: false
            }, {
                id: 4,
                content: '-14',
                correct: true
            }, {
                id: 5,
                content: '-22',
                correct: false
            }
        ],
        explanation: 'Initialize a1 with -4.\n \nCalculate z1 using the post-decrement operation: z1 =' +
                ' a1-- - 1; z1 becomes -5 (since a1 is -4 and then gets decremented).\n \nCalcula' +
                'te b1 using the pre-increment and post-decrement operations: b1 = ++z1 + a1--; z' +
                '1 becomes -4 (since it was pre-incremented before).\n \nb1 becomes -4 + -5 = -9 ' +
                '(since z1 is now -4 and a1 is still -4 and gets decremented).\n \nCompare z1 and' +
                ' b1: if(z1 > b1). Since -4 is not greater than -9, the condition is false.\n \nE' +
                'xecute the else block: a1 = a1++ - 3; a1 becomes -1 (since a1 is -4 and then it ' +
                'is incremented).\n \nFinally, return the sum of a1 + z1 + b1: -1 + -4 + -9 = -14' +
                '.'
    }, {
        id: 1030,
        category: 3,
        isImage: false,
        title: "Which lines cause compilation errors?",
        image: javaCode1030,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '2',
                correct: true
            }, {
                id: 3,
                content: '3',
                correct: true
            }, {
                id: 4,
                content: '4',
                correct: false
            }, {
                id: 5,
                content: '5',
                correct: true
            }
        ],
        explanation: 'In order to solve this question you need to know typecasting between different d' +
                'ata types (char, byte, and int).\n \nYou cannot directly assign a char to a byte' +
                ' or to an int without an explicit cast because either char or int is larger than' +
                ' a byte.\n \nSame logic applies when casting byte to an int. So compilation erro' +
                'rs occurs on lines -> 2 , 3 and 5'
    }, {
        id: 1031,
        category: 3,
        isImage: false,
        title: "What case value isn't allowed and causes compilation error?",
        image: javaCode1031,
        answers: [
            {
                id: 1,
                content: 'A',
                correct: false
            }, {
                id: 2,
                content: 'default',
                correct: false
            }, {
                id: 3,
                content: '-100',
                correct: false
            }, {
                id: 4,
                content: '80',
                correct: false
            }, {
                id: 5,
                content: 'Code works just fine',
                correct: true
            }
        ],
        explanation: 'The following types can be used as a switch variable: byte, char, short, int, St' +
                'ring together with their wrappers classes.\n \nAlso remember that range of a byt' +
                'e is from -128 to 127 and so -100 and 80 is assignable to x.\n \nThe integral va' +
                'lue of A is 65, which is less than 127 so it is fine. So current code works just' +
                ' fine.'
    }
];