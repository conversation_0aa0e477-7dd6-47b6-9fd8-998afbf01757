import { Questions } from "../components/models/Questions";
import { time } from "../components/models/consts";
import { mindMazeExam, allQuestions, codeMingleExam, codeQuestExam, byteWizExam, javaZen } from "../components/questions/allQuestions";
import { AnswerSheet } from "./QuestionContentService";


export class QuestionService{

    static oddOrEven(questions: Questions[], even:boolean):Questions[]{
        var evenArr:Questions[] = [];
        var oddArr:Questions[] = [];
        for(var question of questions){
            if(question.id%2===0){
                evenArr.push(question);
            }else{
                oddArr.push(question);
            }
        }
        if(even) { return evenArr;}
        return oddArr;
    }
    //revert
    static getQuestionsByCategory(category:number):Questions[]{
        var requiredCategory: Questions[] = [];
        for(var question of allQuestions){
            if(question.category === category){
                requiredCategory.push(question)
            }
        }
        return requiredCategory;  
    }

    static getLimitedQuestions(questionSet: Questions[]):Questions[]{
        if(questionSet.length < 65){
            return questionSet;
        }else{
            const shuffledArray = this.shuffleArray(questionSet);
            return shuffledArray.slice(0, 65);
        }
    }

    static getRandomQuestions(amount:number):Questions[]{
        const shuffled = allQuestions.slice(0); // create a copy of the original array
        let i = allQuestions.length;
        while (i--) {
            const index = Math.floor((i + 1) * Math.random());
            [shuffled[index], shuffled[i]] = [shuffled[i], shuffled[index]]; // swap elements
        }
        return shuffled.slice(0, amount);
    }

    static getDataSet = (id:number): Questions[] =>{
        switch(id){
            case 0:return allQuestions;break;
            case 1:return mindMazeExam;break;
            case 2:return codeMingleExam;break;
            case 3:return codeQuestExam;break;
            case 4:return byteWizExam;break;
            case 5:return javaZen;break;
            case 10:return QuestionService.getQuestionsByCategory(1);break;
            case 11:return QuestionService.getQuestionsByCategory(2);break;
            case 12:return QuestionService.getQuestionsByCategory(3);break;
            case 13:return QuestionService.getQuestionsByCategory(4);break;
            case 14:return QuestionService.getQuestionsByCategory(5);break;
            case 15:return QuestionService.getQuestionsByCategory(6);break;
            case 16:return QuestionService.getQuestionsByCategory(7);break;
            case 17:return QuestionService.getQuestionsByCategory(8);break;
            case 18:return QuestionService.getQuestionsByCategory(9);break;
            case 19:return QuestionService.getQuestionsByCategory(10);break;
            default:return allQuestions;break;
        }
    }

    static getQuestionById(id:number, dataset:number): Questions{
        var dataSet:Questions[] = this.getDataSet(dataset);
        for(var question of dataSet){
            if(question.id===id){
                return question;
            }
        }
    }

    static shuffleArray(array: Questions[]): Questions[] {
        const shuffledArray = [...array];
        for (let i = shuffledArray.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffledArray[i], shuffledArray[j]] = [shuffledArray[j], shuffledArray[i]]; // Swap elements
        }
        return shuffledArray;
    }


    static checkAnswers(answerSheet: AnswerSheet): AnswerSheet {
        const { selectedAnswers, allAnswers } = answerSheet;
        answerSheet.isWrong = false; // Initialize isWrong to false
      
        if(selectedAnswers.length === 0){
            answerSheet.isWrong = true;
            return answerSheet;
        }



        // Create a set of correct answer IDs for faster lookup
        const correctAnswerIds = new Set(
          allAnswers.filter((answer) => answer.correct).map((answer) => answer.id)
        );

        if(selectedAnswers.length !== correctAnswerIds.size){
            answerSheet.isWrong = true;
            return answerSheet;
        }
      
        // Check if any selected answer is not in the list of correct answers
        for (const selectedAnswer of selectedAnswers) {
          if (!correctAnswerIds.has(selectedAnswer.id)) {
            answerSheet.isWrong = true;
            break; // Stop checking once an incorrect answer is found
          }
        }
      
        return answerSheet;
      }



}