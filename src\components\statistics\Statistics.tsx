import React, { Component } from 'react';
import { StyleSheet, View, Text, FlatList, TouchableOpacity} from 'react-native';
import { generateRandomCode } from '../../service/RandomService';
import { AppBar, Button, IconButton} from "@react-native-material/core";
import { StatisticsI, StatisticsService } from '../../service/StatisticsService';
import { generalExam } from '../models/consts';
import { FontAwesome5, MaterialCommunityIcons} from '@expo/vector-icons';
import { ExamBody } from '../models/Exams';
import PieChart from 'react-native-pie-chart'
import { AnswerSheet } from '../../service/QuestionContentService';
import { BannerAd, BannerAdSize, TestIds } from 'react-native-google-mobile-ads';
import Icon from "@expo/vector-icons/MaterialCommunityIcons";
import { scale, verticalScale } from 'react-native-size-matters';
import GradientContainer from '../../helpers/GradientContainer';
import LoadingDots from 'react-native-loading-dots';
import { appColors } from '../../utils/appColors';


interface StatisticsState {
  dataset: StatisticsI[];
  loading:boolean;
}

const adUnitId = __DEV__ ? TestIds.BANNER : 'ca-app-pub-5981144475529351/3452174828';

class Statistics extends Component<{}, StatisticsState> {
  constructor(props: {}) {
    super(props);
    this.state = {
      dataset:[],
      loading:false,
    };
  }

  async componentDidMount() {    
    var allStatistics = await StatisticsService.getAllStatistics();
    this.setState({dataset:allStatistics});
  }


  formatString = (inputTimeString):string =>{
    const inputDate = new Date(inputTimeString);

    const day = String(inputDate.getUTCDate()).padStart(2, "0");
    const month = String(inputDate.getUTCMonth() + 1).padStart(2, "0"); // Month is zero-based, so add 1
    const year = String(inputDate.getUTCFullYear());
    const hours = String(inputDate.getUTCHours()).padStart(2, "0");
    const minutes = String(inputDate.getUTCMinutes()).padStart(2, "0");

    const formattedTimeString = `${day}-${month}-${year} ${hours}:${minutes}`;
    return formattedTimeString;
  }

  
  renderEmptyItem =( ) => {
    return (
      <View style={empty.content}>
          <Text style={empty.title}>No Statistics</Text>
          <FontAwesome5 name="chart-pie" style={empty.iconStyle} size={scale(100)} color={appColors.white} />
      </View>
    );
  };

  calculateWrongProcent (myAnswerSheet:AnswerSheet[]){
    var wrong = 0;
    var amountOfQuestion = myAnswerSheet.length;
    for(var sheet of myAnswerSheet){
      if(sheet.isWrong){ wrong++;}
    }
    return (wrong*100)/amountOfQuestion;
  }

  async cleanStats(){
    await StatisticsService.deleteStatistics();
    this.setState({dataset:[]});
  }

  render() {
    const {loading, dataset} = this.state;
    if(loading){
      return (
        <GradientContainer>
          <View style={styles.dotsContainer}>
          <LoadingDots
              size={scale(35)}
              colors={[appColors.white, appColors.white, appColors.white]}
              dots={3}
              borderRadius={scale(15)}/>
          </View>
        </GradientContainer>
      )
    }
    return (
      <GradientContainer>
      <View style={styles.whiteBack}>        
      <AppBar
        title={"Statistics"}
        transparent={true}
        titleStyle={{ fontFamily: "AmorriaBrush", fontSize:scale(25)}}
        contentContainerStyle={{ marginTop:scale(35) }} 
        trailing={ props => (
        <IconButton
        onPress={() => this.cleanStats()}
        icon={props => <MaterialCommunityIcons name="trash-can" size={props.size+10} color={"white"} />}
        {...props}
        />
      )}
      leading={props => (
        <IconButton icon={props => <FontAwesome5 name="chart-pie" color={'white'} size={30} />} {...props} />
      )} />
      <View style={styles.container}>
        {
          dataset==null || dataset.length === 0
          ?
            this.renderEmptyItem()
          :
        <FlatList
          style={styles.list}
          contentContainerStyle={styles.listContainer}
          data={dataset}
          horizontal={false}
          numColumns={2}
          keyExtractor={() => {
            return generateRandomCode()
          }}
          ItemSeparatorComponent={() => {
            return <View style={styles.separator} />
          }}
          renderItem={({ item }) => {
            let wrongProcent = this.calculateWrongProcent(item.answerSheets);
            var examBody:ExamBody = null;
            examBody = generalExam.get(item.examId);
            return (
              <TouchableOpacity style={styles.card} onPress={() => this.props.navigation.navigate('ListStatisticQuestions',{statisticsID:item.id}) }  >
                <View style={styles.cardHeader}>
                  <Text style={styles.price}>#{item.attemptNumber} {examBody.name} </Text>
                    <Text style={[styles.title, styles.date]}>{this.formatString(item.date)}</Text> 
                    <Text style={[styles.title, styles.correct]}>Correct: {(100-wrongProcent).toFixed()}%</Text>
                    <Text style={[styles.title, styles.wrong]}>Wrong: {wrongProcent.toFixed()}%</Text>                    
                </View>

                <PieChart
                  widthAndHeight={100}
                  series={[(100-wrongProcent), wrongProcent]}
                  sliceColor={[appColors.blue, appColors.black]}
                  coverRadius={0.6}
                  coverFill={'#FFF'}
                  style={{alignSelf:'center',margin:15}}
                />
                <Button 
                  title="View" 
                  trailing={props => <Icon name="send" {...props} />} 
                  style={styles.buttonStyle}
                  titleStyle={{fontFamily:'LatoRegular', fontSize: scale(12)}}
                  onPress={() => this.props.navigation.navigate('ListStatisticQuestions',{statisticsID:item.id}) }  />
              </TouchableOpacity>
            )
          }}
        />
         }
      </View>
      </View>     
      <BannerAd 
        unitId={adUnitId}
        size={BannerAdSize.INLINE_ADAPTIVE_BANNER}
        requestOptions={{
          requestNonPersonalizedAdsOnly:false
        }}                        
        />

    </GradientContainer>
    );
  }
}

const empty = StyleSheet.create({
  content :{
    marginTop:'30%',
    flex:4,
    justifyContent:'center',
    alignItems:'center',
  },
  title:{
    fontSize:scale(28),
    padding:5,
    color:appColors.white,
    fontFamily:'AmorriaBrush',
  },
  iconStyle:{
    marginTop:scale(5),
    padding:scale(10),
  },
});

const styles = StyleSheet.create({
  whiteBack: {
    height: '90%',
    paddingBottom: '15%',
  },
  list: {
    paddingHorizontal: scale(5),
  },
  price: {
    fontSize: scale(18),
    fontFamily: 'AmorriaBrush',
    color: 'black',
    textAlign: 'center',
    marginTop: scale(5),
  },
  listContainer: {
    alignItems: 'center',
  },
  buttonStyle: {
    backgroundColor: appColors.blue,
    margin: scale(15),
  },
  title: {
    color: '#000000',
    textAlign: 'center',
  },
  wrong: {
    fontSize: scale(16),
    color: appColors.black,
    fontFamily: 'LatoRegular',
  },
  date:{
    fontSize: scale(14),
    color: appColors.black,
    fontFamily: 'LatoLight',
  },
  correct: {
    fontSize: scale(16),
    color: appColors.blue,
    fontFamily: 'LatoRegular',
  },
  container: {
    paddingVertical: scale(12),
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  card: {
    shadowColor: '#00000021',
    shadowOffset: {
      width: scale(2),
    },
    shadowOpacity: 0.5,
    shadowRadius: scale(4),
    marginVertical: scale(8),
    backgroundColor: 'white',
    flexBasis: '47%',
    marginHorizontal: scale(5),
    borderRadius: scale(30),
  },
  cardHeader: {
    paddingVertical: scale(17),
    paddingHorizontal: scale(16),
    borderTopLeftRadius: scale(1),
    borderTopRightRadius: scale(1),
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  content: {
    marginLeft: scale(16),
    flex: 1,
  },
  separator: {
    height: scale(1),
  },
  name: {
    fontSize: scale(16),
    fontWeight: 'bold',
  },
  ads: {
    maxHeight: '10%',
    width: '100%',
    backgroundColor: '#E6E6E6',
  },
  dotsContainer: {
    marginHorizontal: scale(40),
    marginTop: verticalScale(190),
    width: '40%',
    alignContent: 'center',
    alignSelf: 'center',
    height: '15%',
    justifyContent: 'center',
  },
});

export default Statistics;