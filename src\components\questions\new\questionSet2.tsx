import {Questions} from "../../models/Questions";
import {
    task336,
    task337,
    task338,
    task339,
    task340,
    task341,
    task342,
    task343,
    task344,
    task345,
    task346,
    task354,
    task355,
    task356,
    task357,
    task358,
    task361,
    task365
} from "./snippets/zenPart1";

export const newSetPart2 : Questions[] = [
    {
        id: 336,
        category: 6,
        isImage: false,
        title: "Which of the following statements is true about exception",
        image: task336,
        answers: [
            {
                id: 1,
                content: "Method 2: Integer, Integer",
                correct: false
            }, {
                id: 2,
                content: "Method 3: Object, Object",
                correct: false
            }, {
                id: 3,
                content: "Method 1: int, int",
                correct: true
            }, {
                id: 4,
                content: "Compilation error due to ambiguous method call",
                correct: false
            }
        ],
        explanation: "The key challenge in this question is understanding Java's method resolution rul" +
                "es:\n- Integer in = 5; creates an Integer object.\n- int i = 1; is a primitive i" +
                "nt.\n- The method call test(in, i) needs to resolve the best match among the ava" +
                "ilable methods.\n- test(Integer, Integer) is not a perfect match since i is prim" +
                "itive.\n- test(Object, Object) is too generic and is only chosen if no better op" +
                "tion exists.\n- test(int, int) is selected because Java unboxes Integer in to in" +
                "t to match test(int, int) perfectly.\nFinal output: Method 1: int, int."
    }, {
        id: 337,
        category: 2,
        isImage: false,
        title: "What will be the output of the following Java program demonstrating data type co" +
                "nversions and operations?",
        image: task337,
        answers: [
            {
                id: 1,
                content: "128\n50.0\ntrue",
                correct: false
            }, {
                id: 2,
                content: "-128\n50.0\ntrue",
                correct: true
            }, {
                id: 5,
                content: "128\n50.0\nfalse",
                correct: false
            }, {
                id: 3,
                content: "Compilation error due to byte overflow",
                correct: false
            }, {
                id: 4,
                content: "-128\n50.0\nfalse",
                correct: false
            }
        ],
        explanation: "This question tests Java's type conversion rules:\n- Byte Overflow: b = 127; b +" +
                "= 1; causes an overflow, wrapping b to -128.\n- Float Multiplication: d = f * d;" +
                " results in 5.0 * 10.0 = 50.0.\n- Type Casting: int x = (int) 2.9; truncates to " +
                "2, making (x == y) evaluate to true.\nFinal Output: -128\n50.0\ntrue."
    }, {
        id: 338,
        category: 2,
        isImage: false,
        title: "What will be the output of the following Java program involving data types and t" +
                "ype casting?",
        image: task338,
        answers: [
            {
                id: 2,
                content: "2.0\n11",
                correct: false
            }, {
                id: 3,
                content: "2.625\n12",
                correct: false
            }, {
                id: 1,
                content: "2.625\n11",
                correct: true
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "This program involves division and type casting:\n- result = d / i; gives 10.5 /" +
                " 4 = 2.625.\n- x = (int) (f + i); gives 7.5 + 4 = 11.5, which is cast to 11.\n**" +
                "Final Output:** 2.625\n11."
    }, {
        id: 339,
        category: 3,
        isImage: false,
        title: "What will be the output of the following Java program involving logical operator" +
                "s?",
        image: task339,
        answers: [
            {
                id: 1,
                content: "true",
                correct: true
            }, {
                id: 2,
                content: "false",
                correct: false
            }, {
                id: 3,
                content: "Compilation error",
                correct: false
            }, {
                id: 4,
                content: "true\nfalse",
                correct: false
            }
        ],
        explanation: "This program evaluates logical expressions:\n- (a < b && b < c) is true.\n- !(a " +
                "== 10) is false.\n- true || false is true.\nFinal Output: true."
    }, {
        id: 340,
        category: 4,
        isImage: false,
        title: "What will be the output of the following Java program demonstrating array refere" +
                "nces?",
        image: task340,
        answers: [
            {
                id: 2,
                content: "10\n1",
                correct: false
            }, {
                id: 3,
                content: "1\n10",
                correct: false
            }, {
                id: 1,
                content: "10\n10",
                correct: true
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "This program tests array references in Java:\n- newArr = arr; means newArr and a" +
                "rr point to the same array.\n- Modifying newArr[0] also modifies arr[0].\nFinal " +
                "Output: 10 10."
    }, {
        id: 341,
        category: 5,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task341,
        answers: [
            {
                id: 2,
                content: "5",
                correct: false
            }, {
                id: 3,
                content: "10",
                correct: false
            }, {
                id: 1,
                content: "15",
                correct: true
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "The program sums the integers from 1 to 5:\n- sum += i adds i to sum for each it" +
                "eration.\n- The loop runs while i <= 5, and the final sum is 1 + 2 + 3 + 4 + 5 =" +
                " 15.\nFinal Output: 15."
    }, {
        id: 342,
        category: 1,
        isImage: false,
        title: "What will be the output of the following Java program comparing strings?",
        image: task342,
        answers: [
            {
                id: 1,
                content: "true\ntrue\ntrue",
                correct: false
            }, {
                id: 2,
                content: "true\ntrue\nfalse",
                correct: false
            }, {
                id: 3,
                content: "false\ntrue\nfalse",
                correct: true
            }, {
                id: 4,
                content: "false\nfalse\nfalse",
                correct: false
            }
        ],
        explanation: "The == operator checks for reference equality, so str1 == str2 is false because " +
                "they are different objects. The equals() method checks for value equality, so st" +
                "r1.equals(str2) is true. For str1 == str3, it is false because str1 is created u" +
                "sing the new keyword, so they are different objects."
    }, {
        id: 343,
        category: 1,
        isImage: false,
        title: "What will be the output of the following Java program ?",
        image: task343,
        answers: [
            {
                id: 2,
                content: "Long: 1000\nInt: 1000\nShort: 1000\nByte: 100",
                correct: false
            }, {
                id: 3,
                content: "Long: 1000\nInt: 1000\nShort: 0\nByte: 0",
                correct: false
            }, {
                id: 1,
                content: "Long: 1000\nInt: 1000\nShort: 1000\nByte: -24",
                correct: true
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "When casting a larger type (long) to a smaller type (byte), the value can overfl" +
                "ow. The byte overflows to -24 as it cannot hold the value 1000."
    }, {
        id: 344,
        category: 1,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task344,
        answers: [
            {
                id: 1,
                content: "World,",
                correct: false
            }, {
                id: 2,
                content: "Hello, World",
                correct: false
            }, {
                id: 3,
                content: "HelloWorld",
                correct: false
            }, {
                id: 4,
                content: "World",
                correct: true
            }
        ],
        explanation: "After appending `World` to `Hello`, a comma is inserted at index 5. Then the fir" +
                "st 7 characters are deleted, resulting in `World`."
    }, {
        id: 345,
        category: 1,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task345,
        answers: [
            {
                id: 1,
                content: "String matches!",
                correct: false
            }, {
                id: 2,
                content: "String don't match!",
                correct: false
            }, {
                id: 3,
                content: "No output",
                correct: false
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }, {
                id: 5,
                content: "Exception is thrown during runtime",
                correct: true
            }
        ],
        explanation: "The issue occurs because this code snippet tries to call the equals method on a " +
                "null object. In Java, you cannot invoke methods on a null reference. When str is" +
                " null, calling str.equals(null) will result in a NullPointerException because nu" +
                "ll does not have methods. To avoid this, you should first check if str is null b" +
                "efore calling equals."
    }, {
        id: 346,
        category: 8,
        isImage: false,
        title: "What is the output of the below Java Code?",
        image: task346,
        answers: [
            {
                id: 1,
                content: "Array is printed",
                correct: false
            }, {
                id: 2,
                content: "Error Code is printed",
                correct: false
            }, {
                id: 3,
                content: "ArrayIndexOutOfBoundsException",
                correct: false
            }, {
                id: 4,
                content: "Compilation error",
                correct: true
            }
        ],
        explanation: "ArrayIndexOutOfBoundsException has been already caught by base class Exception l" +
                "eading to compilation error. So basically when a subclass exception is mentioned" +
                " after base class exception, then  this error occurs."
    }, {
        id: 347,
        category: 8,
        isImage: false,
        title: "An array index starts from 1?",
        answers: [
            {
                id: 1,
                content: "True",
                correct: false
            }, {
                id: 2,
                content: "False",
                correct: true
            }
        ],
        explanation: "False, in Java, array indices start from 0, not 1. This is a fundamental concept" +
                " in Java."
    }, {
        id: 348,
        category: 7,
        isImage: false,
        title: "Which of the following is true about inheritance in Java?\n1. Private methods ar" +
                "e final.\n2. Protected members are accessible within a package and inherited cla" +
                "sses outside the package.\n3. Protected methods are final.\n4. We cannot overrid" +
                "e private methods.",
        answers: [
            {
                id: 1,
                content: "1 and 2",
                correct: false
            }, {
                id: 2,
                content: "1, 2 and 4",
                correct: true
            }, {
                id: 3,
                content: "1, 2 and 3",
                correct: false
            }, {
                id: 4,
                content: "1, 2, 3 and 4",
                correct: false
            }
        ],
        explanation: "1. True - In Java, private methods are implicitly final. This means they cannot " +
                "be overridden in a subclass because they are not accessible outside the class in" +
                " which they are defined.\n2. True - protected members (fields, methods, and cons" +
                "tructors) are accessible: within the same package (like default access) and in s" +
                "ubclasses (even if the subclass is in a different package) \n3. False - protecte" +
                "d methods are not implicitly final. They can be overridden in subclasses, provid" +
                "ed the subclass is in the same package or a different package. \n4. True - priva" +
                "te methods are not visible to subclasses, so they cannot be overridden. If a sub" +
                "class defines a method with the same name as a private method in the parent clas" +
                "s, it is treated as a new method in the subclass, not an override. "
    }, {
        id: 349,
        category: 5,
        isImage: false,
        title: "What is the purpose of the continue statement in a loop?",
        answers: [
            {
                id: 1,
                content: "To exit the loop immediately",
                correct: false
            }, {
                id: 2,
                content: "To terminate the program",
                correct: false
            }, {
                id: 3,
                content: "To skip the current iteration and move to the next iteration",
                correct: true
            }, {
                id: 4,
                content: "To execute a specific block of code",
                correct: false
            }
        ],
        explanation: "The continue statement in Java is used to skip the current iteration of a loop a" +
                "nd move to the next iteration."
    }, {
        id: 350,
        category: 6,
        isImage: false,
        title: "A static method in a class can _______",
        answers: [
            {
                id: 1,
                content: "Directly access other static members in the class.",
                correct: true
            }, {
                id: 2,
                content: "Directly access Non-static members in the class.",
                correct: false
            }, {
                id: 3,
                content: "Both A and B",
                correct: false
            }, {
                id: 4,
                content: "None of the above",
                correct: false
            }
        ],
        explanation: `Static members belong to the class in which they are declared and are not part of any instance of the class.

The static methods in a class can directly access other static members in the class. They cannot access instance (non-static) members of the class, as there is no notion of an object associated with the static method.`
    }, {
        id: 351,
        category: 7,
        isImage: false,
        title: `Which of the following is true about interfaces in java.`,
        image: `1) An interface can contain following type of members.
....public, static, final fields (i.e., constants) 
....default and static methods with bodies

2) An instance of interface can be created.

3) A class can implement multiple interfaces.

4) Many classes can implement the same interface.`,
        answers: [
            {
                id: 1,
                content: "1, 3 and 4",
                correct: true
            }, {
                id: 2,
                content: "1, 2 and 4",
                correct: false
            }, {
                id: 3,
                content: "2, 3 and 4",
                correct: false
            }, {
                id: 4,
                content: "1, 2, 3 and 4",
                correct: false
            }
        ],
        explanation: `Interfaces can have constants, default, and static methods (1).
You cannot create an instance of an interface (2 is false).
A class can implement multiple interfaces (3).
Multiple classes can implement the same interface (4).`
    }, {
        id: 352,
        category: 7,
        isImage: false,
        title: `Which of the following is/are true about constructors in Java?`,
        image: `1) Constructor name should be same as class name.

2) If you don't define a constructor for a class, a default parameterless constructor is automatically created by the compiler. 

3) The default constructor calls super() and initializes all instance variables to default value like 0, null.

4) If we want to parent class constructor, it must be called in first line of constructor.`,
        answers: [
            {
                id: 1,
                content: "1",
                correct: false
            }, {
                id: 2,
                content: "1, 2",
                correct: false
            }, {
                id: 3,
                content: "1, 2 and 3",
                correct: false
            }, {
                id: 4,
                content: "1, 2, 3 and 4",
                correct: true
            }
        ],
        explanation: "All statements are true: a constructor's name must match the class name, a defau" +
                "lt parameterless constructor is automatically created if none is defined, the de" +
                "fault constructor calls super() and initializes instance variables to default va" +
                "lues, and if calling a parent class constructor, it must be the first statement " +
                "in the constructor."
    }, {
        id: 353,
        category: 2,
        isImage: false,
        title: `Which of the following is not a primitive data type in Java?`,
        answers: [
            {
                id: 1,
                content: "int",
                correct: false
            }, {
                id: 2,
                content: "float",
                correct: false
            }, {
                id: 3,
                content: "String",
                correct: true
            }, {
                id: 4,
                content: "boolean",
                correct: false
            }
        ],
        explanation: "String is not a primitive data type; it is an object (reference type) in Java, d" +
                "efined by the String class in java.lang package."
    }, {
        id: 354,
        category: 7,
        isImage: false,
        title: "What is the output of the below Java Code?",
        image: task354,
        answers: [
            {
                id: 1,
                content: "A",
                correct: true
            }, {
                id: 2,
                content: "B",
                correct: false
            }, {
                id: 4,
                content: "Runtime error",
                correct: false
            }, {
                id: 5,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "The method print(int x) in class A is called because method overloading is resol" +
                "ved at compile time based on the reference type (A), not the runtime object (B) " +
                "printing A to the console."
    }, {
        id: 355,
        category: 8,
        isImage: false,
        title: "What is the output of the below Java Code?",
        image: task355,
        answers: [
            {
                id: 1,
                content: "A",
                correct: false
            }, {
                id: 2,
                content: "A B",
                correct: false
            }, {
                id: 3,
                content: "A C",
                correct: false
            }, {
                id: 4,
                content: "A B C",
                correct: true
            }, {
                id: 5,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "We start by printing A, then we catch RunTimeException and jumping to catch bloc" +
                "k. In catch block we print B, but we should know that the finally block always e" +
                "xecutes, even if a return statement is encountered in the catch block."
    }, {
        id: 356,
        category: 7,
        isImage: false,
        title: "What is the output of the below Java Code?",
        image: task356,
        answers: [
            {
                id: 1,
                content: "Bark\nBark",
                correct: false
            }, {
                id: 2,
                content: "Bark\nAnimal Sound",
                correct: false
            }, {
                id: 3,
                content: "Animal Sound\nBark",
                correct: true
            }, {
                id: 4,
                content: "Animal Sound\nAnimal Sound",
                correct: false
            }, {
                id: 5,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "The variable sound is resolved based on the reference type (Animal), but the met" +
                "hod makeSound() is resolved based on the runtime object (Dog), demonstrating pol" +
                "ymorphism."
    }, {
        id: 357,
        category: 8,
        isImage: false,
        title: "What is the output of the below Java Code?",
        image: task357,
        answers: [
            {
                id: 1,
                content: "a=0",
                correct: false
            }, {
                id: 2,
                content: "inside the finally block",
                correct: false
            }, {
                id: 3,
                content: "b=null",
                correct: false
            }, {
                id: 4,
                content: "a=0\nDivide by zero error\ninside the finally block",
                correct: true
            }, {
                id: 5,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "On division of 20 by 0, divide by zero exception occurs and control goes inside " +
                "the catch block. Also, the finally block is always executed whether an exception" +
                " occurs or not."
    }, {
        id: 358,
        category: 7,
        isImage: false,
        title: "What is the output of the below Java Code?",
        image: task358,
        answers: [
            {
                id: 1,
                content: "Base\nDerived\nBase",
                correct: false
            }, {
                id: 2,
                content: "Base\nBase\nDerived",
                correct: false
            }, {
                id: 3,
                content: "Base\nDerived\nDerived",
                correct: true
            }, {
                id: 4,
                content: "Compiler Error",
                correct: false
            }
        ],
        explanation: "Predicting the first line of output is easy. We create an object of type Base an" +
                "d call DoPrint(). DoPrint calls the print function and we get the first line.\n " +
                "DoPrint(y) causes the second line of output. Assigning a derived class reference" +
                " to a base class reference is allowed in Java. Therefore, the expression Base y " +
                "= new Derived() is a valid statement in Java. In DoPrint(), o starts referring t" +
                "o the same object as referred by y. Functions are virtual by default in Java. So" +
                ", when we call o.print(), the print() method of Derived class is called due to r" +
                "un time polymorphism present by default in Java.\nDoPrint(z) causes the third li" +
                "ne of output, we pass a reference of Derived type and the print() method of Deri" +
                "ved class is called again. The point to note here is: object slicing doesn’t hap" +
                "pen in Java. Because non-primitive types are always assigned by reference. "
    }, {
        id: 359,
        category: 8,
        isImage: false,
        title: "What is built-in base class in Java, which is used to handle all exceptions?",
        answers: [
            {
                id: 1,
                content: "Raise",
                correct: false
            }, {
                id: 2,
                content: "Exception",
                correct: false
            }, {
                id: 3,
                content: "Error",
                correct: false
            }, {
                id: 4,
                content: "Throwable",
                correct: true
            }
        ],
        explanation: "The Throwable class is the built-in base class for both errors and exceptions in" +
                " Java. It is the parent class for Exception and Error. While Exception handles e" +
                "xceptions that can be caught and handled by the program, Error represents seriou" +
                "s issues (like JVM errors) that are generally not handled by the program. So the" +
                " correct answer is Throwable."
    }, {
        id: 360,
        category: 5,
        isImage: false,
        title: "Which loop construct guarantees that the loop body is executed at least once?",
        answers: [
            {
                id: 1,
                content: "for loop",
                correct: false
            }, {
                id: 2,
                content: "while loop",
                correct: false
            }, {
                id: 3,
                content: "do-while loop",
                correct: true
            }, {
                id: 4,
                content: "none",
                correct: false
            }
        ],
        explanation: "The do-while loop in Java guarantees that the loop body is executed at least onc" +
                "e, as the condition is checked after the loop body is executed."
    }, {
        id: 361,
        category: 7,
        isImage: false,
        title: "What is the output of the below Java Code?",
        image: task361,
        answers: [
            {
                id: 1,
                content: "Base",
                correct: false
            }, {
                id: 2,
                content: "Derived",
                correct: false
            }, {
                id: 3,
                content: "Runtime Error",
                correct: false
            }, {
                id: 4,
                content: "Compiler Error",
                correct: true
            }
        ],
        explanation: "This code snippet produces compilation error because it has access modifier conf" +
                "lict. In the Base class, the foo() method is declared as public. In the Derived " +
                "class, the foo() method is declared as private. In Java, you cannot reduce the v" +
                "isibility of an overridden method. The Derived class is attempting to override t" +
                "he public method foo() from the Base class with a private method, which is not a" +
                "llowed."
    }, {
        id: 362,
        category: 5,
        isImage: false,
        title: "Which loop construct in Java is best suited when the number of iterations is unk" +
                "nown?",
        answers: [
            {
                id: 1,
                content: "for loop",
                correct: false
            }, {
                id: 2,
                content: "while loop",
                correct: true
            }, {
                id: 3,
                content: "do-while loop",
                correct: false
            }, {
                id: 4,
                content: "none",
                correct: false
            }
        ],
        explanation: "The while loop in Java is used when the number of iterations is unknown or depen" +
                "ds on a certain condition."
    }, {
        id: 363,
        category: 6,
        isImage: false,
        title: "Protected members in Java are accessible:",
        answers: [
            {
                id: 1,
                content: "Within their own class",
                correct: true
            }, {
                id: 2,
                content: "In other non-subclasses within the same package",
                correct: true
            }, {
                id: 3,
                content: "In all the sub-classes in any package",
                correct: true
            }, {
                id: 4,
                content: "In all sub-classes within the same package only",
                correct: false
            }, {
                id: 5,
                content: " Only in sub-classes and cannot be accessible by non-subclasses irrespective of " +
                        "package.",
                correct: false
            }
        ],
        explanation: `A protected member is accessible in:
1. All classes in the package containing its class
2. All the subclasses of its class in any package where this class is visible
3. Non-subclasses in other packages cannot access protected members from other packages.

Therefore, the correct options are - 1, 2, and 3.`
    }, {
        id: 365,
        category: 7,
        isImage: false,
        title: "What is the output of the below Java Code?",
        image: task365,
        answers: [
            {
                id: 1,
                content: "Base",
                correct: false
            }, {
                id: 2,
                content: "Derived",
                correct: false
            }, {
                id: 3,
                content: "Runtime Error",
                correct: false
            }, {
                id: 4,
                content: "Compiler Error",
                correct: true
            }
        ],
        explanation: "Final methods cannot be overridden. The method show() in the Base class is decla" +
                "red final, which means it cannot be overridden in the Derived class. Since the D" +
                "erived class tries to override the final method, it results in a compiler error."
    }
];