import {Questions} from "../../models/Questions";
import {task33, task34, task35, task38} from "./codeSnippets/questionSet1Snippets";
import {
    task39,
    task40,
    task41,
    task42,
    task43,
    task47,
    task48,
    task49,
    task51,
    task52,
    task53,
    task54,
    task55,
    task56,
    task57,
    task58,
    task60,
    task62,
    task63,
    task64,
    task65
} from "./codeSnippets/questionSet2Snippets";
export const passedSetPart2 : Questions[] = [
    {
        id: 33,
        category: 9,
        isImage: false,
        title: "Which of the following is invalid lambda expressions to replace implemented clas" +
                "s ?",
        image: task33,
        answers: [
            {
                id: 1,
                content: "InterSquare in = (int n) -> {return n*n;};",
                correct: false
            }, {
                id: 2,
                content: "InterSquare in = (int n) -> {n*n;}",
                correct: true
            }, {
                id: 3,
                content: "InterSquare in = n -> n*n;",
                correct: false
            }, {
                id: 4,
                content: "InterSquare in = (n) -> n*n;",
                correct: false
            }
        ],
        explanation: "Whenever we are using curly braces for lambda expressions body, return keyword s" +
                "hould be defined explicitly."
    }, {
        id: 34,
        category: 7,
        isImage: false,
        title: "What is the result?",
        image: task34,
        answers: [
            {
                id: 1,
                content: "ABCDEF",
                correct: true
            }, {
                id: 2,
                content: "ABCF",
                correct: false
            }, {
                id: 3,
                content: "Compilation error",
                correct: false
            }, {
                id: 4,
                content: "Exception thrown at runtime",
                correct: false
            }
        ],
        explanation: 'Run method starts by printing "A", then "B", attempts to divide by zero (causing' +
                ' an ArithmeticException), catches the exception and prints "C", executes the fin' +
                'ally block to print "D", and since there is no obstacles prints "E" and returns ' +
                'to main method, which finally prints "F" and ultimately outputs:ABCDEF'
    }, {
        id: 35,
        category: 7,
        isImage: false,
        title: "What is the result?",
        image: task35,
        answers: [
            {
                id: 1,
                content: "AC",
                correct: false
            }, {
                id: 2,
                content: "ABC",
                correct: false
            }, {
                id: 3,
                content: "Compilation error",
                correct: true
            }, {
                id: 4,
                content: "Exception thrown at runtime",
                correct: false
            }
        ],
        explanation: "This code causes a compilation error because the ml() method is declared to thro" +
                "w a general Exception, but the catch block in the main() method is specifically " +
                "set to handle only QuestionException, leaving other Exception types unhandled. I" +
                "n Java, if a method is declared with throws Exception, it must either catch Exce" +
                "ption or its subclasses, but in this case, the catch block only handles Question" +
                "Exception."
    }, {
        id: 36,
        category: 8,
        isImage: false,
        title: "Choose checked exceptions from the following list?",
        answers: [
            {
                id: 1,
                content: "InterruptedException",
                correct: true
            }, {
                id: 2,
                content: "IOException",
                correct: true
            }, {
                id: 3,
                content: "Exception",
                correct: true
            }, {
                id: 4,
                content: "RuntimeException",
                correct: false
            }, {
                id: 5,
                content: "Error",
                correct: false
            }
        ],
        explanation: "RuntimeException and its child classes, Error and its child classes are unchecke" +
                "d exceptions. Except these remaining are considered as checked exceptions."
    }, {
        id: 37,
        category: 9,
        isImage: false,
        title: "Which statements are TRUE about Predicate?",
        answers: [
            {
                id: 1,
                content: "It contains two methods: test(), apply()",
                correct: false
            }, {
                id: 2,
                content: "It contains only one method: test()",
                correct: true
            }, {
                id: 3,
                content: "The return type of test() method can be any type",
                correct: false
            }, {
                id: 4,
                content: "It is functional interface",
                correct: true
            }
        ],
        explanation: "A Predicate is a functional interface because it has a single abstract method (t" +
                "est()), which makes it compatible with lambda expressions or method references."
    }, {
        id: 38,
        category: 10,
        isImage: false,
        title: "What is the result?",
        image: task38,
        answers: [
            {
                id: 1,
                content: "2002",
                correct: false
            }, {
                id: 2,
                content: "2102",
                correct: false
            }, {
                id: 3,
                content: "Compilation error",
                correct: false
            }, {
                id: 4,
                content: "Exception thrown at runtime",
                correct: true
            }
        ],
        explanation: "45 is invalid value for Date and hence we will get runtime exception"
    }, {
        id: 39,
        category: 8,
        isImage: false,
        title: "What exceptions are thrown?",
        image: task39,
        answers: [
            {
                id: 1,
                content: "NullPointerException",
                correct: true
            }, {
                id: 2,
                content: "ArithmeticException",
                correct: false
            }, {
                id: 3,
                content: "Both NullPointerException and ArithmeticException",
                correct: false
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }, {
                id: 5,
                content: "No exception is thrown",
                correct: false
            }
        ],
        explanation: "If catch block and finally block throws exceptions, then only finally block thro" +
                "wn exception since it is higher priority and therefore NullPointerException is t" +
                "hrown since s is null"
    }, {
        id: 40,
        category: 7,
        isImage: false,
        title: "What will be printed in console?",
        image: task40,
        answers: [
            {
                id: 1,
                content: "Parent class method",
                correct: false
            }, {
                id: 2,
                content: "Child class method",
                correct: false
            }, {
                id: 3,
                content: "FileNotFoundException is thrown",
                correct: false
            }, {
                id: 4,
                content: "IOException is thrown",
                correct: false
            }, {
                id: 5,
                content: "Compilation fails",
                correct: true
            }
        ],
        explanation: "This code produces compilation error because it violates the following principle" +
                ": If parent class throws checked exception, then child class should either throw" +
                " the same exception or its child. FileNotFoundException is subclass of IOExcepti" +
                "on, however Child is subclass of Parent class."
    }, {
        id: 41,
        category: 6,
        isImage: false,
        title: "What will be printed in console?",
        image: task41,
        answers: [
            {
                id: 1,
                content: "int",
                correct: true
            }, {
                id: 2,
                content: "Integer",
                correct: true
            }, {
                id: 3,
                content: "Double",
                correct: false
            }, {
                id: 4,
                content: "Object",
                correct: false
            }
        ],
        explanation: "In this code snippet, two constructors are called. In new Question(7) => 7 is an" +
                " integer literal, so the constructor Question(int i) will be called because it m" +
                "atches the type of the argument. Therefore, it will print int to the console. ne" +
                "w Question(Integer.valueOf(" + 7 + ")); -> In this case, 7 is a string, and you're using Integer.valueOf() to conver" +
                "t it to an Integer object. Since there's a constructor with an Integer parameter" +
                " (Question(Integer i)), that constructor will be called, and it will print Integ" +
                "er to the console."
    }, {
        id: 42,
        category: 6,
        isImage: false,
        title: "What will be printed in console?",
        image: task42,
        answers: [
            {
                id: 1,
                content: "10",
                correct: false
            }, {
                id: 2,
                content: "100",
                correct: false
            }, {
                id: 3,
                content: "Compilation error",
                correct: true
            }, {
                id: 4,
                content: "Exception is thrown",
                correct: false
            }
        ],
        explanation: "Variables in interface are final by default and therefore cannot be updated. Con" +
                "sidering it, this code snippet fails during compilation mode"
    }, {
        id: 43,
        category: 7,
        isImage: false,
        title: "What will be printed in console?",
        image: task43,
        answers: [
            {
                id: 1,
                content: "AB",
                correct: false
            }, {
                id: 2,
                content: "CB",
                correct: false
            }, {
                id: 3,
                content: "ACB",
                correct: false
            }, {
                id: 4,
                content: "Compilation error",
                correct: true
            }, {
                id: 5,
                content: "Exception is thrown",
                correct: false
            }
        ],
        explanation: "This code snippet produces compilation error, because Child constructor donot ha" +
                "ve no-arg constructor, which causes compilation error. If no-args constructor be" +
                "en added, then compilation error will be removed"
    }, {
        id: 44,
        category: 7,
        isImage: false,
        title: "Which of the following is True regarding interface and abstract classes?",
        answers: [
            {
                id: 1,
                content: "Interface and abstract classes contain abstract methods.",
                correct: true
            }, {
                id: 2,
                content: "Interface and abstract classes contain constructors.",
                correct: false
            }, {
                id: 3,
                content: "Interface and abstract classes contain static methods.",
                correct: true
            }, {
                id: 4,
                content: "Interface and abstract classes contain instance variables.",
                correct: false
            }
        ],
        explanation: "Every variable present inside interface is always public, static and final and w" +
                "e cannot declare instance variables. But abstract class can contain instance var" +
                "iables. Both interface and abstract class can contain abstract methods. Both int" +
                "erface and abstract class can contain static methods. Constructors concept appli" +
                "cable only for classes but not for interfaces."
    }, {
        id: 45,
        category: 7,
        isImage: false,
        title: "Which of the following allowed in abstract class ?",
        answers: [
            {
                id: 1,
                content: "int z;",
                correct: true
            }, {
                id: 2,
                content: "void run();",
                correct: false
            }, {
                id: 3,
                content: "public static void run();",
                correct: true
            }, {
                id: 4,
                content: "public void run();",
                correct: false
            }, {
                id: 5,
                content: "public final void run();",
                correct: false
            }
        ],
        explanation: "For abstract class methods, if we don't want to provide implementation, then com" +
                "pulsory we should declare that method as abstract, otherwise we will get compile" +
                " time error."
    }, {
        id: 46,
        category: 7,
        isImage: false,
        title: "What is/are TRUE about constructors?",
        answers: [
            {
                id: 1,
                content: "First line inside should always be this()",
                correct: true
            }, {
                id: 2,
                content: "First line inside should always be super()",
                correct: false
            }, {
                id: 3,
                content: "First line inside should be either this() or super()",
                correct: true
            }, {
                id: 4,
                content: "First line inside can be any valid code",
                correct: false
            }
        ],
        explanation: "Important rule for consturctors - first line of every constructor is either supe" +
                "r() or this(). If non of it is given, then compiler will pass super()"
    }, {
        id: 47,
        category: 5,
        isImage: false,
        title: "What will be printed in console?",
        image: task47,
        answers: [
            {
                id: 1,
                content: "Sum of numbers: 0",
                correct: false
            }, {
                id: 2,
                content: "Sum of numbers: 15",
                correct: false
            }, {
                id: 4,
                content: "Compilation error",
                correct: true
            }, {
                id: 5,
                content: "Exception is thrown",
                correct: false
            }
        ],
        explanation: "The compilation fails because the numbers array does not have a .size property i" +
                "n Java. In Java, arrays do not have a size property; instead, you should use the" +
                " length property to get the length of an array. Therefore, the correct code woul" +
                "d be numbers.length instead of numbers.size in the loop condition."
    }, {
        id: 48,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task48,
        answers: [
            {
                id: 1,
                content: "121",
                correct: false
            }, {
                id: 2,
                content: "21",
                correct: true
            }, {
                id: 4,
                content: "222",
                correct: false
            }, {
                id: 3,
                content: "11",
                correct: false
            }
        ],
        explanation: "if( ++z < 0 && z++ > 1): The prefix increment ++z is applied first, which increm" +
                "ents z to 1. Since 1 is not less than 0, the first part of the condition evaluat" +
                "es to false, and short-circuiting prevents the evaluation of the second part (z+" +
                "+ > 1). The block inside the curly braces {} is executed next, since it donot be" +
                "long to if operator printing 2 to the console. Considering everything -> 21"
    }, {
        id: 49,
        category: 5,
        isImage: false,
        title: "What will be printed in console?",
        image: task49,
        answers: [
            {
                id: 1,
                content: "1 2 3 4 5 6",
                correct: false
            }, {
                id: 2,
                content: "0 1 2 3 4 5 6",
                correct: false
            }, {
                id: 3,
                content: "1 2 3 4 5",
                correct: false
            }, {
                id: 4,
                content: "0 1 2 3 4 5",
                correct: false
            }, {
                id: 5,
                content: "Compilation fails",
                correct: true
            }
        ],
        explanation: "The variable x is a local variable of do block and cannot be accessed from outsi" +
                "de. Hence we will get compile time error."
    }, {
        id: 50,
        category: 5,
        isImage: false,
        title: "What loop cannot be used to access elements in reverse order",
        answers: [
            {
                id: 1,
                content: "standard for loop",
                correct: false
            }, {
                id: 2,
                content: "for-each loop",
                correct: true
            }, {
                id: 3,
                content: "do-while loop",
                correct: false
            }, {
                id: 4,
                content: "while-do loop",
                correct: false
            }
        ],
        explanation: "for-each loop can be used to access array elements only in original order but no" +
                "t in reverse order."
    }, {
        id: 51,
        category: 1,
        isImage: false,
        title: "What will be printed in console?",
        image: task51,
        answers: [
            {
                id: 1,
                content: "var is > 10",
                correct: false
            }, {
                id: 2,
                content: "var is > 0",
                correct: true
            }, {
                id: 3,
                content: "var is < 0",
                correct: false
            }, {
                id: 4,
                content: "7",
                correct: true
            }, {
                id: 4,
                content: "8",
                correct: false
            }
        ],
        explanation: "The code prints var is > 0 followed by the value 7 because the first condition o" +
                "f the ternary operator fails, leading to a pre-decrement of var, resulting in 6," +
                " which satisfies the second condition and therefore executes another increment o" +
                "perator returning value 7 to variable var"
    }, {
        id: 52,
        category: 5,
        isImage: false,
        title: "What is the result?",
        image: task52,
        answers: [
            {
                id: 1,
                content: "5791113",
                correct: false
            }, {
                id: 2,
                content: "7911",
                correct: false
            }, {
                id: 3,
                content: "79",
                correct: false
            }, {
                id: 4,
                content: "7",
                correct: true
            }, {
                id: 5,
                content: "Compilation fails",
                correct: false
            }
        ],
        explanation: "In the first iteration of do-while, i value will become 6 which is less than 7. " +
                "In console value of i is incremented as well making it to print 7. This will mak" +
                "e inside while loop fails. For the next iterations of do-while, while loop never" +
                " executes as value of i is always more than 7. Hence the output is:7"
    }, {
        id: 53,
        category: 5,
        isImage: false,
        title: "What will be printed to console?",
        image: task53,
        answers: [
            {
                id: 1,
                content: "234",
                correct: false
            }, {
                id: 2,
                content: "35",
                correct: false
            }, {
                id: 3,
                content: "24",
                correct: true
            }, {
                id: 4,
                content: "1234",
                correct: false
            }, {
                id: 5,
                content: "2345",
                correct: false
            }
        ],
        explanation: "int i = 1; - Initializes the variable i with the value 1. while(++i < 5) - This " +
                "is the condition for the while loop. ++i increments the value of i before evalua" +
                "ting it. So, initially, i becomes 2 and then it checks if 2 is less than 5, whic" +
                "h is true. Therefore, the loop proceeds. System.out.print(i++); - This line prin" +
                "ts the value of i and then increments it. Since i is 2, it prints 2 and then inc" +
                "rements i to 3. The loop then goes back to the condition, ++i < 5, i is now 3. I" +
                "ncrementing i makes it 4, which is still less than 5, so the loop continues. Sys" +
                "tem.out.print(i++); - Again, this line prints the value of i, which is now 4, an" +
                "d then increments it to 5. The loop condition is checked again, ++i < 5, but i i" +
                "s now 5. Since 5 is not less than 5, the loop exits. So, the output will be 24."
    }, {
        id: 54,
        category: 3,
        isImage: false,
        title: "What will be printed to console?",
        image: task54,
        answers: [
            {
                id: 1,
                content: "0",
                correct: true
            }, {
                id: 2,
                content: "1",
                correct: false
            }, {
                id: 3,
                content: "2",
                correct: true
            }, {
                id: 4,
                content: "10",
                correct: true
            }, {
                id: 5,
                content: "20",
                correct: false
            }, {
                id: 6,
                content: "30",
                correct: false
            }
        ],
        explanation: "Default case can be located any place in switch operator, but it will be conside" +
                "red if no case is matched. In the above snippet, no case is matched and hence de" +
                "fault case will be executed and since default case is not ended with break opera" +
                "tor, all onwardsstatements will be executed until break or end of switch. Theref" +
                "ore such values as: 0 and 10 will be printed, which will increment the value of " +
                "total twice => 2"
    }, {
        id: 55,
        category: 3,
        isImage: false,
        title: "What will be printed to console?",
        image: task55,
        answers: [
            {
                id: 1,
                content: "10",
                correct: false
            }, {
                id: 2,
                content: "100",
                correct: true
            }, {
                id: 3,
                content: "1000",
                correct: true
            }, {
                id: 4,
                content: "Compilation fails",
                correct: false
            }, {
                id: 5,
                content: "Exception is thrown",
                correct: false
            }
        ],
        explanation: "First rule is that case label should be within the range of switch argument type" +
                " and constant. In the above code switch argument we have byte and char, which in" +
                " sum will give us higher type = int and sum 100. Value 100 exists in one of the " +
                "cases. Since no break operator is found this code will lead to execution of the nex" +
                "t case operator, printing 100 and 1000 accordingly"
    }, {
        id: 56,
        category: 3,
        isImage: false,
        title: "What will be printed to console?",
        image: task56,
        answers: [
            {
                id: 2,
                content: "Hi = 1",
                correct: false
            }, {
                id: 1,
                content: "Hello = 1",
                correct: false
            }, {
                id: 3,
                content: "Hi = 2",
                correct: false
            }, {
                id: 4,
                content: "Hello = 2",
                correct: true
            }, {
                id: 5,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "This code increments x, making its value to 1, and assigns its value to y, since" +
                " both of them have the same value, making boolean bl true and executing first pa" +
                "rt of if-else operator. This prints Hello =  followed by the incremented value o" +
                "f x = 2."
    }, {
        id: 57,
        category: 8,
        isImage: false,
        title: "What will be printed into console?",
        image: task57,
        answers: [
            {
                id: 1,
                content: "null",
                correct: false
            }, {
                id: 2,
                content: "Hello World!",
                correct: false
            }, {
                id: 3,
                content: "Compilation error",
                correct: true
            }, {
                id: 4,
                content: "Exception is thrown",
                correct: false
            }
        ],
        explanation: "This code snippet has compilation error. Because a StringBuilder is a different " +
                "class, and cannot be directly used as a String. You must use the toString() meth" +
                "od."
    }, {
        id: 58,
        category: 8,
        isImage: false,
        title: "What will be printed into console?",
        image: task58,
        answers: [
            {
                id: 1,
                content: "TRY",
                correct: false
            }, {
                id: 2,
                content: "CATCH",
                correct: false
            }, {
                id: 4,
                content: "FINALLY",
                correct: true
            }, {
                id: 3,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "If try,catch and finally blocks contains separate return statements, then finall" +
                "y block return statement will get more priority. Hence the output is :FINALLY"
    }, {
        id: 59,
        category: 1,
        isImage: false,
        title: "Pattern class present in java.util.regex package. Which of the following import " +
                "is required to use Pattern class directly in our program.",
        answers: [
            {
                id: 1,
                content: "import java.*;",
                correct: false
            }, {
                id: 2,
                content: "import java.util.*;",
                correct: false
            }, {
                id: 3,
                content: "import java.util.regex.*;",
                correct: true
            }, {
                id: 4,
                content: "No import required",
                correct: false
            }
        ],
        explanation: "When ever we are importing a package, all classes and interfaces present in that" +
                " package are available to our program but not subpackage classes. Hence if we wa" +
                "nt to use sub package class, compulsory we have to write import statement until " +
                "sub package level."
    }, {
        id: 60,
        category: 6,
        isImage: false,
        title: "What access modifiers should be inserted instead of modifier1 and modifier2, for this code to work?",
        image: task60,
        answers: [
            {
                id: 1,
                content: "nothing, nothing",
                correct: false
            }, {
                id: 2,
                content: " nothing, protected",
                correct: false
            }, {
                id: 3,
                content: "protected, protected",
                correct: false
            }, {
                id: 4,
                content: "protected, public",
                correct: false
            }, {
                id: 5,
                content: "public, public",
                correct: true
            }
        ],
        explanation: "Every method present in interface is by default public. Hence at the time of ove" +
                "rriding/implementing that method, we should declare methods as public. "
    }, {
        id: 61,
        category: 6,
        isImage: false,
        title: "Which of the following packages are by default available to every java source fi" +
                "le by default?",
        answers: [
            {
                id: 1,
                content: "java.util",
                correct: false
            }, {
                id: 2,
                content: "java.net",
                correct: false
            }, {
                id: 3,
                content: "java.lang",
                correct: true
            }, {
                id: 4,
                content: "java.sql",
                correct: false
            }
        ],
        explanation: "java.lang package is by default available for every java source file and we are " +
                "not required to import explicitly."
    }, {
        id: 62,
        category: 7,
        isImage: false,
        title: "What will be the result of compiling and executing Question class?",
        image: task62,
        answers: [
            {
                id: 1,
                content: "Parent-",
                correct: false
            }, {
                id: 2,
                content: "Child",
                correct: false
            }, {
                id: 3,
                content: "Parent-Child",
                correct: true
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }, {
                id: 5,
                content: "Exception is thrown",
                correct: false
            }
        ],
        explanation: "First of all, we have to remember the golden rule about the exceptions: if the c" +
                "onstructor of the parent class throws any checked exception, then the child clas" +
                "s constructor can throw the same exception or its parent classes. In this code P" +
                "arent class throws Runtime exception which extends Exception. So there is not co" +
                "mpilation error nor any thrown exception. Considering this we can execute and st" +
                "art explaining the snippet.\nWhen you create an instance of Child, the construct" +
                "or of Child is called. Before the constructor of Child can execute, the construc" +
                "tor of Parent is invoked(because every constructor implicitly calls the construc" +
                "tor of its superclass) printing Parent- in the console and exits. After the cons" +
                "tructor of Child is called printing Child in the same line. Considering this Par" +
                "ent-Child will be printed"

    }, {
        id: 63,
        category: 3,
        isImage: false,
        title: "What will be printed to console?",
        image: task63,
        answers: [
            {
                id: 1,
                content: "Nothing will be printed",
                correct: false
            }, {
                id: 2,
                content: "12",
                correct: true
            }, {
                id: 3,
                content: "13",
                correct: false
            }, {
                id: 4,
                content: "25",
                correct: false
            }, {
                id: 5,
                content: "26",
                correct: false
            }
        ],
        explanation: "This code contains many tricky parts. First operator if(false) donot cause compi" +
                "lation error because it is defined as dead code.\nDead code is code that is exec" +
                "uted but redundant, either the results were never used or adds nothing to the re" +
                "st of the program.\nUnreachable code - code that will never be reached regardles" +
                "s of logic flow. \nIf method without curly braces takes only one and next line of " +
                "code. Based on the condition, next line and only that line after the operator wi" +
                "ll be executed or ignored. In this case our first println operator is ignored. N" +
                "ow let us calculate the value of x, which is initialized as 10, on the next line" +
                " x is transformed into 11(x++) and then (++x)12.\nConsidering all the above the " +
                "printed value will be 12."
    }, {
        id: 64,
        category: 10,
        isImage: false,
        title: "What will be printed to console?",
        image: task64,
        answers: [
            {
                id: 1,
                content: "2022-07-07",
                correct: true
            }, {
                id: 2,
                content: "2022-07-12",
                correct: false
            }, {
                id: 3,
                content: "Compilation error",
                correct: false
            }, {
                id: 4,
                content: "Exception is thrown",
                correct: false
            }
        ],
        explanation: "LocalDate.parse(2023 - 02 - 07) parses the string 2023 - 02 - 07 into a LocalDat" +
                "e object representing February 7, 2023.date.plusMonths(-7)adds - 7 months to the" +
                " date.Since you're subtracting months, it effectively moves the date 7 months ba" +
                "ck. So, February 7, 2023, minus 7 months is July 7, 2022 => 2022-07-07"
    }, {
        id: 65,
        category: 10,
        isImage: false,
        title: "What can be added instead of blank? Consider that both of the classes are in the" +
                " same file",
        image: task65,
        answers: [
            {
                id: 1,
                content: "public",
                correct: false
            }, {
                id: 2,
                content: "default",
                correct: true
            }, {
                id: 3,
                content: "protected",
                correct: false
            }, {
                id: 4,
                content: "final",
                correct: true
            }, {
                id: 5,
                content: "abstract",
                correct: true
            }
        ],
        explanation: "public modifier is not allowed since the name of the file is Parent.java.\ndefau" +
                "lt modifier is called when we don't use any modifier, and makes our Child class " +
                "accessible only within package.\nprotected modifier as well as private modifier " +
                "can't be applied on the class.\nfinal modifier is a non-access modifier used for" +
                " classes, attributes and methods, which makes them non-changeable (impossible to" +
                " inherit or override).\nabstract modifier can be used here, making the class imp" +
                "ossible to create object.\nHowever using both abstract and final modifiers with " +
                "a class is illegal in Java."
    }
];