import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { task176, task177, task179, task180, task181, task182, task183, task184, task185 } from "./snips/basicSnips";

export const strings : Questions[] = [
    {
        id: 176,
        category: 2,
        isImage: false,
        title: "What values will be printed in console?",
        image: task176,
        answers: [
            {
                id: 1,
                content: '30',
                correct: true
            }, {
                id: 2,
                content: '300',
                correct: false
            }, {
                id: 3,
                content: 'Ben',
                correct: false
            }, {
                id: 4,
                content: 'JohnBen',
                correct: true
            }, {
                id: 5,
                content: 'Compilation error',
                correct: false
            }
        ],
        explanation: 'The given code defines a class Student with instance variables name of type Stri' +
                'ng and age of type int, plus this code does not have any error both compilation ' +
                'and execution.\n\nSystem.out.println(myAge + st.age) line attempts to print the ' +
                'sum of myAge (30) and st.age (0). This will print 30 because st.age is 0.\n\nSys' +
                'tem.out.println(myName + st.name) line attempts to concatenate my<PERSON><PERSON> ("<PERSON>") w' +
                'ith st.name ("<PERSON>"). This will print "JohnBen". '
    }, {
        id: 177,
        category: 2,
        isImage: false,
        title: "What will be printed in console?",
        image: task177,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '2',
                correct: true
            }, {
                id: 3,
                content: '3',
                correct: false
            }, {
                id: 4,
                content: '4',
                correct: false
            }, {
                id: 5,
                content: '5',
                correct: true
            }, {
                id: 6,
                content: '6',
                correct: true
            }
        ],
        explanation: 'The code you provided demonstrates various cases involving String comparisons an' +
                'd methods in Java.\n\nThe == operator checks for reference equality for objects.' +
                ' In this case, (hell + o) creates a new string "Hello". Even though its content ' +
                'is the same as the value of hello, it is a different object in memory. So, the c' +
                'ondition will not be true, and 1 will not be printed.\n\nThe literal "Hello" is ' +
                'a string constant, so both hello and "Hello" point to the same object in the str' +
                'ing pool. Thus, this condition is true, and 2 will be printed.\n\nThe concat met' +
                'hod creates a new string, similar to concatenation using the + operator. Like be' +
                'fore, (hell.concat(o)) creates a new string "Hello", which is a different object' +
                ' from hello. Therefore, the condition is false, and 3 will not be printed.\n\nTh' +
                'e substring(0, 4) method creates a new string "Hell" from hello. This is a new o' +
                'bject in memory, so the condition is false, and 4 will not be printed.\n\n.equal' +
                's() method is used for comparing the content of the strings. Since the content o' +
                'f o is "o", this condition is true, and 5 will be printed.\n\nThe trim method re' +
                'moves leading and trailing spaces from the string, so the trimmed version become' +
                's "o". Since the content of o is also "o", this condition is true, and 6 will be' +
                ' printed.\n\nIn total, the output will be: 256'
    }, {
        id: 178,
        category: 2,
        isImage: false,
        title: 'What is/are true about immutable objects?',
        answers: [
            {
                id: 1,
                content: 'Immutable object can be modified',
                correct: false
            }, {
                id: 2,
                content: 'Immutable object cannot be modified',
                correct: true
            }, {
                id: 3,
                content: 'String is immutable',
                correct: true
            }, {
                id: 4,
                content: 'StringBuilder is immutable',
                correct: false
            }, {
                id: 5,
                content: 'LocalDate is immutable',
                correct: true
            }
        ],
        explanation: 'Immutable in java means that once an object is created, we cannot change its con' +
                'tent.\n\nIn Java, all the wrapper classes (like Integer, Boolean, Byte, Short), ' +
                'String class and LocalDate LocalDateTime LocalTime are immutables.'
    }, {
        id: 179,
        category: 2,
        isImage: false,
        title: "What will be printed in console?",
        image: task179,
        answers: [
            {
                id: 1,
                content: 'John,Hello world',
                correct: true
            }, {
                id: 2,
                content: 'john,hello world',
                correct: false
            }, {
                id: 3,
                content: 'Hell John,o world',
                correct: false
            }, {
                id: 4,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'sb.append("Hello"): Appends the string "Hello" to the StringBuilder object.\n\n.' +
                'insert(5, " world"): Inserts the string " world" at index 5 (after o) within the' +
                ' StringBuilder object.The StringBuilder now contains "Hello world".\n\n.insert(0' +
                ', "John,"): Inserts the string "John," at index 0 (the beginning) within the Str' +
                'ingBuilder object. The StringBuilder now contains "John,Hello world".\n\nsb.toSt' +
                'ring().toLowerCase(): Converts the StringBuilder to a String object and then cal' +
                'ls the toLowerCase() method on the string.\n\nHowever, this result is discarded ' +
                'because the return value is not assigned back to any variable.\n\nSystem.out.pri' +
                'ntln(sb);: Prints the content of the StringBuilder object, which is "John,Hello ' +
                'world".'
    }, {
        id: 180,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task180,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '2',
                correct: false
            }, {
                id: 3,
                content: 'Compilation failed',
                correct: true
            }, {
                id: 4,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'if (java == sb) { ... }: This if-statement attempts to compare the reference equ' +
                'ality between a String object (java) and a StringBuilder object (sb).\n\nSince t' +
                'hese are two different types of objects, the comparison is not valid, and it wil' +
                'l result as a compilation error.'
    }, {
        id: 181,
        category: 2,
        isImage: false,
        title: "What will be printed in console?",
        image: task181,
        answers: [
            {
                id: 1,
                content: '456790',
                correct: false
            }, {
                id: 2,
                content: '3456790',
                correct: true
            }, {
                id: 3,
                content: '456',
                correct: false
            }, {
                id: 4,
                content: '123456789',
                correct: true
            }, {
                id: 5,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'sb.append(0). delete(0, 2). deleteCharAt(5); \n\nappend(0): Appends the characte' +
                'r 0 to the end of the StringBuilder.\n\ndelete(0, 2): Deletes characters at indi' +
                'ces 0 and 1 (inclusive).The StringBuilder now contains "34567890".\n\n.deleteCha' +
                'rAt(5): Deletes the character at index 5. The StringBuilder now contains "345679' +
                '0".\n\njava. substring( 0 , 7). substring(1,6). substring(2);\n\njava.substring(' +
                '0, 7):1234567\n\n"1234567".substring(1, 6):23456\n\n"23456".substring(2):456.\n' +
                '\nHowever since there is no assign variable and string object is immutable, resu' +
                'lt of java will be remain the same.'
    }, {
        id: 182,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task182,
        answers: [
            {
                id: 1,
                content: '9',
                correct: false
            }, {
                id: 2,
                content: '2',
                correct: false
            }, {
                id: 3,
                content: '4',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: true
            }, {
                id: 5,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'System.out.println(java.length) line attempts to print the length of the java st' +
                'ring.\n\nHowever, the correct method to get the length of a String is length(), ' +
                'not length and therefore it throws compilation error.\n\nIf fixed then the ouput' +
                ' will be 9, 2, 4 '
    }, {
        id: 183,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task183,
        answers: [
            {
                id: 1,
                content: '2345',
                correct: true
            }, {
                id: 2,
                content: '234567',
                correct: true
            }, {
                id: 3,
                content: ' ',
                correct: true
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }, {
                id: 5,
                content: 'Exception is thrown',
                correct: true
            }
        ],
        explanation: 'System.out.println(java.substring(1, 5));: This line prints a substring of the j' +
                'ava string starting at index 1 (inclusive) and ending at index 5 (exclusive). Th' +
                'e substring will be "2345".\n\nSystem.out.println(java.substring(1, 7));: This l' +
                'ine prints a substring of the java string starting at index 1 (inclusive) and en' +
                'ding at index 7 (exclusive). The substring will be "234567".\n\nSystem.out.print' +
                'ln(java.substring(7, 7));: This line attempts to print a substring starting and ' +
                'ending at index 7. However, since the end index is equal to the start index, the' +
                ' resulting substring is an empty string "".\n\nSystem.out.println(java.charAt(10' +
                '));: This line attempts to access the character at index 10 of the java string. ' +
                'However, the valid indices for the java string are from 0 to 8 (inclusive), as i' +
                't has a length of 9 characters. Trying to access an index beyond this range will' +
                ' result in an IndexOutOfBoundsException.'
    }, {
        id: 184,
        category: 3,
        isImage: false,
        title: "What is the value of amount?",
        image: task184,
        answers: [
            {
                id: 1,
                content: '2',
                correct: false
            }, {
                id: 2,
                content: '4',
                correct: false
            }, {
                id: 3,
                content: '6',
                correct: false
            }, {
                id: 4,
                content: '8',
                correct: true
            }, {
                id: 5,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'amount = amount + java.substring(1, 3).length();: Calculates the length of the s' +
                'ubstring "23" (starting from index 1 and ending at index 3-1=2) and adds it to a' +
                'mount. amount becomes 0 + 2 = 2.\n\namount = amount + java.substring(1, 5).lengt' +
                'h();: Calculates the length of the substring "2345" (starting from index 1 and e' +
                'nding at index 5-1=4) and adds it to amount. amount becomes 2 + 4 = 6.\n\namount' +
                ' = amount + java.substring(3, 5).length();: Calculates the length of the substri' +
                'ng "45" (starting from index 3 and ending at index 5-1=4) and adds it to amount.' +
                ' amount becomes 6 + 2 = 8.\n\nSystem.out.println(amount);: Prints the value of t' +
                'he amount variable, which is now 8.'
    }, {
        id: 185,
        category: 3,
        isImage: false,
        title: task185,
        answers: [
            {
                id: 1,
                content: '123456789',
                correct: true
            }, {
                id: 2,
                content: '123456789false',
                correct: true
            }, {
                id: 3,
                content: '12+3456789',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }, {
                id: 5,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'This code snippet doesnot contain any compilation errors and executes just fine.' +
                '\n\nsb.append(false).insert(2, "+");: Performs a series of operations on the Str' +
                'ingBuilder object sb; append(false): Appends the string representation of false,' +
                ' which is "false", to the end of the StringBuilder. The StringBuilder now contai' +
                'ns "123456789false".\n\ninsert(2, "+"): Inserts the character  at index 2 within' +
                ' the StringBuilder. The StringBuilder now contains "12+3456789false".\n\nsb.dele' +
                'te(2, 3);: Deletes the character at index 2 (inclusive) within the StringBuilder' +
                '. After this operation, the StringBuilder contains "123456789false" again.\n\nSy' +
                'stem.out.println(java);: Prints the original value of the java string, which is ' +
                '"123456789".\n\nSince there were no changes applied to java variable, it result ' +
                'remains the same.'
    }
]