export const task39 = `
public class Question{

    public static void main(String[ args) {
        try {
            System.out.println(5*2/0);
        }  catch (ArithmeticException e) {
            System.out.println(7*2/0);
        } finally {
            String s=null;
            System.out.println(s.length());
        }
    }

}
`;

export const task40 = `
class Parent{

    void ml () throws FileNotFoundException {
        System.out.println("Parent class method");
    }

}

class Child extends Parent{

    void ml () throws IOException {
        System.out.println("Child class method");
    }

}

public class Question{

    public static void main(String[] args) {
        Parent p = new Child();
        p.ml();
    }

}
`;

export const task41 = `
public class Question{

Question(int i){ System.out.println("int"); }

Question(Integer i){ System.out.println("Integer");}

Question(Double d){ System.out.println("Double");}

Question(Object i){ System.out.println("Object");}

public static void main(String  args) {
    new Question(7);
    new Question (Integer.valueOf("7"));
}
`;

export const task42 = `
interface QuestionIn{
    int x=10;
}

public class Question implements Interf{

    public static void main(String  args) {
        x=100;
        System.out.println(x);
    }
}
`;

export const task43 = `
class Parent{

    Parent () {
        System.out.println("A");
    }

    public void ml (){
        System.out.println("B");
    }

}

class Child extends Parent{

    Child(int i) {
        System.out.println("C");
    }
}

public class Question{

    public static void main(String[] args) {
        Parent p = new Child();
        p.ml();
    }
`;

export const task47 = `
public class Question{

    public static void main(String [] args){
        int[] numbers = {1, 2, 3, 4, 5};
        int sum=0;
        for (int i=0; i < numbers.size; i++) {
            sum += numbers[i];
        }
        System.out.println("Sum of numbers: + sum);
    }
}
`;

export const task48 = `
public class Question{

    public static void main(String  args) {

        int z=0;

        if (++z < 0 && z++ > 1){
            System.out.print("1");
        }

        {
            System.out.print("2");
        }

        System.out.print(z);
    }

}
`;

export const task49 = `
public class Question{

    public static void main(String[] args) {

        do{
            int x=0;
            System.out.print(++x+" ");
        }
        while(x<=5);

    }
}
`;

export const task51 = `
public class Question{

    public static void main(String[] args) {
        int var=7;
        String response = (var++>10)? "var is > 10" : (--var > 0) ? "var is > 0" : "var is < 0";
        System.out.println(response);
        System.out.println(var);
    }

}
`;

export const task52 = `
public class Question{

    public static void main(String[] args) {

        int i = 5;
        do {
            while (++i<7) {
                System.out.print(++i);
            }
        }while (++i<15);

    }

}
`;

export const task53 = `
public class Question{

    public static void main(String[] args) {
        int i=1;
        while (++i<5){
            System.out.print(i++);
        }
    }
}
`;

export const task54 = `
public class Question{
    public static void main(String[ args) {
        int x = 40;
        int total = 0;
        switch(x){
            default: 
                total++;
                System.out.println("0");
            case 10:
                total++;
                System.out.println("10");
                break;
            case 20:
                total++;
                System.out.println("20");
            case 30:
                total++;
                System.out.println("30");
        }
        System.out.println(total);
    }
}
`;

export const task55=`
public class Question {
    public static void main(String[] args) {
        byte b = 50;
        char ch = 50;
        switch (b + ch) {
            case 10:
                System.out.println(10);
            case 100:
                System.out.println(100);
            case 1000:
                System.out.println(1000);
        }
    }
}
`;

export const task56=`
public class Question {
    public static void main(String[] args) {
        int x = 0;
        int y = 0;
        y = ++x;
        boolean bl = x > y ? true : false;
        if (bl) {
            y = x++;
            System.out.println("Hello = " + x);
        } else {
            x = ++y;
            System.out.println("Hi = " + y);
        }
    }
}
`;

export const task57=`
public class Question {
    public static void main(String args[]) {
        String str = new String("Hello World!");
        StringBuffer sb = (StringBuffer) str;
        System.out.println(sb);
    }
}
`;


export const task58=`
public class Question {
    public static String whatIsPrinted() {
        try {
            return "TRY";
        } catch (ArithmeticException e) {
            return "CATCH";
        } finally {
            return "FINALLY";
        }
    }

    public static void main(String args[]) {
        System.out.print(whatIsPrinted());
    }
}
`;


export const task60=`
interface Interf {
    abstract void ml();
}

abstract class Child implements Interf {
    modifier1 abstract void ml();
}

class Parent extends Child {
    modifier2 void ml() {}
}
`;


export const task62=`
class Parent {
    Parent() throws RuntimeException {
        System.out.print("Parent-");
    }
}

class Child extends Parent {
    Child() throws Exception {
        System.out.print("Child");
    }
}

public class Question {
    public static void main(String[] args) throws Exception {
        new Child();
    }
}
`;


export const task63=`
public class Question {
    public static void main(String[] args) {
        int x = 10;
        boolean b = x++ + ++x > 25;
        if (false)
            System.out.println(x++ + ++x);
        System.out.println(x);
    }
}
`;

export const task64=`
public class Question {
    public static void main(String args[]) {
        LocalDate date = LocalDate.parse("2023-02-07");
        System.out.println(date.plusMonths(-7));
    }
}
`;

export const task65=`
public class Parent {
}

______ class Child {
}
`;


export const task66=`
public class Question {
    public static void main(String[] args) {
        ArrayList<Integer> lists = new ArrayList<Integer>();
        lists.add(1);
        lists.add(2);
        lists.add(3);
        lists.remove(2);
        System.out.println(lists);
    }
}`;


export const task67=`
public class Question {
    public static void main(String[] args) {
        String s = new String("JavaOracleExam!");
        s = s.substring(2, 10);
        s = s.substring(2, 7);
        s = s.substring(2, 5);
        System.out.println(s);
    }
}
`;


export const task68=`
public class Question {
    public static void main(String[] args) {
        LocalDate dt = LocalDate.of(2002, Month.JANUARY, 15);
        dt.plusMonths(2);
        dt.plusHours(10);
        System.out.println(dt);
    }
}

`;


export const task69=`
class Parent {
    Parent() {
        System.out.println("Parent");
    }

    public void ml() {
        System.out.println("Parent ml");
    }
}

class Child extends Parent {
    Child() {
        System.out.println("Child");
    }

    protected void ml() {
        System.out.println("Childml");
    }
}

public class Question {
    public static void main(String[] args) {
        Child p = new Child();
        p.ml();
    }
}
`;
