import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { javaCode222, javaCode223, javaCode224, javaCode225 } from "./snips/basicSnips";

export const classes : Questions[] = [
    {
        id: 221,
        category: 7,
        isImage: false,
        title: 'Which modifiers are automatically applied to all interface methods?',
        answers: [
            {
                id: 1,
                content: 'default',
                correct: false
            }, {
                id: 2,
                content: 'void',
                correct: false
            }, {
                id: 3,
                content: 'public',
                correct: true
            }, {
                id: 4,
                content: 'static',
                correct: false
            }, {
                id: 5,
                content: 'protected',
                correct: false
            }
        ],
        explanation: 'All interface methods are implicitly public.\n \nInterface methods can be declar' +
                'ed as static or default, but they are not implicitly added.\n \nvoid is not a mo' +
                'difier, it is a return type.\n \nAbstract on the other hand is tricky part, in p' +
                'ast all interface methods were abstract by default, but now with bringing static' +
                ' modifier, abstract cannot be defined as default.'
    }, {
        id: 222,
        category: 7,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode222,
        answers: [
            {
                id: 1,
                content: 'No Compilation errors',
                correct: false
            }, {
                id: 2,
                content: '1 compilation error',
                correct: true
            }, {
                id: 3,
                content: '2 compilations error',
                correct: false
            }, {
                id: 4,
                content: 'If compilation errors are removed we get -> Parent Child int',
                correct: false
            }, {
                id: 5,
                content: 'If compilation errors are removed we get -> Parent',
                correct: true
            }, {
                id: 6,
                content: 'If compilation errors are removed we get -> Parent Child byte',
                correct: false
            }
        ],
        explanation: 'This java code snippet has 1 compilation error.\n \npublic void Child(int x){} i' +
                's not a constructor, but it is a method.\n \nWhen we remove Parent ch1 = new Chi' +
                'ld(67) from snippet, it works just fine and prints Parent.\n \n'
    }, {
        id: 223,
        category: 7,
        isImage: false,
        title: "What should be inserted into blank space?",
        image: javaCode223,
        answers: [
            {
                id: 1,
                content: 'Son',
                correct: false
            }, {
                id: 2,
                content: 'Daughter',
                correct: false
            }, {
                id: 3,
                content: 'Infant',
                correct: true
            }, {
                id: 4,
                content: 'Parent',
                correct: true
            }, {
                id: 5,
                content: 'IGrow',
                correct: true
            }, {
                id: 6,
                content: 'Object',
                correct: true
            }
        ],
        explanation: 'Blank line can be filled with any class or interface which is a supertype of Inf' +
                'ant -> Parent, Object(all classes inherited from Object).\n \nIGrow is implement' +
                'ed in Parent class, that is the reason that it is applicable.\n \nInfant is the ' +
                'same class so it also applies.\n \nSon and Daughter are not super classes of Inf' +
                'ant, so therefore both of them are not applicable.'
    }, {

        id: 224,
        category: 7,
        isImage: false,
        title: "Does this code has compilation errors and if yes, how to fix it?",
        image: javaCode224,
        answers: [
            {
                id: 1,
                content: 'No compilation error, code is fine',
                correct: false
            }, {
                id: 2,
                content: 'Add to Parent class public int getAge(){return 1;}',
                correct: true
            }, {
                id: 3,
                content: 'Add to Human class public abstract int getAge();',
                correct: false
            }, {
                id: 4,
                content: 'No compilation error, but runtime exception is thrown',
                correct: false
            }
        ],
        explanation: 'Code fails to compile because interface getAge() method has not been implemented' +
                '.\n \nMethod getAge() in interface should either be mapped as static instead of ' +
                'abstract, with relevant body. Another solution would be that Human or Parent cla' +
                'ss should have implmented like  public int getAge(){return 1;}, '
    }, {
        id: 225,
        category: 7,
        isImage: false,
        title: "What is wrong with the code?",
        image: javaCode225,
        answers: [
            {
                id: 2,
                content: 'Declaration of step variable causes compilation error',
                correct: false
            }, {
                id: 3,
                content: 'getAge() method causes compilation error',
                correct: true
            }, {
                id: 4,
                content: 'incrementAge() method causes compilation error',
                correct: true
            }, {
                id: 1,
                content: 'No compilation error, code is fine',
                correct: false
            }
        ],
        explanation: 'Variable is properly declared and is not source of compilation error.\n \nStatic' +
                ' methods declared in interfaces required to have a body, when it is not provided' +
                ' it gives compilation error.\n \nOn other hand when method in interface do not ha' +
                've static declaration it should not have body part, therefore incrementAge is cau' +
                'sing compilation error as well.'
    }, {
        id: 226,
        category: 7,
        isImage: false,
        title: 'What is the true for both interfaces and abstract classes?',

        answers: [
            {
                id: 1,
                content: 'Both extends to java.lang.Object',
                correct: false
            }, {
                id: 2,
                content: 'Both can contain static methods',
                correct: true
            }, {
                id: 3,
                content: 'Both can contain public static final variables',
                correct: true
            }, {
                id: 4,
                content: 'Methods inside of them assumed to be abstract',
                correct: false
            }, {
                id: 5,
                content: 'Both can be extended using extends keyword',
                correct: true
            }
        ],
        explanation: 'Statement 1 ->Interface donot inherit from java.lang.Object -> false. \n \nState' +
                'ment 2 -> This is true since both of them can contain such methods -> true.\n \nS' +
                'tatement 3 -> All variables in interfaces are static constants, you should be awa' +
                're of that. However in abstract classes they arenot forced to be final, but they' +
                ' can be so -> true.\n \nStatement 4 -> Abstract class can contain concrete method' +
                's so -> false.\n \nStatement 5 -> public interface IGrow extends Itest(another inter' +
                'face) and abstract class AbstractCL extends ParentAbstractCL(another abstract cl' +
                'ass) -> true '
    }
]