import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { NavigationContainer } from '@react-navigation/native';

import Question from '../components/exams/Question';
import Content from '../components/chapters/Content';

import ListQuestions from '../components/exams/ListQuestions';
import ListMarked from '../components/marked/ListMarked';
import Result from '../components/exams/Result';
import Response from '../components/exams/Response';
import SpecificQuestion from '../components/statistics/SpecificQuestion';
import ListStatisticQuestions from '../components/statistics/ListStatisticQuestions';
import Tmp from '../components/Tmp';
import { MyTabsNew } from './Tabs';
import Marked from '../components/marked/Marked';
import { horizontalAnimation, verticalAnimation } from './design/Animations';

const Stack = createStackNavigator();


export default function Nav() {
  return (
    <NavigationContainer>
      <Stack.Navigator  initialRouteName="MyTabsNew">
        <Stack.Screen name="MyTabsNew" component={MyTabsNew}  options={{ headerShown: false, headerLeft: ()=> null }}  />

        <Stack.Screen name="ListQuestions" component={ListQuestions} options={{ headerShown: false }}  />
        <Stack.Screen name="Question" component={Question} options={{ headerShown: false }} />
        <Stack.Screen name="Result" component={Result} options={{ headerShown: false }}   />
        <Stack.Screen name="Response" component={Response} options={{ headerShown: false}}  />

        
        <Stack.Screen name="ListStatisticQuestions" component={ListStatisticQuestions} options={horizontalAnimation} />
        <Stack.Screen name="SpecificQuestion" component={SpecificQuestion} options={{ headerShown: false }} />


        <Stack.Screen name="Marked" component={Marked} options={horizontalAnimation} />

        <Stack.Screen name="Tmp" component={Tmp} options={horizontalAnimation} />

        <Stack.Screen name="Content" component={Content} options={horizontalAnimation} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}