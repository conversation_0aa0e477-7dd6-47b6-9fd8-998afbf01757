import StorageService, { initExam, oldInitExam } from "./StorageService";
import { examHead }  from "../components/models/consts";
import { ExamHead, TypeOfExam } from "../components/models/Exams";

export class InitExamService{

    static async initPush(){
        await StorageService.post(initExam, examHead);
    }

    static async check(){
        await StorageService.delete(oldInitExam);    
        this.getAllExams().then( (data) => {
            if(data === null || data.length!==examHead.length){
                this.initPush();
            }   
        })
    }

    static async generalUpdate(updatedQuestions: ExamHead[]){
        await StorageService.post(initExam, updatedQuestions);
    }


    static async getAllExams():Promise<ExamHead[]>{
        var allInitExams =  StorageService.getData(initExam);
        return allInitExams;
    }

    static async getAllGeneralExams():Promise<ExamHead[]>{
        var generalExams:ExamHead[] = []
        var dataset = await this.getAllExams();
        for(var question of dataset){
            if(question.typeOfExam===TypeOfExam.Exam){
                generalExams.push(question);
            }
        }
        return generalExams;
    }

    
    static async getAllChapterExams():Promise<ExamHead[]>{
        var chapterExams:ExamHead[] = []
        var dataset = await this.getAllExams();
        for(var question of dataset){
            if(question.typeOfExam===TypeOfExam.Chapter){
                chapterExams.push(question);
            }
        }
        return chapterExams;    
    }

    static async markAsOngoing(id: number):Promise<void>{
        var dataset:ExamHead[] = await this.getAllExams();
        for(var exam of dataset){
            if(exam.id === id){
                exam.ongoing = true;
                break;
            }
        }
        await this.generalUpdate(dataset);        
    }

    static async markAsCompleted(id: number):Promise<void>{
        var dataset:ExamHead[] = await this.getAllExams();
        for(var exam of dataset){
            if(exam.ongoing && exam.id === id){
                exam.ongoing = false;
                exam.completed = true;
                break;
            }
        }
        await this.generalUpdate(dataset);        
    }

    static async existsOngoingExam():Promise<boolean>{
        var onGoing:boolean = false;
        var dataset = await this.getAllExams();
        for(var question of dataset){
            if(question.ongoing){
                onGoing = true;
                break;
            }
        }
        return onGoing;   
    }

    
    static async getOngoingExam():Promise<ExamHead>{
        var dataset:ExamHead[] = await this.getAllExams();
        for(var exam of dataset){
            if(exam.ongoing){
                return exam;   
            }
        }
        return null;
        
    }







} 