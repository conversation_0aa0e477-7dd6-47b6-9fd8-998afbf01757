import React, { Component } from "react";
import {
  View,
  FlatList,
  Text,
  TouchableOpacity,
} from "react-native";
import { categories } from "../models/Categories";

import { AppBar, IconButton } from "@react-native-material/core";
import {
  basic,
  dataTypes,
  operators,
  arrays,
  constructors,
  dateTime,
  exceptions,
  inheritance,
  lambda,
  loops,
} from "./ContentDB";
import { generateRandomCode } from "../../service/RandomService";
import {
  BannerAd,
  BannerAdSize,
  TestIds,
} from "react-native-google-mobile-ads";
import { FontAwesome } from "@expo/vector-icons";
import { scale, ScaledSheet } from "react-native-size-matters";
import GradientContainer from "../../helpers/GradientContainer";
import { appColors } from "../../utils/appColors";

const adUnitId = __DEV__  ? TestIds.BANNER  : "ca-app-pub-5981144475529351/3452174828";

interface ChapterState {
}


class Chapters extends Component < {}, ChapterState >  {

  constructor(props) {
    super(props);
    this.state = {
    };
  }

  renderContent = (id: number) => {
    switch (id) {
      case 1:
        return basic;
      case 2:
        return dataTypes;
      case 3:
        return operators;
      case 4:
        return arrays;
      case 5:
        return loops;
      case 6:
        return constructors;
      case 7:
        return inheritance;
      case 8:
        return exceptions;
      case 9:
        return lambda;
      case 10:
        return dateTime;
    }
  };

  render() {
    return (
      <GradientContainer>
          <AppBar
            title="Chapters"
            transparent={true}
            titleStyle={{ fontFamily: "AmorriaBrush", fontSize:scale(25), }}
            contentContainerStyle={{
                marginTop:scale(35),
            }} 
            leading={(props) => (
              <IconButton
                icon={(props) => (
                  <FontAwesome name="book" color={"white"} size={30} />
                )}
                {...props}
              />
            )}
          />
          <FlatList
            contentContainerStyle={styles.listContainer}
            data={categories}
            horizontal={false}
            numColumns={2}
            keyExtractor={(item) => {
              return generateRandomCode();
            }}
            renderItem={({ item }) => {
              return (
                <TouchableOpacity
                  style={[
                    styles.card,
                    {
                      backgroundColor: appColors.white,
                    },
                  ]}
                  onPress={() =>
                    this.props.navigation.navigate("Content", {
                      title: item.title,
                      content: this.renderContent(item.id),
                    })
                  }
                >
                  <View style={styles.cardHeader}>
                    <Text style={styles.title}>{item.title}</Text>
                    {item.icon}
                  </View>
                </TouchableOpacity>
              );
            }}
          />
  
        <View style={styles.ads}>
          <BannerAd
            unitId={adUnitId}
            size={BannerAdSize.INLINE_ADAPTIVE_BANNER}
            requestOptions={{
              requestNonPersonalizedAdsOnly: false,
            }}
          />
        </View>
      </GradientContainer>
    );
  }
}
const styles = ScaledSheet.create({
  listContainer: {
    alignItems: "center",
  },
  card: {
    marginHorizontal: scale(5),
    marginVertical: scale(10),
    flexBasis: "45%",
    borderRadius:scale(50),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: scale(5),
  },
  cardHeader: {
    paddingVertical: scale(17),
    paddingHorizontal: scale(16),
    borderTopLeftRadius: scale(1),
    borderTopRightRadius: scale(1),
    flexDirection: "column",
    gap:scale(5),
    alignItems: "center",
    justifyContent: "center",
  },

  title: {
    fontSize: scale(20),
    flex: 1,
    fontFamily: "LatoLight",
    textAlign: "center",
  },
  icon: {
    height: scale(20),
    width: scale(20),
  },
  ads: {
    maxHeight: "10%",
    width: "100%",
  },
});
export default Chapters;
