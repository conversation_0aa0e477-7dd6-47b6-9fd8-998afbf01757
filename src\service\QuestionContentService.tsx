import { Answer, Questions } from "../components/models/Questions";
import { QuestionService } from "./QuestionService";
import StorageService, {answerSheet} from "./StorageService";

export interface AnswerSheet{
    examId:number;
    serialNumber:number;
    questionId:number;
    selectedAnswers:Answer[];
    allAnswers:Answer[];
    isWrong:boolean;
}

export class QuestionContentService{
    
    static async uploadNewAnswerSheet(answers:AnswerSheet[]){
        await StorageService.post(answerSheet, answers);
    }

    static async getAllAnswers():Promise<AnswerSheet[]>{
        return StorageService.getData(answerSheet);
    }

    static async generateAnswerSheet(examId:number, amountOfQuestion?:number):Promise<void>{
        var dataset:Questions[] =[];
        if(examId===100){
            dataset = QuestionService.getRandomQuestions(amountOfQuestion);
        }else{
            dataset = QuestionService.shuffleArray(QuestionService.getDataSet(examId));
        }
        
        var answerSheet: AnswerSheet[] = [];
        var serial = 0;
        for(var question of dataset){
            var currAnswer : AnswerSheet = {
                examId:examId,
                serialNumber:serial,
                questionId: question.id,
                selectedAnswers: [],
                allAnswers: question.answers,
                isWrong:true
            }
            answerSheet.push(currAnswer);
            serial = serial + 1;
        }
        this.uploadNewAnswerSheet(answerSheet);
    }

    static async getAnAnswerSheet(serialNumber:number): Promise<AnswerSheet>{
        return this.getAllAnswers().then((answerForm) => {
            for(var ans of answerForm){
                if(ans.serialNumber===serialNumber){
                    return ans;
                }
            }
        });
    } 



    static async uploadAnAnswer(questionId:number, answers: Answer[]){
        if(answers.length === 0){
            return;
        }else{
            this.getAllAnswers().then((answerForm) => {
                for(var ans of answerForm){
                    if(ans.questionId===questionId){
                        ans.selectedAnswers = answers;
                        break;
                    }
                }
                
                this.uploadNewAnswerSheet(answerForm);
            });
        }

    }
    
    static async updateAnswerSheet(newAnswerSheet: AnswerSheet):Promise<void>{
        return this.getAllAnswers().then((answerForm) => {
            for(var ans of answerForm){
                if(ans.serialNumber===newAnswerSheet.serialNumber){
                    ans.selectedAnswers = newAnswerSheet.selectedAnswers;
                    break;
                }
            }            
            return this.uploadNewAnswerSheet(answerForm);
        });
    }

    static async finalStep(answerSheets){
        await this.uploadNewAnswerSheet(answerSheets);
    }

    static async deleteAnswerForm(){
        await StorageService.delete(answerSheet);
    } 


}