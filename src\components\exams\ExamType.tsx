import React, { Component, ReactElement } from "react";
import { AntDesign, FontAwesome } from "@expo/vector-icons";
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions
} from "react-native";
import Modal from "react-native-modal";
import { AppBar, IconButton } from "@react-native-material/core";
import Icon from "@expo/vector-icons/MaterialCommunityIcons";
import { generateRandomCode } from "../../service/RandomService";
import { ExamHead, ExamBody, TypeOfExam } from "../models/Exams";
import { InitExamService } from "../../service/InitExamService";
import { generalExam } from "../models/consts";
import { ScrollView } from "react-native-gesture-handler";
import { scale, ScaledSheet, verticalScale } from "react-native-size-matters";
import GradientContainer from "../../helpers/GradientContainer";
import EntryExam from "../../helpers/modals/EntryExam";
import { appColors } from "../../utils/appColors";
import BlinkingText from "../../helpers/BlinkingText";
import LoadingDots from "react-native-loading-dots";
import CoffeeBanner from "../../helpers/modals/CoffeeBanner";


interface Item {
  name: string;
  icon: string;
  desc: string;
  typeOfExam: TypeOfExam;
}

interface General {
  listExamsIsOpen: boolean;
  listChaptersIsOpen: boolean;
  isModalVisible: boolean;
  title: string;
  examId: number;
  listOfExams: ExamHead[];
  loading: boolean;
  onGoing: ExamHead;
  items: Item[];
}

const deviceHeight = Dimensions.get("window").height;
const deviceWidth = Dimensions.get("window").width;

class ExamType extends Component<{}, General> {
  constructor(props) {
    super(props);
    this.state = {
      listExamsIsOpen: false,
      listChaptersIsOpen: false,
      isModalVisible: false,
      title: "",
      examId: 0,
      listOfExams: [],
      loading: true,
      onGoing: null,
      items: [
        {
          name: "General exam",
          desc: "Simulation of an exam",
          icon: "bookshelf",
          typeOfExam: TypeOfExam.Exam,
        },
        {
          name: "Chapterwise",
          desc: "Sorted by chapters",
          icon: "format-list-group",
          typeOfExam: TypeOfExam.Chapter,
        },
        {
          name: "Chapterwise",
          desc: "Sorted by chapters",
          icon: "format-list-group",
          typeOfExam: TypeOfExam.Random,
        }
      ],
    };
  }

  togglelistExamsIsOpen = () => {
    this.setState((prevState) => ({
      listExamsIsOpen: !prevState.listExamsIsOpen,
    }));
  };

  togglelistChaptersIsOpen = () => {
    this.setState((prevState) => ({
      listChaptersIsOpen: !prevState.listChaptersIsOpen,
    }));
  };

  componentDidMount() {
    this.loadType = this.loadType.bind(this);
    this.loadType();
    this.props.navigation.addListener("focus", this.loadType);
  }

  async loadType() {
    var ongoingExam = InitExamService.getOngoingExam();

    var examHeadList = InitExamService.getAllExams();
    examHeadList.then((data) => {
      this.setState({ listOfExams: data });
      ongoingExam.then((onGoing) => {
        if (onGoing) {
          this.setState({ onGoing: onGoing });
        }
        this.setState({ loading: false });
      });
    });
  }


  closeModal = () => {
    this.setState({ isModalVisible: false });
  };

  showModal = (examId: number, title: string) => {
    var { onGoing } = this.state;
    if (onGoing && onGoing.id === examId) {
      this.props.navigation.navigate("ListQuestions");
    } else {
      this.setState({ examId: examId, title: title, isModalVisible: true });
    }
  };

  renderAppHeader = () => {
    return(
      <AppBar
            transparent={true}
            titleStyle={{ fontFamily: "AmorriaBrush", fontSize:scale(25), }}
            contentContainerStyle={{
                marginTop:scale(35),
            }} 
            title="Exam type"
            leading={(props) => (
              <IconButton
                icon={(props) => (
                  <FontAwesome
                    name="graduation-cap"
                    color={"white"}
                    size={scale(30)}
                  />
                )}
                {...props}
              />
            )}
          />
    )
  }


  renderName = (renderedExam: ExamHead, examBody: ExamBody): ReactElement => {
    var { onGoing } = this.state;

    var text = <Text style={[styles.groupName, {color:appColors.white}]}>{examBody.name}</Text>;
    var textExam =   <BlinkingText text={examBody.name} fontSize={scale(20)} />;
  
    var textDisabled = (
      <Text style={styles.disabledTitle}>{examBody.name}</Text>
    );

    if (onGoing !== null) {
      if (onGoing.id === renderedExam.id) {
        return textExam;
      }
    }
    if (examBody.enabled) {
      return text;
    }
    return textDisabled;
  };


  renderExam = (item: ExamHead) => {
    var examBody: ExamBody = null;
    examBody = generalExam.get(item.id);
    return (
      <React.Fragment key={generateRandomCode()}>
        <TouchableOpacity
          style={styles.container}
          onPress={() =>
            examBody.enabled && this.showModal(item.id, examBody.name)
          }
        >
          <View style={styles.rowDirection}>
            <View style={styles.iconContainer}>
              {
                React.cloneElement(examBody.icon, { color: appColors.white })
              }
            </View>
            <View style={styles.content}>
              <View style={styles.mainContent}>
                <View style={styles.text}>
                  {this.renderName(item, examBody)}
                </View>
                <Text style={styles.timeAgo}>{examBody.descr}</Text>
              </View>
            </View>
            <View style={styles.iconContainer}>
              {examBody.enabled 
                ?
                  <AntDesign
                    name="caretright"
                    color={appColors.white}
                    size={scale(25)}
                    style={styles.iconCenter}
                  />
                
                :
                  <AntDesign
                    name="lock"
                    color={appColors.white}
                    size={scale(30)}
                    style={styles.iconCenter}
                  />
                
              
              }
            </View>
          </View>

          <View style={styles.separator} />
        </TouchableOpacity>
      </React.Fragment>
    );
  };

  render() {
    var {
      loading,
      listOfExams,
      listChaptersIsOpen,
      listExamsIsOpen,
      isModalVisible,
      title,
      examId,
      items,
      onGoing,
    } = this.state;
    if (loading) {
      return (
        <GradientContainer>
            {this.renderAppHeader()}
            <View style={styles.dotsContainer}>
            <LoadingDots
                size={scale(35)}
                colors={[appColors.white, appColors.white, appColors.white]}
                dots={3}
                borderRadius={scale(15)}/>
            </View>
        </GradientContainer>
      );
    }
    return (
      <GradientContainer>
        <View style={{ height: "90%" }}>
          {this.renderAppHeader()}
            <Modal
              onBackdropPress={() => this.setState({ isModalVisible: false })} 
              isVisible={isModalVisible}
              coverScreen={false}
              deviceHeight={deviceHeight}
              deviceWidth={deviceWidth}
            >
            <EntryExam
              title={title}
              examId={examId}
              closeModal={() => this.closeModal()}
              navigation={this.props.navigation}
              callUnmountComponent={() => this.componentWillUnmount()}
            />
          </Modal>
          <ScrollView>
            <TouchableOpacity
              style={styles.container}
              key={generateRandomCode()}
              onPress={this.togglelistExamsIsOpen}
            >
              <View style={styles.rowDirection}>
                <View style={styles.iconContainer}>
                    <Icon name={items[0].icon} color={appColors.white} size={scale(40)} />
                </View>
                <View style={styles.content}>
                  <View style={styles.mainContent}>
                    <View style={styles.text}>
                        {onGoing && onGoing.typeOfExam === items[0].typeOfExam ? (
                            <BlinkingText text={items[0].name} fontSize={scale(20)} />
                        ) : (
                            <Text style={[styles.groupName, {color: appColors.white}]}>{items[0].name}</Text>
                        )}
                    </View>
                    <Text style={styles.timeAgo}>{items[0].desc}</Text>
                  </View>
                </View>
                <View style={styles.iconContainer}>
                  <AntDesign
                    name={listExamsIsOpen ? "downcircleo" : "rightcircleo"}
                    color={appColors.white}
                    size={scale(35)}
                    style={styles.iconCenter}
                  />
                </View>
              </View>
            </TouchableOpacity>
            <View style={styles.separator} />
            {listExamsIsOpen &&
              listOfExams
                .filter((exam) => exam.typeOfExam === items[0].typeOfExam)
                .map(this.renderExam)}
            <TouchableOpacity
              style={styles.container}
              key={generateRandomCode()}
              onPress={this.togglelistChaptersIsOpen}
            >
              <View style={styles.rowDirection}>
                <View style={styles.iconContainer}>
                    <Icon name={items[1].icon} color={appColors.white} size={scale(40)} />
                </View>
                <View style={styles.content}>
                  <View style={styles.mainContent}>
                    <View style={styles.text}>                     
                      {onGoing && onGoing.typeOfExam === items[1].typeOfExam ? (
                            <BlinkingText text={items[1].name} fontSize={scale(20)} />
                        ) : (
                            <Text style={[styles.groupName, {color: appColors.white}]}>{items[1].name}</Text>
                        )}
                    </View>
                    <Text style={styles.timeAgo}>{items[1].desc}</Text>
                  </View>
                </View>
                <View style={styles.iconContainer}>
                  <AntDesign
                    name={listChaptersIsOpen ? "downcircleo" : "rightcircleo"}
                    color={appColors.white}
                    size={scale(35)}
                    style={styles.iconCenter}
                  />
                </View>
              </View>
            </TouchableOpacity>
            <View style={styles.separator} />
            {listChaptersIsOpen &&
              listOfExams
                .filter((exam) => exam.typeOfExam === items[1].typeOfExam)
                .map(this.renderExam)}
              {//--Random------------
              }

            <TouchableOpacity
              style={styles.container}
              key={generateRandomCode()}
              onPress={() =>
                this.showModal(100, 'Random')
              }
            >
              <View style={styles.rowDirection}>
                <View style={styles.iconContainer}>
                    <Icon name={'shuffle'} color={appColors.white} size={scale(40)} />
                </View>
                <View style={styles.content}>
                  <View style={styles.mainContent}>
                    <View style={styles.text}>
                        {onGoing && onGoing.id === 100 ? (
                            <BlinkingText text={"Random"} fontSize={scale(20)} />
                        ) : (
                            <Text style={[styles.groupName, {color: appColors.white}]}>Random</Text>
                        )}
                    </View>
                    <Text style={styles.timeAgo}>Arbitrarily selected</Text>
                  </View>
                </View>
                <View style={styles.iconContainer}>
                  <AntDesign
                    name={listExamsIsOpen ? "downcircleo" : "rightcircleo"}
                    color={appColors.white}
                    size={scale(35)}
                    style={styles.iconCenter}
                  />
                </View>
              </View>
            </TouchableOpacity>
            <View style={styles.separator} />
            {//--EndRandom------------
            }
          </ScrollView>
        </View>
        <View style={styles.ads}>
          <CoffeeBanner/>
        </View>
      </GradientContainer>
    );
  }
}

const styles = ScaledSheet.create({

  container: {
    padding: scale(8),
    width: "100%",
    flexDirection: "row",
    borderBottomWidth: scale(1),
    borderColor: "#FFFFFF",
    alignItems: "flex-start",
  },
  rowDirection: {
    flexDirection: "row",
  },
  padding: {
    paddingHorizontal: scale(5),
  },
  iconContainer: {
    flexDirection: "column",
    margin: scale(5),
    alignSelf: "center",
  },
  iconCenter: {},
  text: {
    marginBottom: scale(5),
    flexDirection: "row",
    flexWrap: "wrap",
  },
  content: {
    flex: 1,
    marginLeft: scale(10),
    flexDirection: "column",
  },
  mainContent: {
    marginRight: scale(60),
  },
  separator: {
    height: scale(1),
    backgroundColor: appColors.white,
  },
  timeAgo: {
    marginBottom: "5%",
    fontSize: scale(16),
    color: appColors.white,
    fontFamily: "LatoRegular",
  },
  groupName: {
    fontSize: scale(20),
    fontFamily: "AmorriaBrush",
  },

  disabledTitle: {
    fontSize: scale(20),
    color: appColors.white,
    fontFamily: "LatoRegular",
  },
  ads: {
    paddingTop: scale(7),
    minHeight: "10%",
    width: "100%",
  },
  dotsContainer: {
    marginHorizontal: scale(40),
    marginTop: verticalScale(190),
    width: '40%',
    alignContent: 'center',
    alignSelf: 'center',
    height: '15%',
    justifyContent: 'center',
  },
});

export default ExamType;
