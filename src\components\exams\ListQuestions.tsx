import { Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import React, { Component } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Text } from 'react-native';
import {   AppBar, IconButton} from "@react-native-material/core";
import { generateRandomCode } from '../../service/RandomService';
import { QuestionService } from '../../service/QuestionService';
import { AnswerSheet, QuestionContentService } from '../../service/QuestionContentService';
import { InitExamService } from '../../service/InitExamService';
import { BannerAd, BannerAdSize, TestIds } from 'react-native-google-mobile-ads';
import { scale, verticalScale } from 'react-native-size-matters';
import { appColors } from '../../utils/appColors';
import GradientContainer from '../../helpers/GradientContainer';
import LoadingDots from 'react-native-loading-dots';

interface GeneralState{
    answerSheets:AnswerSheet[];
    examId:number;
    loading:boolean;
    load:number;
}

const adUnitId = __DEV__ ? TestIds.BANNER : 'ca-app-pub-5981144475529351/3452174828';

class ListQuestions extends Component< {}, GeneralState >  {

    constructor(props:{}){
        super(props);
        this.state={
          answerSheets:[],
            examId:0,
            loading:true,
            load:0,
        }
    }

    componentDidMount(): void {
        this.loadQuestionSets();        
        this.loadQuestionSets = this.loadQuestionSets.bind(this);          
        this.props.navigation.addListener('focus', this.loadQuestionSets);
    }

    async loadQuestionSets() {
      try {
          const allForms = await QuestionContentService.getAllAnswers();
          const examHead = await InitExamService.getOngoingExam();
          this.setState({ examId: examHead.id, answerSheets: allForms, loading: false});        
      } catch (error) {
        // Handle errors here
      }
    }
    


  renderItemCustom = (item:AnswerSheet) => {
    let question = QuestionService.getQuestionById(item.questionId,item.examId);
    let meta = "";
    if(item.selectedAnswers.length>0){
      meta = '\u2714';
    }
    return(
      <TouchableOpacity
      style={styles.containerCustom}
      key={generateRandomCode()}
      onPress={() => this.props.navigation.navigate('Question',{id:item.serialNumber}) }  
      >
      <View style={styles.rowDirection}>
        <View style={styles.iconContainer}>
            {
              item.selectedAnswers.length>0
              ?
                <MaterialCommunityIcons name={'head-lightbulb'} color={appColors.white}  size={scale(45)} /> 
              :
                <MaterialCommunityIcons name={'head-question'} color={appColors.white}  size={scale(40)} /> 
            }
        </View>
        <View style={styles.content}>
          <View style={styles.mainContent}>
            <View style={styles.text}>                     
              <Text style={[styles.groupName]}>{"#"+question.id} {meta}</Text>
            </View>
            <Text style={styles.timeAgo}>{question.title}</Text>
          </View>
        </View>
        <View style={styles.iconContainer}>
            <MaterialCommunityIcons 
            name="chevron-right"
            color={appColors.white}
            size={scale(35)}
            style={styles.iconCenter}
          />
        </View>
      </View>
    </TouchableOpacity>
    )
  }

  render = () => {
    const {answerSheets, loading} = this.state;
    return (
      <GradientContainer>
      <ScrollView>
                <AppBar
                    titleStyle={{ fontFamily: "AmorriaBrush", fontSize:scale(25) }}
                    contentContainerStyle={{
                      marginTop:scale(35),
                    }} 
                    title={"Questions"}
                    transparent={true}
                    leading={(props) => (
                      <IconButton
                      onPress={() => this.props.navigation.navigate("MyTabsNew")}
                      icon={props => <Feather name="arrow-left-circle" size={scale(props.size+10)} color={props.color} />}
                        {...props}
                      />
                    )}
                    
                />
     
          { loading 
              ?
              <View style={styles.dotsContainer}>
                <LoadingDots
                    size={scale(35)}
                    colors={[appColors.white, appColors.white, appColors.white]}
                    dots={3}
                    borderRadius={scale(15)}/>
              </View>
             :
              answerSheets.map(this.renderItemCustom)
          }
      </ScrollView>
      <View style={styles.ads}>
        <BannerAd 
          unitId={adUnitId}
          size={BannerAdSize.INLINE_ADAPTIVE_BANNER}
          requestOptions={{
              requestNonPersonalizedAdsOnly:false
              }}                        
          />
      </View>
      </GradientContainer>
    );
  }
}



const styles = StyleSheet.create({
  rowDirection: {
    flexDirection: "row",
  },
  iconContainer: {
    flexDirection: "column",
    margin: scale(5),
    alignSelf: "center",
  },
  iconCenter: {},
  text: {
    marginBottom: scale(5),
    flexDirection: "row",
    flexWrap: "wrap",
  },
  content: {
    flex: 1,
    marginLeft: scale(3),
    marginTop: scale(5),
    flexDirection: "column",
  },
  mainContent: {
    marginRight: scale(5),
  },
  timeAgo: {
    marginBottom: "1%",
    fontSize: scale(14),
    color: appColors.white,
    fontFamily: "LatoRegular",
  },
  groupName: {
    fontSize: scale(16),
    fontFamily: "AmorriaBrush",
    color:appColors.white
  },
  containerCustom: {
    padding: scale(8),
    width: "100%",
    flexDirection: "row",
    borderBottomWidth: scale(1),
    borderColor: "#FFFFFF",
    alignItems: "flex-start",
  },
  avatar: {
    backgroundColor:'white',
  },
  ads:{
    maxHeight:'10%',
    width:'100%',
  },
  dotsContainer: {
    marginHorizontal: scale(40),
    marginTop: verticalScale(190),
    width: '40%',
    alignContent: 'center',
    alignSelf: 'center',
    height: '15%',
    justifyContent: 'center',
  },
});

export default ListQuestions;
