import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { javaCode1037, javaCode1038, javaCode1039 } from "./snippets/codeSnip";
export const dataTypeDB : Questions[] = [
    {
        id: 1037,
        category: 2,
        isImage: false,
        image: javaCode1037,
        title: "What will be printed in the console?",
        answers: [
            {
                id: 1,
                content: "*2** / *1**",
                correct: false
            }, {
                id: 2,
                content: "*2** / 1111",
                correct: false
            }, {
                id: 3,
                content: "1211 / *1**",
                correct: false
            }, {
                id: 4,
                content: "1211 / 1111",
                correct: true
            }, {
                id: 5,
                content: "1221 / 1111",
                correct: false
            }
        ],
        explanation: 'String one = "1"; assigns the string "1" to the variable one.\n \nString str = o' +
                'ne + "2" + one + one; concatenates the values of one, "2", one, and one to creat' +
                'e the string "1211". At this point, str contains the value "1211".\n \nstr.repla' +
                'ce("1", "*"); replaces all occurrences of "1" with "*" in the string str. Howeve' +
                'r, this operation does not modify the original string str. It returns a new stri' +
                'ng with the replacements, but you are not assigning it to any variable. So, the ' +
                'value of str remains "1211".\n \nString newStr = str.replace("2", "1"); replaces' +
                ' the "2" with "1" in the string str and assigns the result to the variable newSt' +
                'r. The value of newStr is "1111".\n \nFinally, System.out.print(str + " / " + ne' +
                'wStr); prints the values of str and newStr separated by a "/". Since the value o' +
                'f str is still "1211" (unchanged by the earlier str.replace("1", "*"); line) and' +
                ' the value of newStr is "1211", the correct output is indeed "1211 / 1111."'
    }, {
        id: 1038,
        category: 2,
        isImage: false,
        image: javaCode1038,
        title: "Which method transforms the date 01-01-1990 into 01-01-XXXX, hiding the year?",
        answers: [
            {
                id: 1,
                content: "a(String dateOfBirth)",
                correct: false
            }, {
                id: 2,
                content: "b(String dateOfBirth)",
                correct: true
            }, {
                id: 3,
                content: "c(String dateOfBirth)",
                correct: false
            }, {
                id: 4,
                content: "d(String dateOfBirth)",
                correct: true
            }
        ],
        explanation: 'a -> this method returns XX-XX-1990 since resp is XX-XX and then we are appendin' +
                'g dateOfBirth so this method is wrong.\n \nb -> basically is the reverse of a(), it ' +
                'is string from dateOfBirth and therefore returns 01-01-XXXX which we need \n \nc -> ' +
                'return compilation error because .append() donot exists in String class.\n \nd -> ge' +
                'nerate a StringBuilder object with dateOfBirth content. StringBuilder has replac' +
                'e method which Replaces the characters in a substring of this sequence with chara' +
                'cters in the specified String. The substring begins at the specified start and e' +
                'xtends to the characterat index end - 1 or to the end of the sequence if no such' +
                ' character exists. First the characters in the substring are removed and then th' +
                'e specified String is inserted at start. So in our case we are replacing charact' +
                'ers from 6 to 10 -> 1990 with XXXX. \n \nOur answers are -> b and d'
    }, {
        id: 1039,
        category: 2,
        isImage: false,
        image: javaCode1039,
        title: "What will be printed in the console?",
        answers: [
            {
                id: 1,
                content: "Hello / Hello ",
                correct: false
            }, {
                id: 2,
                content: "Hello World! / Hello",
                correct: false
            }, {
                id: 3,
                content: "Hello / Hello World!",
                correct: true
            }, {
                id: 4,
                content: "Hello World! / Hello World!",
                correct: false
            }, {
                id: 5,
                content: "Exception is thrown during the runtime",
                correct: false
            }
        ],
        explanation: 'A String is immutable while a StringBuilder is not.\nSo in method newString(), s.concat(' +
                '"World!"); will produce a new String "Hello World!" but will not affect the orig' +
                'inal String.\n \nHowever, the append() method of Strin' +
                'gBuilder appends to the original String object.\nSo, "Hello" becomes "Hello World' +
                '!".\n \nOutput is -> Hello / Hello World!'
    }
];