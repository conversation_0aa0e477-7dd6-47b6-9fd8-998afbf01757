import { MaterialCommunityIcons, FontAwesome } from '@expo/vector-icons';
import { ReactElement } from 'react';
import { appColors } from '../../utils/appColors';
import { scale } from 'react-native-size-matters';

export interface Category{
    id: number;
    title:string;
    icon:ReactElement;
}


export const categories: Category[] = [
    {
        id: 1, 
        title:"Basics",
        icon:<MaterialCommunityIcons name='beaker' color={appColors.blue}  size={scale(40)} style={{alignSelf: 'center'}} />
    },
    {
        id: 2, 
        title:"Data Types",
        icon:<FontAwesome name='address-book' color={appColors.blue}  size={scale(40)} style={{alignSelf: 'center'}} /> 
    },
    {
        id: 3, 
        title:"Operators",
        icon:<MaterialCommunityIcons name='ab-testing' color={appColors.blue}  size={scale(40)} style={{alignSelf: 'center'}} />
    },
    {
        id: 4, 
        title:"Arrays/Lists",
        icon:<FontAwesome name='list' color={appColors.blue}  size={scale(40)} style={{alignSelf: 'center'}} /> 
    },
    {
        id: 5, 
        title:"Loops",
        icon:<MaterialCommunityIcons name='circle-slice-1' color={appColors.blue}  size={scale(40)} style={{alignSelf: 'center'}} /> 
    },
    {
        id: 6, 
        title:"Methods",
        icon:<MaterialCommunityIcons name='waveform' color={appColors.blue}  size={scale(40)} style={{alignSelf: 'center'}} /> 
    },
    {
        id: 7, 
        title:"Inheritance",
        icon:<MaterialCommunityIcons name='account-supervisor' color={appColors.blue}  size={scale(40)} style={{alignSelf: 'center'}} />
    },
    {
        id: 8, 
        title:"Exceptions",
        icon:<MaterialCommunityIcons name='sprinkler' color={appColors.blue}  size={scale(40)} style={{alignSelf: 'center'}} />
    },
    {
        id: 9, 
        title:"Lambda",
        icon:<FontAwesome name='lastfm' color={appColors.blue}  size={scale(40)} style={{alignSelf: 'center'}} /> 
    },
    {
        id: 10, 
        title:"Date/Time",
        icon:<MaterialCommunityIcons name='calendar-multiselect' color={appColors.blue}  size={scale(40)} style={{alignSelf: 'center'}} />
    },    
]


export const getCategory = (id:number ):Category =>{    
    for(var category of categories){
        if(category.id === id){
            return category;
        }
    }
}