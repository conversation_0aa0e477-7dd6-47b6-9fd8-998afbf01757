import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { task162, task163, task164, task165, task166 } from "./snips/basicSnips";

export const operators : Questions[] = [
    {
        id: 161,
        category: 3,
        isImage: false,
        title: 'Which of the following operators are used with boolean variables?',
        answers: [
            {
                id: 1,
                content: '==',
                correct: true
            }, {
                id: 2,
                content: '+',
                correct: false
            }, {
                id: 3,
                content: '--',
                correct: false
            }, {
                id: 4,
                content: '!',
                correct: true
            }, {
                id: 5,
                content: '%',
                correct: false
            }, {
                id: 6,
                content: '<=',
                correct: false
            }
        ],
        explanation: 'Correct answers are == and ! \n \n== is used with numbers, booleans and objects' +
                '\n \n+ - are arithmetic operators.\n \n% is modulus operator which is used with ' +
                'numeric primitives.\n \n<= is relational operators that compares the values of t' +
                'wo numbers.'
    }, {
        id: 162,
        category: 2,
        isImage: false,
        title: "What data type, applied here, will not cause compilation error?",
        image: task162,
        answers: [
            {
                id: 1,
                content: 'int',
                correct: true
            }, {
                id: 2,
                content: 'long',
                correct: true
            }, {
                id: 3,
                content: 'boolean',
                correct: false
            }, {
                id: 4,
                content: 'double',
                correct: true
            }, {
                id: 5,
                content: 'short',
                correct: false
            }, {
                id: 6,
                content: 'byte',
                correct: false
            }
        ],
        explanation: 'When you perform arithmetic operations on variables of type byte, the result is ' +
                'promoted to an int.\n \nAlso you can assign int into a long or double variable s' +
                'ince both of them can hold any int value.\n \nShort and byte can be used as the ' +
                'data type for z, but it requires an explicit cast because the result of adding t' +
                'wo byte values is always promoted to int.\n \n(short) (x + y); \n \n(byte) (x + ' +
                'y);'
    }, {
        id: 163,
        category: 2,
        isImage: false,
        title: "What modifications should be performed here, if required?",
        image: task163,
        answers: [
            {
                id: 1,
                content: 'Everything is fine, no modification is needed',
                correct: false
            }, {
                id: 2,
                content: 'Rewrite => 14 + a into 14 + (int)a',
                correct: true
            }, {
                id: 3,
                content: 'Rewrite => 14 + a into (int)(14+a)',
                correct: true
            }, {
                id: 4,
                content: 'Change type b from int to short',
                correct: false
            }
        ],
        explanation: 'Primitive type int can be promoted into long, not vice versa, therefore this cod' +
                'e will have compilation error.\n \nIn order to fix that variable we should use e' +
                'xplicit casting of either variable a in 14 + a operation or cast entire operatio' +
                'n.\n \n(int)(14 + a)\n \n14 + (int)a'
    }, {
        id: 164,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task164,
        answers: [
            {
                id: 1,
                content: 'true / 7 / false',
                correct: false
            }, {
                id: 2,
                content: 'Compilation error',
                correct: false
            }, {
                id: 3,
                content: 'false / 7 / false',
                correct: false
            }, {
                id: 4,
                content: 'true / 7 / true',
                correct: false
            }, {
                id: 5,
                content: 'false / 7 / true',
                correct: true
            }
        ],
        explanation: 'Operand & requires execution of both sides therefore\n \ny > 10 => false; \n \nz' +
                ' = true => true marking z variable as true\n \nfalse & true => false.\n \nx = fal' +
                'se, y = 7, z = true'
    }, {
        id: 165,
        category: 3,
        isImage: false,
        title: "What will be the value of a?",
        image: task165,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '2',
                correct: true
            }, {
                id: 3,
                content: '3',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }, {
                id: 5,
                content: 'Exception thrown at the runtime',
                correct: false
            }
        ],
        explanation: 'There is nothing wrong with the code and no exception will be thrown during the ' +
                'runtime.\n \n6 * 3 => 18.\n \n18 % 4 => 2.'
    }, {
        id: 166,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task166,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '2',
                correct: false
            }, {
                id: 3,
                content: '3',
                correct: false
            }, {
                id: 4,
                content: '4',
                correct: true
            }, {
                id: 5,
                content: 'Compilation error',
                correct: false
            }, {
                id: 6,
                content: 'Exception thrown at the runtime',
                correct: false
            }
        ],
        explanation: 'There is nothing wrong with the code and no exception will be thrown during the ' +
                'runtime.\n \nb + c => 3, so a = 3; \n \na = a + b++ => a = 3 + 1 => 4. \n \nKeep in m' +
                'ind that value of b now is 2, but during calculation value was 1.'
    }
]