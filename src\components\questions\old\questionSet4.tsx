import {Questions} from "../../models/Questions";
export const passedSetPart4 : Questions[] = [
    {
        "id": 106,
        "category": 3,
        "isImage": false,
        "title": "What will be the result of attempting to compile and run the following code snip" +
                "pet?",
        "image": "public class Question {\n    public static void main(String[] args) {\n        B" +
                "oolean bool = new Boolean(\"tRUe\");\n        switch(bool) {\n            case t" +
                "rue:\n                System.out.println(\"TRUE\");\n            case false:\n  " +
                "              System.out.println(\"FALSE\");\n            default: \n           " +
                "     System.out.println(\"DEFAULT\");\n        }\n    }\n}",
        "answers": [
            {
                "id": 1,
                "content": "TRUE",
                "correct": false
            }, {
                "id": 2,
                "content": "FALSE\nDEFAULT",
                "correct": false
            }, {
                "id": 3,
                "content": "DEFAULT",
                "correct": false
            }, {
                "id": 4,
                "content": "Compilation error",
                "correct": true
            }
        ],
        "explanation": "The 'switch' statement can only accept primitive types (byte, short, int, char)," +
                " their wrapper classes (Byte, Short, Integer, Character), enums, and Strings. Si" +
                "nce 'Boolean' is not a valid type for a 'switch' statement, this code will cause" +
                " a compilation error."
    }, {
        "id": 107,
        "category": 3,
        "isImage": false,
        "title": "What will be the result of compiling and executing the Welcome class?",
        "image": "package com.example.test;\n\npublic class Welcome {\n    String message = null;" +
                "\n    \n    public Welcome() {\n        this(\"Hello World!\");\n    }\n    \n  " +
                "  public Welcome(String str) {\n        message = str;\n    }\n    \n    public " +
                "void showMessage() {\n        System.out.println(message);\n    }\n    \n    pub" +
                "lic static void main(String[] args) {\n        Welcome w1 = new Welcome();\n    " +
                "    Welcome w2 = new Welcome(\"Hello Java!\");\n        w1.showMessage();\n     " +
                "   w2.showMessage();\n    }\n}",
        "answers": [
            {
                "id": 1,
                "content": "Hello World!\nHello Java!",
                "correct": true
            }, {
                "id": 2,
                "content": "null\nHello Java!",
                "correct": false
            }, {
                "id": 3,
                "content": "null\nnull",
                "correct": false
            }, {
                "id": 4,
                "content": "Hello World!\nnull",
                "correct": false
            }
        ],
        "explanation": "The statement 'Welcome w1 = new Welcome();' invokes the no-arg constructor.\n\nT" +
                "he no-arg constructor calls the parameterized constructor with the argument \"He" +
                "llo World!\".\n\nThe parameterized constructor assigns \"Hello World!\" to the '" +
                "message' variable of the object referred to by 'w1'.\n\nThe statement 'Welcome w" +
                "2 = new Welcome(\"Good Evening!\");' invokes the parameterized constructor, whic" +
                "h assigns \"Good Evening!\" to the 'message' variable of the object referred to " +
                "by 'w2'.\n\nThe call 'w1.showMessage();' prints 'Hello World!'\n\nThe call 'w2.s" +
                "howMessage();' prints 'Hello Java!'."
    }, {
        "id": 108,
        "category": 6,
        "isImage": false,
        "title": "The 'super' keyword in Java is used to:",
        "answers": [
            {
                "id": 1,
                "content": "refer to static method of the class.",
                "correct": false
            }, {
                "id": 2,
                "content": "refer to parent class object.",
                "correct": true
            }, {
                "id": 3,
                "content": "refer to static variable of the class.",
                "correct": false
            }, {
                "id": 4,
                "content": "refer to current class object.",
                "correct": false
            }
        ],
        "explanation": "The 'super' keyword refers to the parent class object, allowing access to the pa" +
                "rent class's methods and variables. The 'this' keyword, on the other hand, refer" +
                "s to the current class object."
    }, {
        "id": 109,
        "category": 6,
        "isImage": false,
        "title": "What will be the result of compiling and executing the PersonList class?",
        "image": "//PersonList.java\npackage com.example.test;\n\nimport java.util.ArrayList;\nimp" +
                "ort java.util.List;\n\npublic class PersonList {\n    public static void main(St" +
                "ring[] args) {\n        List<StringBuilder> people = new ArrayList<>();\n       " +
                " people.add(new StringBuilder(\"Alice\"));\n        people.add(new StringBuilder" +
                "(\"Bob\"));\n        people.add(new StringBuilder(\"Charlie\"));\n        people" +
                ".add(new StringBuilder(\"David\"));\n\n        for(int i = 0; i < people.size();" +
                " i++) \n        {\n            if(i == 0) {\n                people.remove(new S" +
                "tringBuilder(\"Charlie\"));\n            }\n        }\n\n        System.out.prin" +
                "tln(people);\n    }\n}",
        "answers": [
            {
                "id": 1,
                "content": "[Alice, Bob, Charlie, David]",
                "correct": true
            }, {
                "id": 2,
                "content": "[Alice, David]",
                "correct": false
            }, {
                "id": 3,
                "content": "[Alice, Bob, David]",
                "correct": false
            }, {
                "id": 4,
                "content": "An exception is thrown at runtime",
                "correct": false
            }
        ],
        "explanation": "In this example, the code is trying to remove an item from the list while iterat" +
                "ing using a traditional for loop. One might think that this code would throw a j" +
                "ava.util.ConcurrentModificationException.\n\nHowever, a java.util.ConcurrentModi" +
                "ficationException is never thrown for a traditional for loop. It is thrown for a" +
                " for-each loop or while using an Iterator/ListIterator.\n\nIn this case, people." +
                "remove(new StringBuilder(\"Charlie\")) will never remove any items from the list" +
                " because the StringBuilder class doesn't override the equals(Object) method of t" +
                "he Object class.\n\nThe StringBuilder instances created at people.add(new String" +
                "Builder(\"Charlie\")) and people.remove(new StringBuilder(\"Charlie\")) are at d" +
                "ifferent memory locations, and the equals(Object) method returns false for these" +
                " instances."
    }, {
        "id": 110,
        "category": 6,
        "isImage": false,
        "title": "What will be the result of compiling and executing the Example class?",
        "image": "package com.example.demo;\n\npublic class Example {\n    public static void modi" +
                "fy(int value) {\n        value++;\n        System.out.println(value);\n    }\n\n" +
                "    public static void main(String[] args) {\n        int number = 1;\n        E" +
                "xample.modify(number);\n        System.out.println(number);\n    }\n}",
        "answers": [
            {
                "id": 1,
                "content": "2\n2",
                "correct": false
            }, {
                "id": 2,
                "content": "2\n1",
                "correct": true
            }, {
                "id": 3,
                "content": "None of the other options",
                "correct": false
            }, {
                "id": 4,
                "content": "Compilation Error",
                "correct": false
            }
        ],
        "explanation": "There are no compilation errors and the main(String[]) method is invoked when ex" +
                "ecuting the Example class.\n\nnumber = 1.\n\nExample.modify(number) is executed " +
                "next. The contents of number (which is 1) are copied to the variable 'value', an" +
                "d the modify(int) method starts executing.\n\nvalue++; increments the value of v" +
                "alue by 1, making value = 2. There are no changes to the value of the variable '" +
                "number' in the main(String[]) method, which still contains 1.\n\nSystem.out.prin" +
                "tln(value); prints 2 to the console.\n\nThe modify(int) method finishes its exec" +
                "ution, and control goes back to the main(String[]) method.\n\nSystem.out.println" +
                "(number); prints 1 to the console."
    }, {
        "id": 111,
        "category": 7,
        "isImage": false,
        "title": "What will be the result of compiling and executing the ExampleBaseDerived class?",
        "image": "package com.example.test;\n\nclass Parent {\n    protected void method1() {\n   " +
                "     System.out.println(\"Parent: method1()\");\n    }\n}\n\nclass Child extends" +
                " Parent {\n    void method1() {\n        System.out.println(\"Child: method1()\"" +
                ");\n    }\n}\n\npublic class ExampleBaseDerived {\n    public static void main(S" +
                "tring[] args) {\n        Parent p = new Child();\n        p.method1();\n    }\n}",
        "answers": [
            {
                "id": 1,
                "content": "None of the other options",
                "correct": true
            }, {
                "id": 2,
                "content": "Child: method1()",
                "correct": false
            }, {
                "id": 3,
                "content": "Parent: method1()\nChild: method1()",
                "correct": false
            }, {
                "id": 4,
                "content": "Parent: method1()",
                "correct": false
            }
        ],
        "explanation": "The Child class overrides the method method1() of the Parent class. The access m" +
                "odifier of the method method1() in the Parent class is protected, so the overrid" +
                "ing method can use protected or public access.\n\nHowever, in this case, the ove" +
                "rriding method uses the default access modifier, which results in a compilation " +
                "error. Thus, the code does not compile."
    }, {
        "id": 112,
        "category": 1,
        "isImage": false,
        "title": "Which of the following correctly imports Animal class from com.safari package?",
        "answers": [
            {
                "id": 1,
                "content": "import com.safari.*;",
                "correct": true
            }, {
                "id": 2,
                "content": "Import com.safari.Animal;",
                "correct": false
            }, {
                "id": 3,
                "content": "Import com.safari.*;",
                "correct": false
            }, {
                "id": 4,
                "content": "import com.safari;",
                "correct": false
            }
        ],
        "explanation": "There are 2 ways to import the Animal class from the com.safari package:\n\nimpo" +
                "rt com.safari.*;\n\nimport com.safari.Animal;\n\nNOTE: All keywords in the impor" +
                "t statement should be in lowercase letters.\n\nOut of the given 4 options, only " +
                "one statement is correct:\n\nimport com.safari.*;"
    }, {
        "id": 113,
        "category": 9,
        "isImage": false,
        "title": "What will be the result of compiling and executing the ExampleStringList class?",
        "image": "package com.example.test;\n\nimport java.util.ArrayList;\nimport java.util.List;" +
                "\n\npublic class ExampleStringList {\n    public static void main(String[] args)" +
                " {\n        List<String> stringList = new ArrayList<>();\n        stringList.add" +
                "(\"Apple\");\n        stringList.add(\"Dog\");\n        stringList.add(\"Banana" +
                "\");\n        stringList.add(\"Cat\");\n        stringList.add(\"Elephant\");\n " +
                "       stringList.add(\"Ant\");\n\n        stringList.removeIf(s -> s.length() >" +
                " 4);\n\n        System.out.println(stringList);\n    }\n}",
        "answers": [
            {
                "id": 1,
                "content": "Compilation error",
                "correct": false
            }, {
                "id": 2,
                "content": "[Dog, Cat, Ant]",
                "correct": true
            }, {
                "id": 3,
                "content": "[Apple, Dog, Banana, Cat, Elephant, Ant]",
                "correct": false
            }, {
                "id": 4,
                "content": "[Apple, Banana, Elephant]",
                "correct": false
            }, {
                "id": 5,
                "content": "Runtime Exception",
                "correct": false
            }
        ],
        "explanation": "The removeIf(Predicate) method was added as a default method in the Collection i" +
                "nterface in JDK 8. It removes all the elements of this collection that satisfy t" +
                "he given predicate.\n\nIn this example, the Predicate's test method returns true" +
                " for all the Strings whose length is greater than 4. Thus, strings with more tha" +
                "n 4 characters are removed from the list, leaving [Dog, Cat, Ant]."
    }, {
        "id": 114,
        "category": 5,
        "isImage": false,
        "title": "What will be the result of compiling and executing LoopExample class?",
        "image": "package com.example.loops;\n\npublic class LoopExample {\n    public static void" +
                " main(String[] args) {\n        for:\n        for (int x = 2; x <= 100; x = x + " +
                "2) {\n            for(int y = 1; y <= 10; y++) {\n                System.out.pri" +
                "nt(x * y + \"\\t\");\n            }\n            System.out.println();\n        " +
                "    if(x == 10) {\n                break for;\n            }\n        }\n    }\n" +
                "}",
        "answers": [
            {
                "id": 1,
                "content": "Total 50 rows will be there in the output",
                "correct": false
            }, {
                "id": 2,
                "content": "Compilation error",
                "correct": true
            }, {
                "id": 3,
                "content": "Total 5 rows will be there in the output",
                "correct": false
            }, {
                "id": 4,
                "content": "Total 100 rows will be there in the output",
                "correct": false
            }
        ],
        "explanation": "for is a keyword and hence can't be used as a label.\n\nJava labels follow the i" +
                "dentifier naming rules and one rule is that we can't use java keywords as identi" +
                "fier. Hence, Compilation error."
    }, {
        "id": 115,
        "category": 4,
        "isImage": false,
        "title": "What will be the result of compiling and executing ArrayExample class?",
        "image": "package com.example.arrays;\n\npublic class ArrayExample {\n    public static vo" +
                "id main(String[] args) {\n        String[] names = new String[1];\n        Syste" +
                "m.out.println(names[0].isEmpty());\n    }\n}",
        "answers": [
            {
                "id": 1,
                "content": "false",
                "correct": false
            }, {
                "id": 2,
                "content": "true",
                "correct": false
            }, {
                "id": 3,
                "content": "NullPointerException is thrown at runtime",
                "correct": true
            }, {
                "id": 4,
                "content": "ArrayIndexOutOfBoundsException is thrown at runtime",
                "correct": false
            }
        ],
        "explanation": "All the elements of an array are initialized to their respective zeros (in the c" +
                "ase of primitive types) or null (in the case of reference types).\n\nSo, names[0" +
                "] refers to null.\n\nMethod 'isEmpty()' is invoked on a null reference and hence" +
                " NullPointerException is thrown at runtime."
    }, {
        "id": 116,
        "category": 6,
        "isImage": false,
        "title": "For the class Fruit, which option, if used to replace /*INSERT*/, will print GRE" +
                "EN on to the console?",
        "image": "package com.example.methods;\n\npublic class Fruit {\n     public String hue;\n" +
                "\n     public Fruit(String hue) {\n         /*INSERT*/\n     }\n\n     public st" +
                "atic void main(String [] args) {\n         Fruit fruit = new Fruit(\"GREEN\");\n" +
                "         System.out.println(fruit.hue);\n     }\n}",
        "answers": [
            {
                "id": 1,
                "content": "this.hue = GREEN;",
                "correct": false
            }, {
                "id": 2,
                "content": "hue = hue;",
                "correct": false
            }, {
                "id": 3,
                "content": "hue = GREEN;",
                "correct": false
            }, {
                "id": 4,
                "content": "this.hue = hue;",
                "correct": true
            }
        ],
        "explanation": "Instance variable hue is shadowed by the parameter variable hue of the parameter" +
                "ized constructor. So, hue = hue will have no effect, because short hand notation" +
                " within the constructor body will always refer to the LOCAL variable. To refer t" +
                "o the instance variable, this reference is needed. Hence 'this.hue = hue;' is co" +
                "rrect.\n\n'hue = GREEN;' and 'this.hue = GREEN;' cause a compilation error as GR" +
                "EEN is not within double quotes(\"\").\n\nNOTE: 'hue = \"GREEN\";' will only ass" +
                "ign 'GREEN' to the local variable and not the instance variable, but 'this.hue =" +
                " \"GREEN\";' will assign 'GREEN' to the instance variable."
    }, {
        "id": 117,
        "category": 8,
        "isImage": false,
        "title": "What will be the result of compiling and executing ExceptionTest class?",
        "image": "package com.example.exceptions;\n\nimport java.io.FileNot" +
                "FoundException;\n\npublic class ExceptionTest {\n     public static void main(St" +
                "ring[] args) {\n         try {\n             System.out.println(1);\n         } " +
                "catch (NullPointerException ex) {\n             System.out.println(\"ONE\");\n  " +
                "       } catch (FileNotFoundException ex) {\n             System.out.println(\"T" +
                "WO\");\n         }\n         System.out.println(\"THREE\");\n     }\n}",
        "answers": [
            {
                "id": 1,
                "content": "None of the System.out.println statements are executed",
                "correct": false
            }, {
                "id": 2,
                "content": "THREE",
                "correct": false
            }, {
                "id": 3,
                "content": "TWO\nTHREE",
                "correct": false
            }, {
                "id": 4,
                "content": "Compilation error",
                "correct": true
            }, {
                "id": 5,
                "content": "ONE\nTHREE",
                "correct": false
            }
        ],
        "explanation": "Java doesn't allow catching specific checked exceptions if these are not thrown " +
                "by the statements inside the try block.\n\ncatch(FileNotFoundException ex) {} ca" +
                "uses a compilation error in this case as System.out.println(1); will never throw" +
                " FileNotFoundException.\n\nNOTE: Java allows catching Exception type. catch(Exce" +
                "ption ex) {} will never cause a compilation error."
    }, {
        "id": 118,
        "category": 6,
        "isImage": false,
        "title": "When does a class get the default constructor?",
        "answers": [
            {
                "id": 1,
                "content": "If the class does not define any constructors explicitly.",
                "correct": true
            }, {
                "id": 2,
                "content": "All classes in Java get a default constructor.",
                "correct": false
            }, {
                "id": 3,
                "content": "If you define parameterized constructor for the class.",
                "correct": false
            }, {
                "id": 4,
                "content": "You have to define at least one constructor to get the default constructor.",
                "correct": false
            }
        ],
        "explanation": "Default constructor (which is a no-argument constructor) is added by the Java co" +
                "mpiler only if there are no constructors in the class."
    }, {
        "id": 119,
        "category": 4,
        "isImage": false,
        "title": "What will be the result of compiling and executing Test class?",
        "image": "// Test.java\npackage com.example.exceptions;\n\nimport java.util.ArrayList;\nim" +
                "port java.util.List;\n\npublic class Test {\n     public static void main(String" +
                "[] args) {\n         List<String> list = new ArrayList<>();\n         list.add(0" +
                ", \"Array\");\n         list.set(0, \"List\");\n\n         System.out.println(li" +
                "st);\n     }\n}",
        "answers": [
            {
                "id": 1,
                "content": "[List]",
                "correct": true
            }, {
                "id": 2,
                "content": "An exception is thrown at runtime",
                "correct": false
            }, {
                "id": 3,
                "content": "[Array]",
                "correct": false
            }, {
                "id": 4,
                "content": "[Array, List]",
                "correct": false
            }, {
                "id": 5,
                "content": "[List, Array]",
                "correct": false
            }
        ],
        "explanation": "list.add(0, \"Array\"); means list --> [Array], list.set(0, \"List\"); means rep" +
                "lace the current element at index 0 with the passed element \"List\". So after t" +
                "his operation, list --> [List]. In the console, [List] is printed."
    }, {
        "id": 120,
        "category": 7,
        "isImage": false,
        "title": "Which of the following is true for code below?",
        "image": "package com.example.exceptions;\n\ninterface I01 {\n     void m1();\n}\n\npublic" +
                " class Implementer extends Object implements I01{\n     protected void m1() {\n" +
                "\n     }\n}",
        "answers": [
            {
                "id": 1,
                "content": "None of the other options.",
                "correct": true
            }, {
                "id": 2,
                "content": "interface I01 gives compilation error as method m1 is not public.",
                "correct": false
            }, {
                "id": 3,
                "content": "Method m1() in Implementer class is not implemented correctly.",
                "correct": false
            }, {
                "id": 4,
                "content": "Implementer class declaration is not correct.",
                "correct": false
            }
        ],
        "explanation": "void m1(); in interface I01 is equivalent to public abstract void m1(); So metho" +
                "d m1() is implicitly public and abstract.\n\nIn Java, a class can extend from on" +
                "ly one class but can implement multiple interfaces. Correct keywords are: extend" +
                "s and implements. So class declaration is correct.\n\nAs method m1() is implicit" +
                "ly public in I01, hence overriding method in Implementer class should also be pu" +
                "blic. But it is protected and hence the compiler complains."
    }, {
        "id": 121,
        "category": 8,
        "isImage": false,
        "title": "Which of the following is true for code below?",
        "image": "package com.example.exceptions;\n\npublic class Test {\n     public static void " +
                "main(String[] args) {\n         byte[] arr = new byte[0];\n         System.out.p" +
                "rintln(arr[0]);\n     }\n}",
        "answers": [
            {
                "id": 1,
                "content": "Compilation error",
                "correct": true
            }, {
                "id": 2,
                "content": "0",
                "correct": false
            }, {
                "id": 3,
                "content": "NullPointerException",
                "correct": false
            }, {
                "id": 4,
                "content": "ArrayIndexOutOfBoundsException",
                "correct": false
            }
        ],
        "explanation": "arr refers to an array object of size 0. That means arr stores some memory addre" +
                "ss.\n\nSo we will not get NullPointerException in this case. But index 0 is not " +
                "available for an array object of size 0 and thus ArrayIndexOutOfBoundsException " +
                "is thrown at runtime."
    }, {
        "id": 122,
        "category": 4,
        "isImage": false,
        "title": "What will be the result of compiling and executing Test class?",
        "image": "package com.org.question;\n \npublic class Test {\n    public static void main(S" +
                "tring[] args) {\n        int [] arr1 = {5, 10, 15};\n        int [] arr2 = {'A'," +
                " 'B'};\n        arr1 = arr2;\n        System.out.println(arr1.length + arr2.leng" +
                "th);\n    }\n}",
        "answers": [
            {
                "id": 1,
                "content": "0",
                "correct": false
            }, {
                "id": 5,
                "content": "4",
                "correct": true
            }, {
                "id": 2,
                "content": "5",
                "correct": false
            }, {
                "id": 4,
                "content": "6",
                "correct": false
            }, {
                "id": 3,
                "content": "An exception is thrown at runtime",
                "correct": false
            }
        ],
        "explanation": "Initially arr1 refers to an int array object of 3 elements.\n\nAnd arr2 refers t" +
                "o an int array object of 2 elements [char type is compatible with int type]\n\nW" +
                "hen the statement `arr1 = arr2;` executes, variable arr1 copies the content of a" +
                "rr2, which is the address of array object containing 2 elements. Hence, arr1 als" +
                "o starts referring to same array object. arr1.length = 2 and arr2.length = 2.\n" +
                "\nTherefore, output is: 4"
    }, {
        "id": 123,
        "category": 8,
        "isImage": false,
        "title": "What will be the result of compiling and executing Test class?",
        "image": "package com.example.org;\n \npublic class Test {\n    public static void main(St" +
                "ring[] args) {\n        StringBuilder sb = new StringBuilder(5);\n        sb.app" +
                "end(\"0123456789\");\n        sb.delete(8, 1000);\n        System.out.println(sb" +
                ");\n    }\n}",
        "answers": [
            {
                "id": 1,
                "content": "01234567",
                "correct": true
            }, {
                "id": 2,
                "content": "An exception is thrown at runtime",
                "correct": false
            }, {
                "id": 3,
                "content": "Compilation error",
                "correct": false
            }, {
                "id": 4,
                "content": "89",
                "correct": false
            }
        ],
        "explanation": "`new StringBuilder(5);` creates a StringBuilder instance, whose internal char ar" +
                "ray's length is 5 but the internal char array's length is adjusted when characte" +
                "rs are added/removed from the StringBuilder instance. `sb.append(\"0123456789\")" +
                ";` successfully appends \"0123456789\" to the StringBuilder's instance referred " +
                "by sb.\n\ndelete method accepts 2 parameters: delete(int start, int end), where " +
                "start is inclusive and end is exclusive.\n\nThis method throws StringIndexOutOfB" +
                "oundsException for following scenarios:\n\nA. start is negative\n\nB. start is g" +
                "reater than sb.length()\n\nC. start is greater than end\n\n\n\nIf end is greater" +
                " than the length of StringBuilder object, then StringIndexOutOfBoundsException i" +
                "s not thrown and end is set to sb.length().\n\nSo, in this case, `sb.delete(8, 1" +
                "000);` is equivalent to `sb.delete(8, sb.length());` and this deletes characters" +
                " at 8th index (8) and 9th index (9). So remaining characters are: \"01234567\"." +
                "\n\n\n\nStringBuilder class overrides toString() method, which prints the text s" +
                "tored in StringBuilder instance. Hence, `System.out.println(sb);` prints 0123456" +
                "7 on to the console."
    }, {
        "id": 124,
        "category": 5,
        "isImage": false,
        "title": "What will be the result of compiling and executing?",
        "image": "package com.example.org; \n\npublic class Test { \n public static void main(Stri" +
                "ng[] args) { \n int start = 1; \n int sum = 0; \n do { \n if(start % 2 == 0) { " +
                "\n continue; \n } \n sum += start; \n } while(++start <= 10); \n System.out.prin" +
                "tln(sum); \n }\n}",
        "answers": [
            {
                "id": 1,
                "content": "55",
                "correct": false
            }, {
                "id": 2,
                "content": "24",
                "correct": false
            }, {
                "id": 3,
                "content": "Compilation error",
                "correct": false
            }, {
                "id": 4,
                "content": "25",
                "correct": true
            }
        ],
        "explanation": "When start is divisible by 2 [2, 4, 6, 8, 10], continue; statement takes the con" +
                "trol to boolean expression and hence sum += start; is not executed. Hence result" +
                " is the sum of numbers 1,3,5,7,9."
    }, {
        "id": 125,
        "category": 2,
        "isImage": false,
        "title": "Which of the following statements about Java data types are true?",
        "answers": [
            {
                "id": 1,
                "content": "A variable of type double can hold a value of type float without explicit castin" +
                        "g.",
                "correct": false
            }, {
                "id": 2,
                "content": "Java supports automatic type promotion in expressions.",
                "correct": true
            }, {
                "id": 3,
                "content": "Primitive data types in Java are mutable.",
                "correct": false
            }, {
                "id": 4,
                "content": "A char in Java can hold a Unicode character.",
                "correct": true
            }
        ],
        "explanation": "In Java, a variable of type double cannot hold a value of type float without exp" +
                "licit casting because double has a wider range than float. Java supports automat" +
                "ic type promotion in expressions, where narrower data types are automatically pr" +
                "omoted to wider data types if necessary. Primitive data types in Java are immuta" +
                "ble, meaning they cannot be changed once they are assigned a value. A char in Ja" +
                "va can indeed hold a Unicode character, as char data type in Java represents a 1" +
                "6-bit Unicode character."
    }, {
        "id": 126,
        "category": 3,
        "isImage": false,
        "title": "What will be the result of compiling and executing Test class?",
        "image": "package com.example.opera;\n\npublic class MyOperators{\n     public static void" +
                " main(String[] args) {\n         String str = \"Java World!\";\n         System." +
                "out.println(str.length() + \" : \" + str.charAt(10));\n     }\n}",
        "answers": [
            {
                "id": 1,
                "content": "Compilation error.",
                "correct": false
            }, {
                "id": 2,
                "content": "An exception is thrown at runtime.",
                "correct": false
            }, {
                "id": 3,
                "content": "11 : !",
                "correct": true
            }, {
                "id": 4,
                "content": "11 : s",
                "correct": false
            }
        ],
        "explanation": "String class has length() method, which returns the number of characters in the " +
                "String. So length() method returns 11. String class also has charAt(int index) m" +
                "ethod, which returns the character at the specified index. str.charAt(10) looks " +
                "for the character at index 10 (indices start with 0). The '!' sign is at index 1" +
                "0. Hence the output is: 11 : !"
    }, {
        "id": 127,
        "category": 3,
        "isImage": false,
        "title": "What will be the result of compiling and executing Test class?",
        "image": "package com.example.org;\n\npublic class Test {\n    public static void main(Str" +
                "ing[] args) {\n        final int i1 = 1;\n        final Integer i2 = 1;\n       " +
                " final String s1 = \":ONE\";\n        \n        String str1 = i1 + s1;\n        " +
                "String str2 = i2 + s1;\n        \n        System.out.println(str1 == \"1:ONE\");" +
                "\n        System.out.println(str2 == \"1:ONE\");\n    }\n}",
        "answers": [
            {
                "id": 2,
                "content": "false\ntrue",
                "correct": false
            }, {
                "id": 3,
                "content": "true\ntrue",
                "correct": false
            }, {
                "id": 1,
                "content": "true\nfalse",
                "correct": true
            }, {
                "id": 4,
                "content": "false\nfalse",
                "correct": false
            }
        ],
        "explanation": "For the statement `String str1 = i1 + s1;`, `i1 + s1` is a constant expression w" +
                "hich is computed at compile-time and results in String literal '1:ONE'. This mea" +
                "ns during compilation, Java compiler translates the statement to `String str1 = " +
                "'1:ONE';`. As '1:ONE' is a String literal, hence at runtime it will be referred " +
                "by String Pool. On the other hand, for the statement `String str2 = i2 + s1;`, `" +
                "i2 + s1` is computed at run-time and returns a non-pool String object '1:ONE'. A" +
                "s a result, `str1 == '1:ONE'` returns true, whereas `str2 == '1:ONE'` returns fa" +
                "lse."
    }, {
        "id": 128,
        "category": 2,
        "isImage": false,
        "title": "What will be the result of compiling and executing Exam class?",
        "image": "package com.mycompany.exam;\n\npublic class Exam {\n    public stati" +
                "c void main(String[] args) {\n        StringBuilder myStringBuilder = new String" +
                "Builder(\"ErrorHandling\");\n        myStringBuilder.delete(5, 6).insert(5, \" H" +
                "\").toString().toUpperCase();\n        System.out.println(myStringBuilder);\n   " +
                " }\n}",
        "answers": [
            {
                "id": 1,
                "content": "Error Handling",
                "correct": true
            }, {
                "id": 2,
                "content": "Error Hndling",
                "correct": false
            }, {
                "id": 3,
                "content": "ERRORHANDLING",
                "correct": false
            }, {
                "id": 4,
                "content": "ERROR HANDLING",
                "correct": false
            }
        ],
        "explanation": "myStringBuilder -> \"ErrorHandling\"\nmyStringBuilder.delete(5, 6) -> \"ErrorHnd" +
                "ling\"\nmyStringBuilder.insert(5, \" H\") -> \"Error Handling\"\nmyStringBuilder" +
                ".toString() -> Creates a new String object \"Error Handling\"\n\"Error Handling" +
                "\".toUpperCase() -> Creates another String object \"ERROR HANDLING\" but the Str" +
                "ing object is not referred and used.\n\nMethod invocation on myStringBuilder mod" +
                "ifies the same object, so after insert(5, \" H\") method invocation myStringBuil" +
                "der refers to \"Error Handling\" and this is printed to the Console."
    },
    {
        "id": 129,
        "category": 8,
        "isImage": false,
        "title": "On execution, does Test class print HELLO on to the console?",
        "image": "package com.library.questions;\n\npublic class Question {\n     static Double doubleValue;\n     static int intValue = doubleValue.intValue();\n \n     public static void main(String[] args) {\n         System.out.println(\"HELLO\");\n     }\n}",
        "answers": [
            {
                "id": 1,
                "content": "Yes, HELLO is printed on to the console",
                "correct": false
            },
            {
                "id": 2,
                "content": "No, HELLO is not printed on to the console",
                "correct": true
            }
        ],
        "explanation": "To invoke the special main method, JVM loads the class in the memory. At that time, static fields of Test class are initialized. doubleValue is of Double type so null is assigned to it. intValue is also static variable so doubleValue.intValue(); is executed and as doubleValue is null hence doubleValue.intValue() throws a NullPointerException and as a result an instance of java.lang.ExceptionInInitializerError is thrown."
    },
    {
        "id": 130,
        "category": 8,
        "isImage": false,
        "title": "What will be the result of compiling and executing following program?",
        "image": "package org.come.example;\n \nclass Rectangle {\n     private int height;\n     private int width;\n \n     public Rectangle(int height, int width) {\n         this.height = height;\n         this.width = width;\n     }\n \n     public int getHeight() {\n         return height;\n     }\n \n     public int getWidth() {\n         return width;\n     }\n}\n \npublic class ExceptionExample {\n     public static void main(String[] args) {\n        private int i = 100;\n        private int j = 200;\n         Rectangle rect = new Rectangle(i, j);\n         System.out.println(rect.getHeight() + \", \" + rect.getWidth());\n     }\n}",
        "answers": [
            {
                "id": 1,
                "content": "Compilation Error",
                "correct": true
            },
            {
                "id": 2,
                "content": "100, 200",
                "correct": false
            },
            {
                "id": 3,
                "content": "200, 100",
                "correct": false
            },
            {
                "id": 4,
                "content": "0, 0",
                "correct": false
            }
        ],
        "explanation": "i and j cannot be declared private as i and j are local variables.\n\nOnly final modifier can be used with local variables."
    }
    
    

];