import React, {Component } from 'react';
import BouncyCheckbox from "react-native-bouncy-checkbox";
import { Answer } from '../models/Questions';
import { generateRandomCode } from '../../service/RandomService';
import { appColors } from '../../utils/appColors';
import { scale } from 'react-native-size-matters';

interface PassedProps {
    answer : Answer;
    isChecked: boolean;
}

interface CheckboxState{
   
}


export class SharedCheckbox extends Component < PassedProps, CheckboxState > {
    constructor(props) {
        super(props);
        this.state = {};
    }

    render(){
        const { answer, isChecked } = this.props;   

        return(
            <BouncyCheckbox
            key={generateRandomCode()}
            textContainerStyle={{minWidth:'10%', maxWidth:'90%'}}
            size={25}
            fillColor={appColors.blue}
            unfillColor="#FFFFFF"
            text={answer.content}
            iconStyle={{borderColor: "black"}}
            innerIconStyle={{borderWidth: 2}}
            disabled={true}
            isChecked={isChecked}
            textStyle={{
                textDecorationLine: (!answer.correct ? "line-through" : 'none'),
                color: (answer.correct ? appColors.white : appColors.black),
                fontFamily: 'LatoRegular',
                fontSize:scale(16),
                backgroundColor: (answer.correct  ? appColors.blue : null)
            }}
        /> 
        )
    }
}