import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { javaCode193, task186, task188, task189, task190, task192 } from "./snips/basicSnips";

export const arrays : Questions[] = [
    {
        id: 186,
        category: 5,
        isImage: false,
        title: "What will be printed in console?",
        image: task186,
        answers: [
            {
                id: 1,
                content: '0',
                correct: false
            }, {
                id: 2,
                content: '1',
                correct: false
            }, {
                id: 3,
                content: '2',
                correct: true
            }, {
                id: 4,
                content: '3',
                correct: false
            }, {
                id: 5,
                content: 'Compilation error',
                correct: false
            }, {
                id: 6,
                content: 'Exception is thrown during the run time',
                correct: false
            }
        ],
        explanation: 'When i = 0, the inner loop iterates for j = 0, 1, 2.\n\nIn this case, i is equal' +
                ' to j.\n\nWhen j = 0, so the counter is incremented by 1.\n\nWhen i = 1, the inn' +
                'er loop iterates for j = 0, 1, 2. In this case, i is equal to j when j = 1, so t' +
                'he counter is incremented by 1.\n\nWhen i = 2, the inner loop iterates for j = 0' +
                ', 1, 2. In this case, i is equal to j when j = 2, so the counter is incremented ' +
                'by 1. After all iterations, the counter has been incremented a total of 2 times.' +
                ' Therefore, the output of the program is indeed: 2'
    }, {
        id: 187,
        category: 4,
        isImage: false,
        title: 'Which arrays are not declared legally?',
        answers: [
            {
                id: 1,
                content: 'int[][] scores = new int[0][2];',
                correct: false
            }, {
                id: 2,
                content: 'double[][] averages = new double[][2];',
                correct: true
            }, {
                id: 3,
                content: 'int[][] grades = new int[0][];',
                correct: false
            }, {
                id: 4,
                content: 'java.lang.Integer[] points = new java.lang.Integer[5];',
                correct: false
            }, {
                id: 5,
                content: 'int[][] midterms = new double[][];',
                correct: true
            }
        ],
        explanation: 'int[][] scores = new int[0][2]; This declaration is legal. It creates a 2D array' +
                ' named scores with 0 rows and 2 columns. This is allowed because arrays can have' +
                ' zero length for any dimension.\n\ndouble[][] averages = new double[][2]; This d' +
                'eclaration is illegal. When defining the size of an array, you must specify both' +
                ' dimensions. In this case, you have provided the size for the second dimension (' +
                '2), but you havenot specified the size for the first dimension. It should be som' +
                'ething like double[][] averages = new double[3][2];\n\nint[][] grades = new int[' +
                '0][];    This declaration is legal. Similar to the first example, it creates a 2' +
                'D array named grades with 0 rows. The second dimension is left unspecified, allo' +
                'wing you to later assign arrays of different lengths to each row.\n\njava.lang.I' +
                'nteger[] points = new java.lang.Integer[5]; This declaration is legal. It create' +
                's a 1D array of java.lang.Integer objects named points with a length of 5.\n\nin' +
                't[][] midterms = new double[][]; This declaration is illegal due to the mixing i' +
                'ncompatible data types (int and double). '
    }, {
        id: 188,
        category: 4,
        isImage: false,
        title: "Which line if uncommented will not cause compilation error?",
        image: task188,
        answers: [
            {
                id: 1,
                content: '1',
                correct: true
            }, {
                id: 2,
                content: '2',
                correct: false
            }, {
                id: 3,
                content: '3',
                correct: false
            }, {
                id: 4,
                content: '4',
                correct: false
            }, {
                id: 5,
                content: '5',
                correct: false
            }, {
                id: 6,
                content: '6',
                correct: false
            }
        ],
        explanation: 'Arrays have only 1 possibility to get length of an array and it is called length' +
                ' field. It is a not a method.\n\nSize (method) is used in lists, but not in arra' +
                'ys.\n\nCapacity neither method nor field do not exist in arrays. In list capacit' +
                'y declared either in constructor or by method ensureCapacity(int amount).\n\nSo ' +
                'we have only 1 answer -> 1'
    }, {
        id: 189,
        category: 4,
        isImage: false,
        title: "What will be printed in console?",
        image: task189,
        answers: [
            {
                id: 1,
                content: 'Position = 0',
                correct: false
            }, {
                id: 2,
                content: 'Position = 1',
                correct: true
            }, {
                id: 3,
                content: 'Position = 2',
                correct: false
            }, {
                id: 4,
                content: 'Position = 3',
                correct: false
            }, {
                id: 5,
                content: 'Position = ??',
                correct: true
            }, {
                id: 6,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'Given that gradesA is not sorted, the result of Arrays.binarySearch(gradesA, 67)' +
                ' will be unpredictable. The binary search algorithm assumes a sorted array, and ' +
                'if the array is not sorted, the result may not be accurate.\n\nFor gradesB, sinc' +
                'e it is sorted as {55, 67, 77, 81, 90}, the binary search will work correctly, a' +
                'nd searchB will be 1, indicating that the value 67 is found at index 1.'
    }, {
        id: 190,
        category: 4,
        isImage: false,
        title: "What will be printed in console?",
        image: task190,
        answers: [
            {
                id: 1,
                content: '12',
                correct: false
            }, {
                id: 2,
                content: '34',
                correct: false
            }, {
                id: 3,
                content: 'Compilation error',
                correct: true
            }, {
                id: 4,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'This code snippet produces compilation error.\n\narr3 = arr1;\n\nis not allowed ' +
                'due to type incompatibility. arr1 is of type int[] while arr3 is of type char[].' +
                ' You cannot assign an int[] reference to a char[] variable.'
    }, {
        id: 191,
        category: 4,
        isImage: false,
        title: 'Define code snippet which creates multidimensional array',
        answers: [
            {
                id: 1,
                content: 'int[][] arr1 = {1,4};',
                correct: false
            }, {
                id: 2,
                content: 'int[][] arr2 = {{7},{5,1}};',
                correct: true
            }, {
                id: 3,
                content: 'int[][] arr3 = {{1,2,3,4},{12,7},{31}};',
                correct: true
            }, {
                id: 4,
                content: 'int[][] arr4 = new int[3][3]{{7},{5,1}};',
                correct: false
            }
        ],
        explanation: 'int[][] arr1 = {1, 4}; snippet is invalid. In order to create a 2D array, each s' +
                'ub-array (row) should also be an array.\n\nint[][] arr2 = {{7}, {5, 1}}; snippet' +
                ' is valid. It creates a 2D array where the first row has one element 7 and the s' +
                'econd row has two elements 5 and 1.\n\nint[][] arr3 = {{1, 2, 3, 4}, {12, 7}, {3' +
                '' +
                '' +
                '1}};  snippet is valid. It creates a 2' +
                'D array with three rows where the first row has four elements, the second row ha' +
                's two elements, and the third row has one element.\n\nint[][] arr4 = new int[3][' +
                '3]{{7}, {5, 1}}; snippet is invalid. When using new to create a multidimensional' +
                ' array, you donot need to provide initial values. You should create it like this' +
                ': int[][] arr4 = new int[3][3]; and then populate the array with values using ne' +
                'sted loops or individual assignments.'
    }, {
        id: 192,
        category: 5,
        isImage: false,
        title: "What is the output of a given code?",
        image: task192,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '12',
                correct: false
            }, {
                id: 3,
                content: 'Compilation error',
                correct: true
            }, {
                id: 4,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'Return will never execute, since it is written after continue operator, making t' +
                'his code to show compilation error.'
    }, {
        id: 193,
        category: 5,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode193,
        answers: [
            {
                id: 1,
                content: '12',
                correct: false
            }, {
                id: 2,
                content: '1234',
                correct: true
            }, {
                id: 3,
                content: 'Compilation error',
                correct: false
            }, {
                id: 4,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'This code snippet produces 1234 as an output. continue statement inside of if bo' +
                'dy, has no effect, since it is basically ending current loop, which is already a' +
                't the last line.'
    }
]