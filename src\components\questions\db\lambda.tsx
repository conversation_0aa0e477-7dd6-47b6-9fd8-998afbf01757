import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { javaCode1033, javaCode1034, javaCode1035, javaCode1036 } from "./snippets/codeSnip";
export const lambdaDB : Questions[] = [
    {
        id: 1032,
        category: 9,
        isImage: false,
        title: "Which method is represents Predicate interface?",
        answers: [
            {
                id: 1,
                content: 'public abstract boolean test(T t);',
                correct: false
            }, {
                id: 2,
                content: 'public abstract void test(T t);',
                correct: false
            }, {
                id: 3,
                content: 'public boolean test(T t);',
                correct: true
            }, {
                id: 4,
                content: 'public <T> test(T t);',
                correct: false
            }, {
                id: 5,
                content: 'public void test(T t);',
                correct: false
            }
        ],
        explanation: 'Java Predicate Interface - It is a functional interface which represents a predi' +
                'cate (boolean-valued function) of one argument.\n \nIt is defined in the java.ut' +
                'il.function package and contains test() a functional method: \n \npublic boolean' +
                ' test(T t)'
    }, {
        id: 1033,
        category: 9,
        isImage: false,
        title: "Which line/lines will return true?",
        image: javaCode1033,
        answers: [
            {
                id: 1,
                content: 'b1',
                correct: false
            }, {
                id: 2,
                content: 'b2',
                correct: false
            }, {
                id: 3,
                content: 'b3',
                correct: true
            }, {
                id: 4,
                content: 'b4',
                correct: false
            }
        ],
        explanation: 'This code snippet represents functionality of Predicates.\n \nb1 -> is a correct' +
                ' java snippet, which donot lead to any compilation error, but however this code ' +
                'snippet results to false, since list1 is not empty, it has null object.\n \nb2, ' +
                'b4 -> will throw compilation error, because we have parameter of lambda e' +
                'xpression with the same name which is the violation (you cannot reuse the variab' +
                'le names that have already been used to define new variables in your argument li' +
                'st ) -> compilation error.\n \nb3 -> is the exact opposite of b1, it is checking' +
                ' if list1 is not empty -> true.'
    }, {
        id: 1034,
        category: 9,
        isImage: false,
        image: javaCode1034,
        title: "What does the code snippet output when executed?",
        answers: [
            {
                id: 1,
                content: "Alice Bob Charlie",
                correct: true
            }, {
                "id": 2,
                content: "AliceBobCharlie",
                correct: false
            }, {
                "id": 3,
                content: "null",
                correct: false
            }, {
                "id": 4,
                content: "An error will occur.",
                correct: false
            }
        ],
        explanation: "The code snippet uses a lambda expression to print each element of the 'names' l" +
                "ist, resulting in 'Alice', 'Bob', and 'Charlie'"
    }, {
        id: 1035,
        category: 9,
        isImage: false,
        image: javaCode1035,
        title: "Which of the following options can be inserted above so that it will print [John" +
                ", Andrea]",
        answers: [
            {
                id: 1,
                content: "{return st.grade > 90;}",
                correct: false
            }, {
                id: 2,
                content: "return st.grade > 90;",
                correct: false
            }, {
                id: 3,
                content: "{return st.grade >= 90;}",
                correct: true
            }, {
                id: 4,
                content: "return st.grade >= 90;",
                correct: false
            }, {
                id: 5,
                content: "st.name.length()==4 || st.name.length() == 6",
                correct: true
            }, {
                id: 6,
                content: "st.grade > 90 || st.name.length() == 6",
                correct: false
            }
        ],
        explanation: "This code snippet uses lambda expression to filter among the students.\n \nIn or" +
                "der to find the legit lambda expression we have to evaluate all of the possibili" +
                "ties. \n \nFirstly if you write return statement in your method body, you must e" +
                "nclose it within curly braces and include the semi-colon -> Options s2 and s4 are " +
                "out.\n \nOption s1 is syntatically correct, but returns only Andrea as a result. " +
                "This happens because John has 90 grades, which is not fitting to the condition -" +
                "> so Option s1 is out.\n \nOption s3-> returns John and Andrea because it returns " +
                "all students whose grade is equals or higher 90 -> so true.\n \nOption s5 is tric" +
                "ky -> basically here we are evaluating length of the names John -> 4 and Andrea " +
                "-> 6, all other students have either s3 or s5. So this option fits.\n \nOption s6 r" +
                "eturns only Andrea, because only Andrea has 6 letters in his name and only Andre" +
                "a has 98 grade which is higher 90.\n \nSo summing up -> s3 and s5"
    }, {
        id: 1036,
        category: 9,
        isImage: false,
        image: javaCode1036,
        title: "Which of the following options can be inserted above so that it will print [Set," +
                " Anita]",
        answers: [
            {
                id: 1,
                content: "{return st.grade < 80;}",
                correct: true
            }, {
                id: 2,
                content: "st.name.length() <= 5 && st.grade < 80",
                correct: true
            }, {
                id: 3,
                content: "{return st.name.length() <= 5;}",
                correct: false
            }, {
                id: 4,
                content: "st.name.length() <= 5 || st.grade < 80",
                correct: false
            }
        ],
        explanation: "This code snippet uses lambda expression to filter among the students.\n \nAll t" +
                "he given answers are syntactically correct and hence donot throw compilation err" +
                "ors.\n \nOption 1 returns students whose grade is lower than 80, so our students" +
                " is the only students whose grade are lower than 80 -> true.\n \nOption 2 -> pro" +
                "vides a list of students whose name length is less than 5 and whose grade is les" +
                "s than 80 -> if the first condition returns -> John and Helen together with our " +
                "answers, AND operators throws them out since their grade is higher than 80 so th" +
                "is option is also true.\n \nOption 3 returns -> John Set Helen and Anita -> fals" +
                "e.\n \nOption 4 returns the same since we have OR condition which donot exclude " +
                "John and Helen. \n \nSo to sum up 1 and 2"
    }

];