import { Questions } from "../../models/Questions";
import { Image } from 'react-native';
import { task171, task172, task173, task174, task175 } from "../exam/snips/basicSnips";
export const loopsBD: Questions[] = [
    {
        id: 171,
        category: 3,
        isImage: false,
        title: "How many times Loop iterated will be printed?",
        image: task171,
        answers: [
            {
                id: 1,
                content: '0',
                correct: false
            }, {
                id: 2,
                content: '15',
                correct: false
            }, {
                id: 3,
                content: 'Unlimited',
                correct: true
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }
        ],
        explanation: 'Since there is no increment statement for i provided within the loop, the loop w' +
                'ill become an infinite loop if the condition is initially true (i < 15).\n \nIn this' +
                ' case, i is initialized as 0, and 0 is always less than 15, so the loop will run' +
                ' indefinitely, continuously printing "Loop iterated" to the console.\n \nNormally, a' +
                ' loop would include an increment or update statement to ensure that the loop eve' +
                'ntually terminates. However this statement is optional.'
    }, {
        id: 172,
        category: 1,
        isImage: false,
        title: "What will be printed in console?",
        image: task172,
        answers: [
            {
                id: 1,
                content: 'True',
                correct: false
            }, {
                id: 2,
                content: 'False',
                correct: false
            }, {
                id: 3,
                content: 'Compilation error at line 1',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error at line 2',
                correct: true
            }, {
                id: 5,
                content: 'Compilation error at line 3',
                correct: false
            }, {
                id: 6,
                content: 'Compilation error at line 4',
                correct: false
            }
        ],
        explanation: 'Line 1 declares 2 local variables, however only 1 of them => x2 is initialized, ' +
                'this leads to the compilation error at line 2 with the following message: The lo' +
                'cal variable x1 may not have been initialized. The rest of the code works fine i' +
                'f x1 is also assigned some value'
    }, {
        id: 173,
        category: 5,
        isImage: false,
        title: "What will be printed in console?",
        image: task173,
        answers: [
            {
                id: 1,
                content: '8 / 8',
                correct: false
            }, {
                id: 2,
                content: '2 / 1',
                correct: true
            }, {
                id: 3,
                content: '1 / 2',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }
        ],
        explanation: 'This code snippet represents while loop. Important note here that if while loop ' +
                'doesnot have brackets after, it will take only the first method as its body. \n \nThe' +
                'refore we have only y-- operation and our x++ is repeated only once making its v' +
                'alue 2.\n \nSo x => 2 , y => 1'
    }, {
        id: 174,
        category: 5,
        isImage: false,
        title: "What will be printed in console?",
        image: task174,
        answers: [
            {
                id: 1,
                content: '1234',
                correct: false
            }, {
                id: 2,
                content: '12345',
                correct: false
            }, {
                id: 3,
                content: '123456',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: true
            }
        ],
        explanation: 'This code uses a do-while loop, which is a type of loop where the loop body is e' +
                'xecuted at least once before checking the loop condition.\nSince the variable y w' +
                'as declared inside the loop body, it is not accessible outside the loop, which l' +
                'eads to compilation error'
    }, {
        id: 175,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task175,
        answers: [
            {
                id: 1,
                content: '2',
                correct: false
            }, {
                id: 2,
                content: '5',
                correct: false
            }, {
                id: 3,
                content: '0\n2\n3',
                correct: false
            }, {
                id: 4,
                content: '2\n3',
                correct: true
            }, {
                id: 5,
                content: 'Compilation error',
                correct: false
            }
        ],
        explanation: 'This Java code defines three constants a (with a value of 1) and b (with a value' +
                ' of 2), and an integer variable c (with a value of 2).\n \nIt uses a switch statemen' +
                't with cases that match the value of c to print "1", "2", and "3" sequentially t' +
                'o the console.\n \nThe absence of break statements between cases causes the code to ' +
                'continue executing subsequent cases after a match is found. Since the value inside of switch' +
                ' is 2, then case b will be execute printing -> 2 and since absence of break it leads to the ' +
                ' execution of the next line case a+b which will print -> 3.\n \nSo 23 is our result'
    }
];