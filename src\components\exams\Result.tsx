import {
  Feather,
  MaterialCommunityIcons,
  MaterialIcons,
} from "@expo/vector-icons";
import React, { Component } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
  Text,
  TouchableOpacity
} from "react-native";
import { generateRandomCode } from "../../service/RandomService";
import { QuestionService } from "../../service/QuestionService";
import {
  AnswerSheet,
  QuestionContentService,
} from "../../service/QuestionContentService";
import { InitExamService } from "../../service/InitExamService";
import Modal from "react-native-modal";

import { StatisticsService } from "../../service/StatisticsService";
import { AppBar, IconButton } from "@react-native-material/core";
import {
  BannerAd,
  BannerAdSize,
  TestIds,
} from "react-native-google-mobile-ads";
import Procents from "../../helpers/modals/Procents";
import { scale, verticalScale } from "react-native-size-matters";
import GradientContainer from "../../helpers/GradientContainer";
import { appColors } from "../../utils/appColors";
import LoadingDots from "react-native-loading-dots";


interface GeneralState {
  answerSheets: AnswerSheet[];
  loading: boolean;
  isModalVisible: boolean;
  percentage: number;

}

const deviceHeight = Dimensions.get("window").height;
const deviceWidth = Dimensions.get("window").width;

const adUnitId = __DEV__
  ? TestIds.BANNER
  : "ca-app-pub-5981144475529351/3452174828";

class Result extends Component<{}, GeneralState> {
  constructor(props: {}) {
    super(props);
    this.state = {
      answerSheets: [],
      loading: true,
      isModalVisible: false,
      percentage: 0.0,
    };
  }

  componentDidMount(): void {
    this.loadResults();
    this.loadResults = this.loadResults.bind(this);    
    this.props.navigation.addListener("focus", this.loadResults);
  }
  
  async loadResults() {
    try {
      var myAnswerSheet: AnswerSheet[] =
      await QuestionContentService.getAllAnswers();
      InitExamService.markAsCompleted(myAnswerSheet[0].examId);
      var updSheet: AnswerSheet[] = [];
      var total = myAnswerSheet.length;
      var wrong = 0;
      for (var sheet of myAnswerSheet) {
        updSheet.push(QuestionService.checkAnswers(sheet));
        if (sheet.isWrong) {
          wrong++;
        }
      }
      var wrongPercentage = (wrong * 100) / total;
      console.log("route params")
      console.log(this.props.route.params)
      if (this.props.route.params) {
        this.setState({ isModalVisible: false });
      } else {
        await QuestionContentService.finalStep(updSheet);
        await StatisticsService.uploadNewStatistic(
          updSheet,
          100 - wrongPercentage
        );
        this.setState({ isModalVisible: true });
      }
      this.setState({
        answerSheets: updSheet,
        loading: false,
        percentage: 100 - wrongPercentage,
      });
    } catch (error) {
      console.log(error);
    }
  }



  closeModal = () => {
    this.setState({ isModalVisible: false });
  };


    renderItemCustom = (currentSheet:AnswerSheet) => {
      var question = QuestionService.getQuestionById(currentSheet.questionId, currentSheet.examId);
      let meta = "\u2714";
      if(currentSheet.isWrong){
        meta = '\u274C';
      }
      return(
        <TouchableOpacity
        style={styles.containerCustom}
        key={generateRandomCode()}
        onPress={() => this.props.navigation.navigate("Response", {id: currentSheet.serialNumber}) }  
        >
        <View style={styles.rowDirection}>
          <View style={styles.iconContainer}>
              {
                currentSheet.isWrong ? (
                  <MaterialIcons name={"do-not-disturb-on"} color={"#e64526"} size={scale(35)} />
                ) : (
                  <MaterialCommunityIcons name={"checkbox-marked"} color={appColors.white} size={scale(35)} />
                )
              }
          </View>
          <View style={styles.content}>
            <View style={styles.mainContent}>
              <View style={styles.text}>                     
                <Text style={[styles.groupName]}>{"#"+question.id} {meta}</Text>
              </View>
              <Text style={styles.timeAgo}>{question.title}</Text>
            </View>
          </View>
          <View style={styles.iconContainer}>
              <MaterialCommunityIcons 
              name="chevron-right"
              color={appColors.white}
              size={scale(35)}
              style={styles.iconCenter}
            />
          </View>
        </View>
      </TouchableOpacity>
      )
    }

  render() {
    const { answerSheets, loading, isModalVisible, percentage } = this.state;
    return (
      <GradientContainer>
          <AppBar
            transparent={true}
            titleStyle={{ fontFamily: "AmorriaBrush", fontSize:scale(25)}}
            contentContainerStyle={{ marginTop:scale(35) }} 
            title={"Result"}
            leading={() => (
              <IconButton
                onPress={() => this.props.navigation.navigate("MyTabsNew")}
                icon={() => ( <Feather name="arrow-left-circle" color={appColors.white} size={scale(30)} /> )}
              />
            )}
          />
        <Modal
          isVisible={isModalVisible}
          animationInTiming={250}
          animationOutTiming={500}
          coverScreen={false}
          deviceHeight={deviceHeight}
          deviceWidth={deviceWidth}
        >
          <Procents
            percentage={percentage}
            closeModal={() => this.closeModal()}
          />
        </Modal>
        <ScrollView>
          {loading ? (
            <View style={styles.dotsContainer}>
                <LoadingDots
                    size={scale(35)}
                    colors={[appColors.white, appColors.white, appColors.white]}
                    dots={3}
                    borderRadius={scale(15)}/>
              </View>
          ) : (
            answerSheets.map(this.renderItemCustom)
          )}
        </ScrollView>
        <View style={styles.ads}>
          <BannerAd
            unitId={adUnitId}
            size={BannerAdSize.INLINE_ADAPTIVE_BANNER}
            requestOptions={{
              requestNonPersonalizedAdsOnly: false,
            }}
          />
        </View>
      </GradientContainer>
    );
  }
}

const styles = StyleSheet.create({
  rowDirection: {
    flexDirection: "row",
  },
  iconContainer: {
    flexDirection: "column",
    margin: scale(5),
    alignSelf: "center",
  },
  content: {
    flex: 1,
    marginLeft: scale(3),
    marginTop: scale(5),
    flexDirection: "column",
  },
  mainContent: {
    marginRight: scale(5),
  },
  iconCenter: {},
  text: {
    marginBottom: scale(5),
    flexDirection: "row",
    flexWrap: "wrap",
  },
  groupName: {
    fontSize: scale(16),
    fontFamily: "AmorriaBrush",
    color:appColors.white
  },
  timeAgo: {
    marginBottom: "1%",
    fontSize: scale(14),
    color: appColors.white,
    fontFamily: "LatoRegular",
  },
  itemContainer: {
    alignItems: "center",
    marginBottom: 20,
    shadowColor: "#cccccc",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 1,
    marginHorizontal: 20,
    backgroundColor: "#fff",
    borderRadius: 10,
    paddingBottom: 20,
  },
  coverPhoto: {
    width: "100%",
    height: 60,
    resizeMode: "cover",
  },
  avatarContainer: {
    alignItems: "center",
    marginTop: -35,
  },
  avatar: {
    backgroundColor: "white",
  },
  name: {
    marginTop: 5,
    fontSize: 14,
    fontWeight: "bold",
  },
  ads: {
    maxHeight: "10%",
    width: "100%",
  },
  containerCustom: {
    padding: scale(8),
    width: "100%",
    flexDirection: "row",
    borderBottomWidth: scale(1),
    borderColor: "#FFFFFF",
    alignItems: "flex-start",
  },
  dotsContainer: {
    marginHorizontal: scale(40),
    marginTop: verticalScale(190),
    width: '40%',
    alignContent: 'center',
    alignSelf: 'center',
    height: '15%',
    justifyContent: 'center',
  },
});

export default Result;
