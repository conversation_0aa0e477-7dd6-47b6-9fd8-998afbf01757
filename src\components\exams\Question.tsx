import React, {Component, ReactElement, RefObject } from 'react';
import {
    Text,
    View,
    StyleSheet,
    ScrollView,
    ActivityIndicator, 
    Dimensions,
} from 'react-native';
import {AppBar, IconButton} from "@react-native-material/core";
import BouncyCheckbox from "react-native-bouncy-checkbox";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>} from "@react-native-material/core";
import {AntD<PERSON>, Feather, FontAwesome5, Ionicons, MaterialIcons} from "@expo/vector-icons";
import { QuestionService } from '../../service/QuestionService';
import {Answer, Questions} from '../models/Questions';
import { getCategory} from '../models/Categories';
import { generateRandomCode } from '../../service/RandomService';
import { MarkedQuestionsService } from '../../service/MarkedQuestionsService';
import { AnswerSheet, QuestionContentService } from '../../service/QuestionContentService';
import { BannerAd, BannerAdSize, TestIds, InterstitialAd, AdEventType } from 'react-native-google-mobile-ads';
import Modal from "react-native-modal";
import ReportQuestion from '../../helpers/modals/ReportQuestion';
import AdLoading from '../../helpers/modals/AdLoading';
import { scale } from 'react-native-size-matters';
import GradientContainer from '../../helpers/GradientContainer';
import { appColors } from '../../utils/appColors';
import CodeEditor, { CodeEditorSyntaxStyles } from '@rivascva/react-native-code-editor';

const deviceHeight = Dimensions.get('window').height;
const deviceWidth = Dimensions.get('window').width;

interface QuestionState {
    dataset : AnswerSheet[],
    currentAnswerSheet : AnswerSheet,
    currentQuestion:Questions,
    marked:boolean,
    showAnswer:boolean,
    position:number;
    isModalVisible:boolean;
    adModalVisible:boolean;
    codeFontSize:number;
}

const adUnitId = __DEV__ ? TestIds.BANNER : 'ca-app-pub-5981144475529351/3452174828';
const interstUNIT_ID = __DEV__ ? TestIds.INTERSTITIAL : 'ca-app-pub-5981144475529351/4273715172';


class Question extends Component < {}, QuestionState > {
    private interstitial: InterstitialAd;
    private scrollViewRef: RefObject<ScrollView> = React.createRef<ScrollView>();


    constructor(props : {}) {
        super(props);
        this.state = {
            showAnswer:false,
            dataset: [],
            currentQuestion: null,
            currentAnswerSheet:null,
            marked:false,
            position:1,
            isModalVisible:false,
            adModalVisible:false,
            codeFontSize:scale(13),
        }
        this.interstitial = InterstitialAd.createForAdRequest(interstUNIT_ID, {
            requestNonPersonalizedAdsOnly: false,
        });
    }


    componentDidMount() {
        this.loadQuestion = this.loadQuestion.bind(this);
        this.loadQuestion();
        this.loadInterAd = this.loadInterAd.bind(this);
        this.loadInterAd();
        this.props.navigation.addListener('focus', this.loadQuestion);
    }

    loadInterAd() {
        const {interstitial} = this;
      
        try {      
            interstitial.addAdEventListener( AdEventType.LOADED, () => {
              console.log('Intertitial ad loaded');
            });
      
            interstitial.addAdEventListener(AdEventType.ERROR,(error) => {
              //console.log('Intertitial ad failed to load:', error);
              interstitial.load();
            });
      
            
      
            interstitial.addAdEventListener(AdEventType.CLOSED, () => {
              interstitial.load();
            });
            interstitial.load();
            
          }catch (error) {
            console.log('Intertitial to create ad:', error);
          }
          
    }

    async loadQuestion() {
        if (this.props.route.params) {
            const {id} = this.props.route.params;
            if (id!== undefined || id !==null) {
                var datasetAnswerFormPromise:Promise<AnswerSheet[]> = QuestionContentService.getAllAnswers();
                var answerFormPromise = QuestionContentService.getAnAnswerSheet(id);

                answerFormPromise.then((currentForm) => {
                    datasetAnswerFormPromise.then( (allForms) => {
                        this.setState({dataset:allForms, position:currentForm.serialNumber,currentAnswerSheet:currentForm});
                        this.setState({currentQuestion:QuestionService.getQuestionById(currentForm.questionId,currentForm.examId)});
                    })
                })

            } else {
                this.props.navigation.navigate('ListQuestions');
            }
        }
    }

    

    scrollToTop = () => {
        if (this.scrollViewRef.current) {
          this.scrollViewRef.current.scrollTo({ x: 0, y: 0, animated: false });
        }
    };


    clickPrev = () => {
        var {dataset, currentAnswerSheet,position} = this.state;
        QuestionContentService.updateAnswerSheet(currentAnswerSheet);
        if(position!=0){
            dataset[position].selectedAnswers = currentAnswerSheet.selectedAnswers;
            this.setState({currentAnswerSheet:dataset[position-1]});
            this.setState({currentQuestion:QuestionService.getQuestionById(dataset[position-1].questionId,dataset[position].examId), position:position-1});
            this.clean();
            this.scrollToTop();
            return;            
        }

    }
    clickNext = () => {
        var {dataset, currentAnswerSheet, position} = this.state;
        QuestionContentService.updateAnswerSheet(currentAnswerSheet);
        if(position+1<dataset.length){
            dataset[position].selectedAnswers = currentAnswerSheet.selectedAnswers;
            this.setState({currentAnswerSheet:dataset[position+1]});
            this.setState({currentQuestion:QuestionService.getQuestionById(dataset[position+1].questionId,dataset[position].examId), position:position+1});
            this.clean();
            this.scrollToTop();
            return;
        }
    }

    clickNavBack = () =>{
        var { currentAnswerSheet } = this.state;
        QuestionContentService.updateAnswerSheet(currentAnswerSheet);
        this.props.navigation.navigate("ListQuestions")
    }

    clean = () => {
        this.setState({showAnswer:false, marked:false})
    }

    clickAnswer = () => {
        this.setState({showAnswer:true});
        const { interstitial } = this;
        if(interstitial.loaded){
            interstitial.show();
        }        
    }

    adIsLoading = () => {
        this.setState({adModalVisible:true});
    }

    closeAdModal = () => {
        this.setState({adModalVisible:false})
    }

    clickIncreaseFontSize = () => {
        let {codeFontSize} = this.state;
        this.setState({codeFontSize:codeFontSize + 1});
    }

    clickDecreaseFontSize = () => {
        let {codeFontSize} = this.state;
        this.setState({codeFontSize:codeFontSize - 1});
    }
    
    clickMark = () => {
        var {currentQuestion,marked} = this.state;
        if(marked){
            MarkedQuestionsService.removeMarkQuestions(currentQuestion.id);
        }else{
            MarkedQuestionsService.updateMarkQuestions(currentQuestion.id);
        }
        this.setState({marked:!marked})

    }
    clickFinish = () => {
        const {interstitial} = this;
        var { currentAnswerSheet } = this.state;
        QuestionContentService.updateAnswerSheet(currentAnswerSheet).then(() => {
            if(interstitial.loaded){
                interstitial.show();
            }
            this.props.navigation.navigate("Result");
        });        
    }

    shootButton = (action:string) => {
        switch(action){
            case 'mark': this.clickMark();break;
            case 'unmark': this.clickMark();break;
            case 'answer': this.clickAnswer();break;
            case 'report': this.clickReport();break;
            case 'submit': this.clickFinish();break;
            default:break;
        }
    }

    addAnswer = (id: number, isChecked:boolean) => {
        var {currentAnswerSheet} = this.state;
        if(isChecked){
            currentAnswerSheet.selectedAnswers.push({id});            
        }else{
            const indexToRemove = currentAnswerSheet.selectedAnswers.findIndex((answer) => answer.id === id);
            if (indexToRemove !== -1) {
                currentAnswerSheet.selectedAnswers.splice(indexToRemove, 1);
            }
        }
    }

    isSelectedAnswer = (id: number) => {
        var {currentAnswerSheet} = this.state;
        return currentAnswerSheet.selectedAnswers.map((answer) => answer.id).find((element) => element === id)!==undefined ? true : false;
    }

    clickReport = () => {
        this.setState({isModalVisible:true});
    }

    closeModal = () => {
        this.setState({isModalVisible:false})
    }


    renderItem = (answer : Answer) => {
        const {showAnswer} = this.state;
        return (
        <React.Fragment key={generateRandomCode()}> 
            <Divider style = {{ marginVertical: 15 }}/>
            <BouncyCheckbox
                key={answer.id}
                size={25}
                textContainerStyle={{minWidth:'10%', maxWidth:'90%'}}
                fillColor={appColors.blue}
                unfillColor="#FFFFFF"
                text={answer.content}
                iconStyle={{borderColor: "black"}}
                innerIconStyle={{borderWidth: 2}}
                isChecked={this.isSelectedAnswer(answer.id)}
                onPress={(isChecked : boolean) => this.addAnswer(answer.id, isChecked)}
                textStyle={{
                textDecorationLine: "none",
                color: (answer.correct && showAnswer ? appColors.white : appColors.black),
                fontFamily: 'LatoRegular',
                fontSize:16,
                backgroundColor: (answer.correct && showAnswer ?  appColors.blue : null)
                }}
                /> 
            < Divider style = {{ marginVertical: 15 }}/>
        </React.Fragment>
         );
    };

    renderImage(reactComponent: ReactElement){
        return reactComponent;
    }

    

    render() {
        const {currentQuestion, dataset, codeFontSize, marked,showAnswer, position,isModalVisible, adModalVisible} = this.state;    
        if (currentQuestion === undefined || currentQuestion === null) {
            return   <ActivityIndicator size="large" color="black" />
        }
        var title =getCategory(currentQuestion.category).title;
        var subtitle = position+1+"/"+dataset.length;
        return (
            <GradientContainer>
            <Modal
                isVisible={isModalVisible}
                animationInTiming={250}
                animationOutTiming={500}
                coverScreen={false}
                deviceHeight={deviceHeight}
                deviceWidth={deviceWidth}>
            <ReportQuestion 
                currentQuestion={currentQuestion}
                closeModal={() => this.closeModal()}
            />
            </Modal>
            <Modal
                isVisible={adModalVisible}
                animationInTiming={250}
                animationOutTiming={500}
                coverScreen={false}
                deviceHeight={deviceHeight}
                deviceWidth={deviceWidth}>
            <AdLoading 
                closeModal={() => this.closeAdModal()}
            />
            </Modal>
            <AppBar
                    title={title + " "+subtitle}
                    transparent={true}
                    titleStyle={{ fontFamily: "AmorriaBrush", fontSize:scale(20) }}
                    contentContainerStyle={{
                        marginTop:scale(35),
                    }} 
                    leading={(props) => (
                        <IconButton
                        onPress={() => {this.clickNavBack()}}
                        icon={props => <Feather name="arrow-left-circle" size={scale(props.size+10)} color={props.color} />}
                          {...props}
                        />
                    )}
                
                />
            <ScrollView ref={this.scrollViewRef} contentContainerStyle={{flexGrow: 1}}>

                <View style={styles.header} key={generateRandomCode()}>
                    <View style={styles.topBar}>
                        <View>
                            <Text key={generateRandomCode()} style={styles.subText}>#{currentQuestion.id} {currentQuestion.title}</Text>
                            <Text style={styles.multiple}>Multiple answers are allowed</Text>
                            <Divider style = {{ marginVertical: 5, backgroundColor:'black' }}/>
                            {currentQuestion.isImage
                                ? this.renderImage(currentQuestion.image)
                                : currentQuestion.image!=undefined
                                ?
                                <View style={styles.columnOne}>
                                    <View style={styles.iconRow}>
                                        <IconButton 
                                            icon={props => <Feather name="zoom-in" size={40} color="black" />} 
                                            onPress={ () => this.clickIncreaseFontSize()}
                                        />
                                        <IconButton 
                                            icon={props => <Feather name="zoom-out" size={40} color="black" />} 
                                            onPress={ () => this.clickDecreaseFontSize()}
                                        />
                                    </View>
                                    <ScrollView>
                                        <CodeEditor
                                            style={{
                                                fontSize: codeFontSize,
                                            }}
                                            readOnly={true}
                                            initialValue={String(currentQuestion.image)}
                                            language="java"
                                            syntaxStyle={CodeEditorSyntaxStyles.googlecode}
                                        />
                                    </ScrollView>
                                </View>                                
                                :null
                            }
                        </View>
                    </View>
                    <View style={styles.bottomBar}>
                        {currentQuestion.answers.map(this.renderItem)
                        }
                    </View>
                    {
                        showAnswer
                        ?
                        <View style={styles.explanationBar}>
                            <Text style={styles.explanationText}>{currentQuestion.explanation}</Text>
                        </View>
                        :
                        null
                    }
                    <View style={styles.buttonBar2}>
                        {   marked
                            ?
                            <Button
                                title="Unmark"
                                titleStyle={styles.buttonTitle}
                                style={styles.buttonStyle}
                                onPress={() => this.clickMark()}
                                leading={props => <Ionicons name="bookmarks-outline" {...props} />}
                            />
                            :
                            <Button
                                title="Mark"
                                titleStyle={styles.buttonTitle}
                                style={styles.buttonStyle}
                                onPress={() => this.clickMark()}
                                leading={props => <Ionicons name="bookmarks" {...props} />}
                            />
                        }

                        <Button
                            title="Report"
                            titleStyle={styles.buttonTitle}
                            style={styles.buttonStyle}
                            onPress={() => this.clickReport()}
                            leading={(props) => (<MaterialIcons name="bug-report" {...props}/>)}/>
                    </View>
                </View>

                <AppBar
                    variant="bottom"
                    color={appColors.blue}
                    style={{justifyContent:'center'}}       
                    leading={props => (
                        <IconButton icon={props => <AntDesign name="leftcircleo"  {...props} />} {...props} onPress={() => this.clickPrev()}/>
                    )}
                    trailing={props => (
                        <IconButton icon={props => <AntDesign name="rightcircleo"  {...props} />} {...props} onPress={() => this.clickNext()}/>
                    )}
                >
                      <View style={styles.appBarMenuStyle}>
                        <IconButton
                            onPress={() => this.clickFinish()}
                            icon={() => <FontAwesome5 name="flag-checkered" size={scale(30)} color={appColors.blue} />}  
                            style={{ backgroundColor:'white'}}                          
                        />
                        <IconButton
                            onPress={() => this.clickAnswer()}
                            icon={() => <AntDesign name="eye" size={scale(30)} color={appColors.blue} />}  
                            style={{ backgroundColor:'white'}}                          
                        />
                    </View>
                </AppBar>
            </ScrollView>            

            <View style={styles.ads}>
                <BannerAd 
                    unitId={adUnitId}
                    size={BannerAdSize.INLINE_ADAPTIVE_BANNER}
                    requestOptions={{ requestNonPersonalizedAdsOnly:false }}                        
                />
            </View>
            </GradientContainer>
        );
    }
}

const styles = StyleSheet.create({
    fullScreen:{ 
        flexDirection: 'row', 
        alignItems: 'center',
        height:'100%', 
        width:'100%' 
    },
    columnOne:{
        flexDirection:'column',
    },
    iconRow:{
        flexDirection:'row',
        justifyContent:'space-evenly',
    },
    codeStyle:{
        fontFamily:'MonocodeRegular',
    },
    rightIcon:{
        alignSelf:'flex-end',
    },

    loadedImage:{

    },

    header: {
        flex: 1,
        alignItems: 'center',
        backgroundColor: 'white',
		paddingHorizontal: scale(10),
    },
    topBar: {
        width: '100%',
        flexDirection: 'column',
    },
    appBarMenuStyle:{
        position:'absolute',
        alignSelf: "center", 
        flexDirection:'row', 
        width:"50%",
        justifyContent:'space-evenly'
    },
    explanationBar:{
        width: '95%',
        flex: 1,
    },
    explanationText:{
        color: 'black',
        fontSize: 15,
        fontFamily: "LatoLight",
        textAlign:'justify'
    },
    bottomBar: {
        marginTop: scale(10),
        flexDirection: 'column',
        padding: 5,
        width: '95%',
        flex: 1
    },
    subText: {
        marginTop: '3%',
        color: 'black',
        fontSize: 17,
        fontWeight:'bold',
        fontFamily: "LatoRegular"
    },
    multiple:{
        color: '#215E9D',
        fontSize: 12,
        fontFamily: 'LatoLight'
    },
    buttonBar2: {
		width: "100%",
		flexDirection: "row",
		justifyContent: "space-around",
        backgroundColor: 'white',
        paddingVertical: scale(15),
        maxHeight: scale(60),
		flex: 1,
    },
    buttonStyle: {
        backgroundColor: appColors.blue,
    },
    buttonTitle:{
        fontFamily:'LatoLight'
    },

    ads:{
        maxHeight:'5%',
        width:'100%',
    },
});

export default Question;
