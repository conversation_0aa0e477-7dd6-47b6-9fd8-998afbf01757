import React, {Component, ReactElement, RefObject} from "react";
import {
    Text,
    View,
    StyleSheet,
    ScrollView,
    ActivityIndicator,
    Dimensions
} from "react-native";
import {AppBar, IconButton} from "@react-native-material/core";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>} from "@react-native-material/core";
import {Ant<PERSON><PERSON>, <PERSON>ather, Ionicons, MaterialIcons} from "@expo/vector-icons";
import {QuestionService} from "../../service/QuestionService";
import {Answer, Questions} from "../models/Questions";
import {getCategory} from "../models/Categories";
import { generateRandomCode } from "../../service/RandomService";
import {MarkedQuestionsService} from "../../service/MarkedQuestionsService";
import {BannerAd, BannerAdSize, TestIds} from "react-native-google-mobile-ads";

import Modal from "react-native-modal";
import { SharedCheckbox } from "../shared/SharedCheckbox";
import ReportQuestion from "../../helpers/modals/ReportQuestion";
import { scale } from "react-native-size-matters";
import GradientContainer from "../../helpers/GradientContainer";
import { appColors } from "../../utils/appColors";
import CodeEditor, { CodeEditorSyntaxStyles } from "@rivascva/react-native-code-editor";

const deviceHeight = Dimensions
    .get("window")
    .height;
const deviceWidth = Dimensions
    .get("window")
    .width;

interface MarkedState {
    dataset : Questions[];
    currentQuestion : Questions;
    serialNumber : number;
    chapter : string;
    marked : boolean;
    isModalVisible : boolean;
    codeFontSize:number;
}

const adUnitId = __DEV__
    ? TestIds.BANNER
    : "ca-app-pub-5981144475529351/3452174828";

class Marked extends Component < {},
MarkedState > {
    private scrollViewRef: RefObject < ScrollView > = React.createRef < ScrollView > ();

    constructor(props : {}) {
        super(props);
        this.state = {
            serialNumber: 0,
            dataset: [],
            currentQuestion: null,
            chapter: "",
            marked: true,
            isModalVisible: false,
          
            codeFontSize:scale(12)
        };
    }

    componentDidMount() {

        this.loadQuestion = this
            .loadQuestion
            .bind(this);
        this.loadQuestion();
        this
            .props
            .navigation
            .addListener("focus", this.loadQuestion);


    }

    async loadQuestion() {
        if (this.props.route.params) {
            const {question} = await this.props.route.params;
            if (question) {
                var markedQuestions : Promise < number[] > = MarkedQuestionsService.getAllQuestions();

                markedQuestions.then((questions) => {
                    var questionSet : Questions[] = [];
                    for (var id of questions) {
                        var myQuestions = QuestionService.getQuestionById(id, 0);
                        questionSet.push(myQuestions);
                    }
                    this.setState({currentQuestion: question, dataset: questionSet});
                });
            } else {
                this.props.navigation.navigate('MyTabsNew', { screen: 'ListMarked' })
            }
        }
    }


    scrollToTop = () => {
        if (this.scrollViewRef.current) {
            this.scrollViewRef.current.scrollTo({x: 0, y: 0, animated: false});
        }
    };

    clickPrev = () => {
        var {currentQuestion, dataset} = this.state;
        for (var i = 0; i < dataset.length; i++) {
            if (dataset[i].id == currentQuestion.id && i != 0) {
                this.setState({
                    currentQuestion: dataset[i - 1],
                    serialNumber: i - 1,
                    marked: true
                });
                this.scrollToTop();
                return;
            }
        }
    };
    clickNext = () => {
        var {currentQuestion, dataset} = this.state;
        for (var i = 0; i < dataset.length; i++) {
            if (dataset[i].id == currentQuestion.id && i + 1 < dataset.length) {
                this.setState({
                    currentQuestion: dataset[i + 1],
                    serialNumber: i + 1,
                    marked: true
                });
                this.scrollToTop();
                return;
            }
        }
    };


    clickIncreaseFontSize = () => {
        let {codeFontSize} = this.state;
        this.setState({codeFontSize:codeFontSize + 1});
    }

    clickDecreaseFontSize = () => {
        let {codeFontSize} = this.state;
        this.setState({codeFontSize:codeFontSize - 1});
    }

    clickMark = () => {
        var {currentQuestion, marked} = this.state;
        if (marked) {
            MarkedQuestionsService.removeMarkQuestions(currentQuestion.id);
        } else {
            MarkedQuestionsService.updateMarkQuestions(currentQuestion.id);
        }
        this.setState({
            marked: !marked
        });
    };

    closeModal = () => {
        this.setState({isModalVisible: false});
    };

    clickReport = () => {
        this.setState({isModalVisible: true});
    };

    renderImage(reactComponent : ReactElement) {
        return reactComponent;
    }

    renderItem = (answer : Answer) => {
        return (
            <React.Fragment key={generateRandomCode()}>
                <SharedCheckbox 
                    answer={answer}
                    isChecked = {answer.correct}
                />
                <Divider
                    key={generateRandomCode()}
                    style={{
                    marginVertical: 15
                }}/>
            </React.Fragment>
        );
    };

    render() {
        const {
            currentQuestion,
            dataset,
            marked,
            serialNumber,
            codeFontSize,
            isModalVisible,
        } = this.state;
        if (currentQuestion === null) {
            return <ActivityIndicator size="large" color="black"/>;
        }
        var title = getCategory(currentQuestion.category).title;
        var subtitle = serialNumber + 1 + "/" + dataset.length;
        return (
            <GradientContainer>
                <Modal
                    isVisible={isModalVisible}
                    animationInTiming={250}
                    animationOutTiming={500}
                    coverScreen={false}
                    deviceHeight={deviceHeight}
                    deviceWidth={deviceWidth}
                    style={{
                    marginTop: '15%'
                    }}>
                    <ReportQuestion
                        currentQuestion={currentQuestion}
                        closeModal={() => this.closeModal()}/>
                </Modal>
                <AppBar
                    transparent={true}
                    titleStyle={{ fontFamily: "AmorriaBrush", fontSize:scale(25)}}
                    contentContainerStyle={{ marginTop:scale(35) }} 
                    title={title + " "+subtitle}
                    leading={() => (
                        <IconButton
                        onPress={() => this.props.navigation.navigate('MyTabsNew', { screen: 'ListMarked' })}
                            icon={(props) => (<Feather name="arrow-left-circle" size={props.size+20} color={appColors.white} />)}
                        />                            
                )}/>
                <ScrollView ref={this.scrollViewRef} contentContainerStyle={{flexGrow: 1}}>
                    <View style={styles.header}>
                        <View style={styles.topBar}>
                            <View style={styles.img}>
                                <Text key={generateRandomCode()} style={styles.subText}>
                                    #{currentQuestion.id} {currentQuestion.title}
                                </Text>
                                <Divider style = {{ marginVertical: 5, backgroundColor:'black' }}/>
                                {currentQuestion.isImage
                                ? this.renderImage(currentQuestion.image)
                                : currentQuestion.image!=undefined
                                ?
                                <View style={styles.columnOne}>
                                    <View style={styles.iconRow}>
                                        <IconButton 
                                            icon={props => <Feather name="zoom-in" size={40} color="black" />} 
                                            onPress={ () => this.clickIncreaseFontSize()}
                                        />
                                        <IconButton 
                                            icon={props => <Feather name="zoom-out" size={40} color="black" />} 
                                            onPress={ () => this.clickDecreaseFontSize()}
                                        />
                                    </View>
                                    <ScrollView>
                                        <CodeEditor
                                            style={{
                                                fontSize: codeFontSize,
                                            }}
                                            readOnly={true}
                                            initialValue={String(currentQuestion.image)}
                                            language="java"
                                            syntaxStyle={CodeEditorSyntaxStyles.googlecode}
                                        />
                                    </ScrollView>

                                </View>                                
                                :null
                            }
                            </View>
                        </View>
                        <View style={styles.bottomBar}>
                            {currentQuestion.answers.map(this.renderItem)}
                        </View>
                        <View style={styles.explanationBar}>
                            <Text style={styles.explanationText}>
                                {currentQuestion.explanation}
                            </Text>
                        </View>
                        <View style={styles.buttonBar2}>
                            {marked
                                ? (
                                    <Button
                                        title="Unmark"
                                        style={styles.buttonStyle}
                                        titleStyle={styles.buttonTitle}
                                        onPress={() => this.clickMark()}
                                        leading={(props) => (<Ionicons name="bookmarks-outline" {...props}/>)}/>
                                )
                                : (
                                    <Button
                                        title="Mark"
                                        style={styles.buttonStyle}
                                        titleStyle={styles.buttonTitle}
                                        onPress={() => this.clickMark()}
                                        leading={(props) => <Ionicons name="bookmarks" {...props}/>}/>
                                )}

                            <Button
                                title="Report"
                                style={styles.buttonStyle}
                                titleStyle={styles.buttonTitle}
                                onPress={() => this.clickReport()}
                                leading={(props) => (<MaterialIcons name="bug-report" {...props}/>)}/>
                        </View>
                    </View>
                    <AppBar
                        variant="bottom"
                        color={appColors.blue}
                        leading={(props) => (
                        <IconButton
                            icon={(props) => <AntDesign name="leftcircleo" {...props}/>}
                            {...props}
                            onPress={() => this.clickPrev()}/>
                    )}
                        trailing={(props) => (
                        <IconButton
                            icon={(props) => <AntDesign name="rightcircleo" {...props}/>}
                            onPress={() => this.clickNext()}
                            {...props}/>
                    )}
                        style={{
                        position: "absolute",
                        start: 0,
                        end: 0,
                        bottom: 0
                    }}></AppBar>
                </ScrollView>
                <View style={styles.ads}>
                    <BannerAd
                        unitId={adUnitId}
                        size={BannerAdSize.INLINE_ADAPTIVE_BANNER}
                        requestOptions={{
                        requestNonPersonalizedAdsOnly: false
                    }}/>
                </View>
            </GradientContainer>
        );
    }
}

const styles = StyleSheet.create({
	fullScreen: {
		flexDirection: "row",
		alignItems: "center",
		height: "100%",
		width: "100%",
	},
	codeStyle: {
		fontFamily: 'MonocodeRegular',
	},
	columnOne: {
		flexDirection: 'column',
	},
	iconRow: {
		flexDirection: 'row',
		justifyContent: 'space-evenly',
	},
	rightIcon: {
		alignSelf: 'flex-end',
	},
	header: {
		flex: 1,
		alignItems: "center",
		backgroundColor: "white",
		paddingHorizontal: scale(10),
		paddingBottom: "20%",
		height: "80%",
	},
	topBar: {
		width: "100%",
		flexDirection: "column",
	},
	img: {},
	bottomBar: {
        marginTop: scale(10),
		flexDirection: "column",
        padding: 5,
		width: "95%",
		flex: 1,
	},
	explanationBar: {
		width: "95%",
		flex: 1,
	},
	explanationText: {
		color: "black",
		fontSize: scale(15),
		fontFamily: "LatoLight",
		textAlign: "justify",
	},
	subText: {
		marginTop: scale(3),
		color: "black",
		fontSize: scale(17),
		fontFamily: "LatoRegular",
	},
	buttonBar2: {
		width: "100%",
		flexDirection: "row",
		justifyContent: "space-around",
        backgroundColor: 'white',
        paddingVertical: scale(15),
        maxHeight: scale(60),
		flex: 1,
	},
	buttonStyle: {
		backgroundColor: appColors.blue,
	},
	buttonTitle: {
		fontFamily: 'LatoLight',
	},
	ads: {
		maxHeight: "5%",
		width: "100%",
	},
});


export default Marked;
