export const javaCode1022 = `
public class Student {

    public static final int bestGrade = 100;

    public static void crazySwitch(int grade) {  
        String val = "";  
        switch (grade) {  
            case 2:
            default:  
                val = "def";
            case bestGrade:  
                val = "best";  
                break;  
        }  
        System.out.println(val);  
    }
}
`;

export const javaCode1023 = `
public static void crazyIf(boolean passedBoolean){    
    boolean localBoolean = false;    
    if (passedBoolean != localBoolean = !passedBoolean) {    
        System.out.println("true");    
    } else {    
        System.out.println("false");    
    } 
}

public static void main(String[] args) {    
    crazyIf(true);    
} 

`;

export const javaCode1024 = `
public static void crazySwitch(String[] localArgs){  
    switch(localArgs.length) {  
        case 0:
            System.out.print("no params");  
        default:
            System.out.print("too many params");  
        case 1:
            System.out.print("1 param");  
    }  
}
`;

export const javaCode1025 = `
public static void crazySwitch(int val) { 
    switch(val) { 
        default:
            System.out.print("~"); 
            break;
        case 3:
            System.out.print("3"); 
            break;
        case 4:
            System.out.print("4"); 
            break;
        case 1:
            System.out.print("1"); 
            break;
        case 2:
            System.out.print("2"); 
            break;
    } 
}
`;

export const javaCode1027 = `
public static void crazyIf(boolean passedBoolean) { 
    boolean localBoolean = false; 
    if (passedBoolean = localBoolean != passedBoolean) { 
        System.out.println("true"); 
    } else { 
        System.out.println("false"); 
    } 
}
`;

export const javaCode1028 = `
public static void main(String args[]) {  
    int i = 1;  
    boolean bool1 = true;  
    boolean bool2 = true;  
    boolean bool = false;  
    bool = (bool2 & alwaysTrue(i++));  
    bool = (bool && alwaysTrue(++i));  
    bool = (bool | alwaysTrue(++i));  
    bool = (bool1 || alwaysTrue(i++));  
}

public static boolean alwaysTrue(int line) {  
    System.out.println(line);  
    return true;  
}
`;

export const javaCode1029 = `
public static int crazyIncrements() {
    int a1 = -4;
    int z1 = a1-- - 1;
    int b1 = ++z1 + a1--;
    if (z1 > b1) {
        b1 = b1-- + 5;
    } else {
        a1 = a1++ - 3;
    }
    return a1 + z1 + b1;
}

public static void main(String[] args) {
    System.out.println(crazyIncrements());
}

`;

export const javaCode1030 = `
public static void main(String[] args) {
    char c; byte b; int i;
    c = 'a'; 
    b = 1; 
    i = 0;
    i = b; // 1
    b = c; // 2
    c = i; // 3
    i = c; // 4
    b = i; // 5
}
`;

export const javaCode1031 = `
public void crazySwitch(byte x) {
    switch(x) {
        case 'A':
        default:
        case -100:
        case 80:
    }
}

`;

export const javaCode1033 = `
public static boolean tester(List list, Predicate<List> p) {  
    return p.test(list);  
}

public static void main(String[] args) {  
    List list = new ArrayList<>();  
    list.add(null);  
    boolean b1 = tester(list, list1 -> list1.isEmpty());  
    boolean b2 = tester(list, list -> list1.isEmpty());  
    boolean b3 = tester(list, list1 -> list1.isEmpty());  
    boolean b4 = tester(new ArrayList(), list -> list.remove(0));   
}
`;

export const javaCode1034 = `
public static void main(String[] args) {
    List<String> names = Arrays.asList("Alice", "Bob", "Charlie");
    names.forEach(name -> System.out.print(name + ' '));
}
`;

export const javaCode1035 = `
public static void filterStudents(List<Student> allStudents, Predicate<Student> p) { 
    for (Student st : allStudents) { 
        if (p.test(st)) { 
            System.out.println(st.name); 
        } 
    }
}

public static void main(String[] args) {
    List<Student> allStudents = new ArrayList<>();
    Student s1 = new Student("John", 90);
    Student s2 = new Student("Set", 75);
    Student s3 = new Student("Andrea", 98);
    Student s4 = new Student("Helen", 88);
    Student s5 = new Student("Anita", 76);
    allStudents.addAll(Arrays.asList(s1, s2, s3, s4, s5));
    filterStudents(allStudents, (Student st) -> _______________);
}
`;

export const javaCode1036 = `
public static void filterStudents(List<Student> allStudents, Predicate<Student> p) {
    for(Student st : allStudents) {
        if(p.test(st)) {
            System.out.println(st.name);
        }
    }
}

public static void main(String[] args) {
    List<Student> allStudents = new ArrayList<>();
    Student s1 = new Student("John", 90);
    Student s2 = new Student("Set", 75);
    Student s3 = new Student("Andrea", 98);
    Student s4 = new Student("Helen", 88);
    Student s5 = new Student("Anita", 76);
    allStudents.addAll(Arrays.asList(s1, s2, s3, s4, s5));
    filterStudents(allStudents, (Student st) ->  _______________);
}
`;

export const javaCode1037 = `
public static void main(String args[]) {
    String one = "1";
    String str = one + "2" + one + one;  
    str.replace("1", "*");
    String newStr = str.replace("2", "1"); 
    System.out.print(str + " / " + newStr); 
}
`;

export const javaCode1038 = `
String a(String dateOfBirth) { 
    String resp = "XX-XX-XXXX"; 
    return resp.substring(0, 6) + dateOfBirth.substring(6); 
} 

String b(String dateOfBirth) { 
    String resp = "XX-XX-XXXX"; 
    return dateOfBirth.substring(0, 6) + resp.substring(6); 
} 

String c(String dateOfBirth) { 
    StringBuilder sb = new StringBuilder("XXXX"); 
    return dateOfBirth.substring(0, 6).append(sb.toString());  
} 

String d(String dateOfBirth) { 
    StringBuilder sb = new StringBuilder(dateOfBirth); 
    sb.replace(6, 10, "XXXX"); 
    return sb.toString(); 
}
`;

export const javaCode1039 = `
public void newString(String s) {
    s.concat("World!"); 
}
    
public void newStringBuilder(StringBuilder s) {
    s.append("World!"); 
}
    
public static void main(String args[]) {
    Question q = new Question();
    String s = "Hello ";
    StringBuilder sb = new StringBuilder(s);
    q.newString(s);   
    q.newStringBuilder(sb); 
    System.out.print(s + "/ " + sb);
}
`;