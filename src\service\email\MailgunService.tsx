import axios from 'axios';
import * as qs from 'qs';
import { emailKey } from './key';

class MailgunService {
  private readonly domain: string;
  private readonly apiKey: string;

  constructor() {
    this.domain = 'sandboxf0479c5f6a9a444aae343739cdb8d266.mailgun.org';
    this.apiKey =  `api:${emailKey}`;
  }

  public  sendSimpleMessage = async(subject, text): Promise<void> => {  
    const dataParsed = qs.stringify({
      from: `Mailgun Sandbox <<EMAIL>>`,
      to: 'Java Oracle <<EMAIL>>',
      subject: subject,
      text: text,
    });
    try {
      const response = await axios.post(`https://api.mailgun.net/v3/sandboxf0479c5f6a9a444aae343739cdb8d266.mailgun.org/messages`, dataParsed, {
        auth: {
            username: 'api',
            password: emailKey,
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
    } catch (error) {
      console.error('Failed to send email via Mailgun:', error);
      throw error;
    }
  }
}

export default new MailgunService();