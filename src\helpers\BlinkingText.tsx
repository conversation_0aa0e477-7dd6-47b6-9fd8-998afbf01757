import React, {Component} from 'react';
import {Animated, Easing} from 'react-native';
import { appColors } from '../utils/appColors';

interface BlinkingState{
    fadeAnim: Animated.Value;
}

interface BlinkingProps{
    text: string,
    fontSize: number,
}

class BlinkingText extends Component < BlinkingProps, BlinkingState > {
    constructor(props: BlinkingProps) {
        super(props);
        this.state = {
            fadeAnim: new Animated.Value(0), // Initial value for opacity: 0
        };
    }


    async componentDidMount() {
        this.startAnimation()
    }

    startAnimation() {
        Animated.loop(Animated.sequence([
            Animated.timing(this.state.fadeAnim, {
                toValue: 1,
                duration: 1000,
                easing: Easing.linear,
                useNativeDriver: false
            }),
            Animated.timing(this.state.fadeAnim, {
                toValue: 0,
                duration: 1000,
                easing: Easing.linear,
                useNativeDriver: false
            })
        ]),).start();
    }

    render() {
        const {fadeAnim} = this.state;

        const interpolateColor = fadeAnim.interpolate({ 
            inputRange: [0, 0.5, 1],
            outputRange: [appColors.white, appColors.white, appColors.white]
        });

        const animatedStyle = {
            color: interpolateColor,
            opacity: fadeAnim
        };

        return (
                <Animated.Text
                    style={[
                    {
                        fontSize: this.props.fontSize,
                        textAlign: 'center',
                        fontFamily: "AmorriaBrush"
                    },
                    animatedStyle
                ]}>
                    {this.props.text}
                </Animated.Text>            
        );
    }
}

export default BlinkingText;
