import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import * as Font from 'expo-font'; // If using Expo
import { View, StyleSheet, StatusBar } from 'react-native';
import { appColors } from '../utils/appColors';
import { scale, verticalScale } from 'react-native-size-matters';
import LoadingDots from 'react-native-loading-dots';
import GradientContainer from './GradientContainer';

// Define font loading status in the context
interface FontContextType {
  isFontLoaded: boolean;
}

const FontContext = createContext<FontContextType>({ isFontLoaded: false });

const loadFonts = async () => {
  await Font.loadAsync({
    'LatoLight': require('../../assets/fonts/lato-light.ttf'),
    'LatoRegular': require('../../assets/fonts/lato-regular.ttf'),
    'MonocodeRegular': require('../../assets/fonts/monocode-regular.ttf'),
    'AmorriaBrush': require('../../assets/fonts/amorria-brush.ttf'),
  });
};

interface FontProviderProps {
  children: ReactNode;
}

export const FontProvider: React.FC<FontProviderProps> = ({ children }) => {
  const [isFontLoaded, setFontLoaded] = useState(false);

  useEffect(() => {
    (async () => {
      await loadFonts();
      setFontLoaded(true);
    })();
  }, []);

  if (!isFontLoaded) {
    return (
      <React.Fragment>
          <StatusBar 
            barStyle="default" 
            translucent={true}
            backgroundColor="transparent"
          />
        <GradientContainer>        
          <View style={styles.dotsContainer}>
            <LoadingDots
                size={scale(35)}
                colors={[appColors.white, appColors.white, appColors.white]}
                dots={3}
                borderRadius={scale(15)}/>
          </View>
        </GradientContainer>
      </React.Fragment>
    );
  }

  return <FontContext.Provider value={{ isFontLoaded }}>{children}</FontContext.Provider>;
};

const styles = StyleSheet.create({
    dotsContainer: {
      marginHorizontal: scale(40),
      marginTop: verticalScale(190),
      width: '40%',
      alignContent: 'center',
      alignSelf: 'center',
      height: '15%',
      justifyContent: 'center',
    },
});

export const useFont = () => useContext(FontContext);
