import React, {Component} from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import { generateRandomCode } from '../../service/RandomService';
import { AppBar, IconButton} from "@react-native-material/core";
import { BannerAd, BannerAdSize, TestIds } from 'react-native-google-mobile-ads';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Feather, FontAwesome } from '@expo/vector-icons';
import GradientContainer from '../../helpers/GradientContainer';
import { scale, ScaledSheet } from 'react-native-size-matters';
import { appColors } from '../../utils/appColors';


interface ContentState {
   
}

const adUnitId = __DEV__ ? TestIds.BANNER : 'ca-app-pub-5981144475529351/**********';

class Content extends Component < {},ContentState > {
    constructor(props : {}) {
        super(props);
        this.state = {
  
        };
    }

    renderContent = (content : string) => {
        return (
            <Text key={generateRandomCode()} style={styles.bodyText}>
                - {content}
            </Text>
        );
    };

    render() {
        const {route} = this.props;
        const {title, content} = route.params;

        return (
            <GradientContainer>
            <ScrollView>
                <AppBar
                    title={title}
                    transparent={true}
                    titleStyle={{ fontFamily: "AmorriaBrush", fontSize:scale(25), }}
                    contentContainerStyle={{
                        marginTop:scale(35),
                    }} 
                    leading={props => (
                        <IconButton
                        onPress={() => this.props.navigation.navigate('MyTabsNew', { screen: 'Chapters' })}
                        icon={props => <Feather name="arrow-left-circle" size={props.size+20} color={props.color} />}
                          {...props}
                    />
                    )} />
                <View style={styles.infoBody}>
                <View style={styles.body}>
                    {content.map(this.renderContent)}
                </View>
                </View>
            </ScrollView>
            <View style={styles.ads}>
                <BannerAd 
                    unitId={adUnitId}
                    size={BannerAdSize.INLINE_ADAPTIVE_BANNER}
                    requestOptions={{
                        requestNonPersonalizedAdsOnly:false
                    }}                        
                />
            </View>
        </GradientContainer>
        );
    }
}

const styles = ScaledSheet.create({
    infoBody: {
        marginTop: scale(5),
        paddingHorizontal: scale(20),
    },
    body: {
        marginTop: scale(10),
        paddingTop: scale(10),
    },
    bodyText: {
        fontSize: scale(20),
        fontFamily: 'LatoRegular',
        lineHeight: scale(24),
        color: appColors.white,
    },
    ads: {
        maxHeight: '10%',
        width: '100%',
    },
});

export default Content;
