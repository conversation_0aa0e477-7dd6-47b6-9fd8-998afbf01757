import React, {Component} from 'react';
import { View, StyleSheet, TouchableOpacity, Text, Image, Linking } from 'react-native';
import { appColors } from '../../utils/appColors';
import { scale, verticalScale } from 'react-native-size-matters';
import { LinearGradient } from 'expo-linear-gradient';
import DonateBlinking from './DonateBlinking';

interface CoffeeBannerProps{}

interface CoffeeBannerState{}

class CoffeeBanner extends Component < CoffeeBannerProps, CoffeeBannerState > {

    constructor(props : CoffeeBannerProps) {
        super(props);
        this.state = {};
    }

    openPaypal = () => {
        Linking.openURL('https://www.paypal.com/paypalme/sattar7')
        .catch(err => console.error('An error occurred', err));
    }

    render(){
        return(
            <LinearGradient colors={[appColors.white, appColors.whiteSecond]}>
                <TouchableOpacity style={styles.coffeeContainer} onPress={() => this.openPaypal()}>                
                <View style={styles.textContainer}>
                    <DonateBlinking text='Enjoying' fontSize={scale(25)}  />
                    <Text style={[styles.bodyStyle, {fontFamily: "MonocodeRegular"}]}>Keep it running with a small donation!</Text>
                </View>
                <Image
                    style={styles.photo}
                    source={require('../../img/p.png')}
                />
                </TouchableOpacity>
            </LinearGradient>            
        )
    }

}


const styles = StyleSheet.create({
    coffeeContainer:{
        flexDirection: "row",
        justifyContent:"space-evenly",        
        width: '100%',
        paddingVertical: scale(5)
    },
    bodyStyle:{
        textAlign: "center",
        fontSize: scale(12),
        color: appColors.blue,
    },
    textContainer:{
        alignContent :"center",
        alignItems : "center",
        justifyContent: "center"
    },
    photo: {
        width: scale(40),
        height: verticalScale(40),
        borderRadius: scale(30),
        alignSelf:"center"
    },
});

export default CoffeeBanner;