export const task150 = `
public class Student {

    public static void main(String[] args) {
        int A$B = 12;
        boolean _isWrong = true;
        boolean false = false;
        int java.util = 12;
        double Public = 14.2;
        double 7_cd = 12;
    }
}
`;

export const task151 = `
public class Student {

    public static void main(String[] args) {
        double d = 12.f; //1
        int in = 12.4; //2
        String str = "Hello World"; //3
        int n = str.length; //4
        int m = in.length(); //5
    }
}
`;

export const task152 = `
public class Student {

    public int age;
    public String name;

    public static void main(String[] args) {
        Student st = new Student();
        System.out.print(st.name+" / "+st.age);
    }
}
`;

export const task155 = `
public class Student {

    public static String name = "John";

    public static void grading(Number n) {
        System.out.println(name+" got "+n+" grades");
    }
    public static void main(String[] args) {
        grading(101254121); //1
        grading(0b111110); //2
        grading(0xc2B); //3
        grading(7_7_7); //4
    }
}
`;

export const task162 = `
public class Question {
    public static void main(String[] args) {
        byte a = 7;
        byte b = 14;
        ???? c = a + b;
    }
}
`;

export const task163 = `
public class Question {
    public static void main(String[] args) {
        long a = 7;
        int b = 14 + a;
    }
}
`;

export const task164 = `
public class Question {

    public static void main(String[] args) {
        boolean x = true, z = true;
        int y = 7;
        x = (y > 10) & (z = true);
        System.out.println(x + " / "+y+" / "+ z);
    }
}
`;

export const task165 = `
public class Question {
    public static void main(String[] args) {
        int a = 6 * 3 % 4;
        System.out.println(a);
    }
}
`;

export const task166 = `
public class Question {

    public static void main(String[] args) {
        int a;
        int b = 1;
        int c = 2;
        a = b + c;
        a += b++;
        System.out.println(a);
    }
}
`;

export const task167 = `
public class Question {

    public static void main(String[] args) {
        int a = 10;
        int b = 1;
        int x = 0;
        while(a > b) {
            a--;
            b++;
            x = a + b;
        }
        System.out.println(x);
    }
}
`;

export const task168 = `
public class Question {

    public static void main(String[] args) {
        int x = 5;
        int y = x++ + 2 * x;
        System.out.println(x + " / "+ y);
    }
}
`;

export const task169 = `
public class Question {

    public static void main(String[] args) {
        int x = 5;
        long longX = x * x + x++;
        if(longX < 30) System.out.println("Under 30");
        else System.out.println("More than 30");
        else System.out.println("Equals 30");
    }
}
`;

export const task170 = `
public class Question {

    public static void main(String[] args) {
        int x = 7;
        System.out.println(x > 5 ? x < 7 ? 10 : 11 : 12);
    }
}
`;

export const task171 = `
public class Question {

    public static void main(String[] args) {
        for(int i = 0; i < 15; ) {
            System.out.println("Loop iterated");
        }
    }
}
`;

export const task172 = `
public class Question {

    public static void main(String[] args) {
        int x1, x2 = 75; // 1
        boolean bool = x1 > x2; // 2
        if(bool = true) System.out.println("True"); // 3
        else System.out.println("False"); // 4
    }
}
`;

export const task173 = `
public class Question {

    public static void main(String[] args) {
        int x = 1;
        int y = 15;
        while(y > x)
        y--;
        x++;
        System.out.println(x + " / " + y);
    }
}
`;

export const task174 = `
public class Question {

    public static void main(String[] args) {
        do {
            int y = 1;
            System.out.print(y);
            y++;
        } while(y <= 5);
    }
}
`;

export const task175 = `
public class Question {

    public static void main(String[] args) {
        final int a = 1;
        final int b = 2;
        int c = 2;
        switch(c) {
            case 0: System.out.print(0);
            case a:
            case b: System.out.print(b);
            case a + b: System.out.print(a + b);
        }
    }
}
`;

export const task176 = `


public class Student {

    public String name;
    public int age;

    public static void main(String[] args) {
        int myAge = 30;
        String myName = "John";
        Student st = new Student();
        st.name = "Ben";
        System.out.println(myAge + st.age);
        System.out.println(myName + st.name);
    }
}
`;

export const task177 = `


public class Student {

    public static void main(String[] args) {
        String hello = "Hello";
        String hell = "Hell";
        String o = "o";

        if(hello == (hell + o)) { System.out.print(1); }
        if(hello == "Hello") { System.out.print(2); }
        if(hello == (hell.concat(o))) { System.out.print(3); }
        if(hell == hello.substring(0, 4)) { System.out.print(4); }
        if(o.equals("o")) { System.out.print(5); }
        if(o.equals(" o ".trim())) { System.out.print(6); }
    }
}
`;

export const task179 = `


public class Student {

    public static void main(String[] args) {
        StringBuilder sb = new StringBuilder();
        sb.append("Hello").insert(5, " world").insert(0, "John,");
        sb.toString().toLowerCase();
        System.out.println(sb);
    }
}
`;

export const task180 = `


public class Student {

    public static void main(String[] args) {
        String java = "java";
        StringBuilder sb = new StringBuilder(java);
        if(java == sb) {
            System.out.println(1);
        }
        if(java.equals(sb)) {
            System.out.println(2);
        }
    }
}
`;

export const task181 = `
public class Student {

    public static void main(String[] args) {
        String java = "123456789";
        StringBuilder sb = new StringBuilder(java);
        sb.append(0).delete(0, 2).deleteCharAt(5);
        java.substring(0, 7).substring(1, 6).substring(2);
        System.out.println(java);
        System.out.println(sb);
    }
}
`;

export const task182 = `
public class Student {

    public static void main(String[] args) {
        String java = "123456789";
        System.out.println(java.length);
        System.out.println(java.charAt(1));
        System.out.println(java.charAt(3));
    }
}
`;
// 183.png
export const task183 = `
public class Student {

    public static void main(String[] args) {
        String java = "123456789";
        System.out.println(java.substring(1, 5));
        System.out.println(java.substring(1, 7));
        System.out.println(java.substring(7, 7));
        System.out.println(java.charAt(10));
    }
}
`;

// 184.png
export const task184 = `
public class Student {

    public static void main(String[] args) {
        String java = "123456789";
        int amount = 0;
        amount = amount + java.substring(1, 3).length();
        amount = amount + java.substring(1, 5).length();
        amount = amount + java.substring(3, 5).length();
        System.out.println(amount);
    }
}
`;

// 185.png
export const task185 = `
public class Student {

    public static void main(String[] args) {
        String java = "123456789";
        StringBuilder sb = new StringBuilder(java);
        sb.append(false).insert(2, "+");
        sb.delete(2, 3);
        System.out.println(java);
        System.out.println(sb);
    }
}
`;

// 186.png
export const task186 = `
public class Student {

    public static void main(String[] args) {
	    int counter = 0;
	    LOOP1:
	    for(int i = 0; i < 3; i++)
	    LOOP2:
	    for(int j = 0; j < 3; j++){
		    if(i == j) {
		    	counter++;
		    }
		    else if((i + j) % 2 == 0) {
		    	continue LOOP1;
		    }
	    }
        System.out.println(counter);
    }
}
`;

// 188.png
export const task188 = `
public class Student {

    public static void main(String[] args) {
        int[] scores = new int[2];
        // int length = scores.length; -> 1
        // int length = scores.length(); -> 2
        // int length = scores.size; -> 3
        // int length = scores.size(); -> 4
        // int length = scores.capacity; -> 5
        // int length = scores.capacity(); -> 6
    }
}
`;

export const task189 = `
public class Student {

    public static void main(String[] args) {
        int[] gradesA = {98, 81, 90, 67, 41};
        int[] gradesB = {55, 67, 77, 81, 90};

        int searchA = Arrays.binarySearch(gradesA, 67);
        int searchB = Arrays.binarySearch(gradesB, 67);

        System.out.println("Position = " + searchA);
        System.out.println("Position = " + searchB);
    }
}
`;

// 190.png
export const task190 = `
public class Student {

    public static void main(String[] args) {
        int[] arr1 = {1, 2};
        int[] arr2 = {3, 4};
        char[] arr3 = {1, 2};

        arr1 = arr2;
        arr3 = arr1;
        for(int i = 0; i < arr2.length; i++) {
            System.out.print(arr2[i]);
        }
    }
}
`;

export const task192 = `
public class Student {

    public static void main(String[] args) {
	    int[] arr = {1, 2};
	    for(int i = 0; i < arr.length; i++) {
		    System.out.print(arr[i]);
		    continue;
		    return;
	    }
	}
}
`;

export const javaCode193 = `
public static void main(String[] args) {
    int[] arr = {1,2,3,4};
    for(int i=0; i<arr.length; i++) {
        System.out.print(arr[i]);
        if(arr[i] % 2 == 0) {
            continue;
        }
    }
}

`;

export const javaCode195 = `
public static void main(String[] args) {
    int myGrade = 70;
    final int bestGrade = 100;
    final int passingGrade = 100;  
    switch(myGrade) {
        default: System.out.print("Default");
        case 70: System.out.print("Average");
        case bestGrade:
            System.out.print("A");
            break;
        case passingGrade:
            System.out.print("Passed");
            break;
    }
}
`;

export const javaCode196 = `
public static void main(String[] args) {
    ArrayList arrayList = new ArrayList();
    int val = arrayList.length(); //1
    int val = arrayList.length; //2
    int val = arrayList.size(); //3
    int val = arrayList.size; //4
    int val = arrayList.capacity(); //5
    int val = arrayList.capacity; //6
}
`;

export const javaCode198 = `
public static void main(String[] args) {
    int[] a = {1,2};
    int[] b = {1,2};

    ArrayList arr1 = new ArrayList<>();
    ArrayList arr2 = new ArrayList<>();

    arr1.add(1); arr1.add(2);
    arr2.add(1); arr2.add(2);

    boolean b1 = (a == b);  
    boolean b2 = a.equals(b);       
    boolean b3 = (arr1 == arr2);
    boolean b4 = arr1.equals(arr2);

    System.out.println(b1+" / "+b2+" / "+b3+" / "+b4);
}
`;

export const javaCode199 = `
public static void main(String[] args) {
    ArrayList<Integer> arrl = new ArrayList<>();
    arrl.add(1);
    arrl.add(2);
    arrl.add("1");
        
    for(Integer val : arrl)  
        System.out.print(val);
}
`;

export const javaCode200 = `
public static void main(String[] args) {
    ArrayList<Integer> arrl = new ArrayList<>();
    arrl.add(1); arrl.add(2); arrl.add(3);
    arrl.remove(0);
    arrl.remove(1);
        
    for(Integer val : arrl) {
            System.out.print(val);
    }
}
`;


export const javaCode201 = `
public static void main(String[] args) {
    List<Integer> list1 = Arrays.asList(5, -7, 20, 1);
    Integer[] arr1 = new Integer[4];
    boolean bol1 = arr1 == list1;
    boolean bol2 = arr1.equals(list1);
    System.out.println(bol1 + " / " + bol2);
}
`;

export const javaCode202 = `
public static void main(String[] args) {
    List<Integer> list1 = Arrays.asList(0, 1, 2, 3);
    list1.set(1, 0);
    list1.set(0, 1);
    System.out.println(list1.get(0));
}
`;


export const javaCode203 = `
public static void main(String[] args) {
    List<Integer> list1 = Arrays.asList(7);
    List<Integer> list2 = new ArrayList<>();
    list2.set(0, 7);
    boolean b1 = list1 == list2;
    boolean b2 = list1.equals(list2);
    System.out.println(b1 + " / " + b2);
}
`;

export const javaCode204 = `
public static void main(String[] args) {
    List<Integer> list1 = new ArrayList<>();
    list1.add(1);
    list1.add(new Integer(2));
    list1.add(Integer.parseInt("3"));
    list1.add(Integer.valueOf(4));
    list1.add(null);
    for (int val : list1){
        System.out.print(val);
    } 
}
`;

export const javaCode205 = `
public static void main(String[] args) {
    List<Integer> list1 = new ArrayList<>();
    list1.add(1);
    list1.add(new Integer(2));
    list1.add(Integer.parseInt("3"));
    list1.add(Integer.valueOf(4));
    boolean bll = list1.contains(3);
    int indexVal = list1.indexOf(1);
    list1.clear();
    int size = list1.size();
    System.out.println(indexVal + " / " + size + " / " + bll);
}
`;

export const javaCode206 = `
public class Student{
    ______ void read(){}
}
`;

export const javaCode210 = `
public static int calculate(Integer a, Integer... b){ 
    return b.length;
}
`;

export const javaCode211 = `
public static void print(byte x){ 
    System.out.print("char"); 
}

public static void print(short x){ 
    System.out.print("short");
}

public static void print(int x){ 
    System.out.print("int");
}

public static void print(Object x){ 
    System.out.print("Object");
}

public static void main(String[] args) {
    char ch = 7;
    short sh = 7;
    print(ch);
    print(sh);
    print(new Integer(7));
}
`;

export const javaCode212 = `
public class Student {

    public void play() {
        System.out.print("Play");
    }

    public static void sleep() {
        System.out.print("Sleep");
    }

    public static void study() {
        System.out.print("Study");
    }

    public static void summer() {
        play();
        sleep();
    }

    public static void main(String[] args) {
        Student john = null;
        john.study();
        john.summer();
    }

}
`;


export const javaCode213 = `
public class Student {

    private static String name;
    private static final String lastName;
    private static final int age;
    private static final String subject;

    static {
        lastName = "Doc";
    }

    static {
        name = "John";
        age = 30;
    }

    static {
        lastName = "Wick";
    }

    public static void main(String[] args) {
        subject= "Art";
    }
}
`;

export const javaCode214 = `
public class Bank {

    public static long square(int debt) {
        long newDebt = debt * debt;
        debt = 0;
        return newDebt;
    }

    public static void main(String[] args) {
        int debt = 70;
        long newDebt = square(debt);
        System.out.println(debt);
    }
}
`;

export const javaCode215 = `
import java.time.LocalDate;

public class Watch {

    public static void main(String[] args) {  
        LocalDate myDate = ______  ;
    }
}
`;

export const javaCode216 = `
import java.time.*;

public class Watch {

    public static void main(String[] args) {
        LocalTime localTime = LocalTime.of(15, 25);
        localTime.plusHours(2);
        localTime.plusDays(5);
        System.out.println(localTime);
    }
}
`;

export const javaCode217 = `
import java.time.*;

public class Watch {

    public static void main(String[] args) {
        LocalDate localDate = LocalDate.of(2001,Month.JANUARY, 01);
        System.out.print(localDate.getDayOfMonth());
        System.out.print(" / " + localDate.getMonth());
        System.out.print(" / " + localDate.getYear());
    }
}
`;

export const javaCode218 = `
import java.time.*;

public class Watch {

    public static void main(String[] args) {
        LocalDate localDate = LocalDate.of(2015,Month.FEBRUARY, 32);
        System.out.print(localDate.getDayOfMonth());
        System.out.print(" / " + localDate.getMonth());
        System.out.print(" / " + localDate.getYear());
    }
}
`;

export const javaCode219 = `
import java.time.*;

public class Watch {
    public static void main(String[] args) {
        LocalDate localDate = localDate.of(2015,Month.FEBRUARY, 10);
        localDate = localDate.plusDays();
        localDate.plusYears(1);
        System.out.print(localDate.getDayOfMonth());
        System.out.print(" / " + localDate.getMonth());
        System.out.print(" / " + localDate.getYear());
    }
}
`;


export const javaCode220 = `
import java.time.*;
import java.time.format.*;

public class Watch {

    public static void main(String[] args) {
        LocalDateTime dateTime = LocalDateTime.of(2000,1,2,3,22,35);
        Period pl = Period.of(1,1,1);
        dateTime = dateTime.plus(pl);
        dateTime.minus(pl);
        DateTimeFormatter formatter = DateTimeFormatter.ofLocalizedDate(FormatStyle.LONG);
        System.out.println(dateTime.format(formatter));
    }
}
`;

// starting from here

export const javaCode222 = `
class Parent{
    public Parent(int x) {
        System.out.print("Parent int");
    }

    public Parent() {
        System.out.print("Parent");
    }
}

public class Child extends Parent {

    public void Child(int x) {
        System.out.print("Child int");
    }

    public Child(byte d) {
        System.out.print("Child byte");
    }

    public static void main(String[] args) {
        Parent ch = new Parent();
        Parent ch1 = new Child(67);
    }
}
`;

export const javaCode223 = `
public interface IGrow {}

public class Parent implements IGrow{
    public static void main(String[] args) {
    ______ baby = new Infant();
}

class Son extends Parent {}

class Daughter extends Parent {}

class Infant extends Parent {}
`;

export const javaCode224 = `
public interface IGrow {
    abstract int getAge();
}

abstract class Human implements IGrow {
    abstract int getWeight();
}

public class Parent extends Human {
    int getWeight() { return 80; }
}
`;

export const javaCode225 = `
public interface IGrow {

    int step = 1;

    static int getAge();

    public void incrementAge() {
        step++;
    }
}
`;

export const javaCode227 = `
class Parent extends Human{

    public void eat(int weight) {
        System.out.println("Parent is eating");
    }
}

public class Human{
    public void eat() {
        System.out.println("Human is eating");
    }

    public static void main(String[] args) {
        Human human = new Parent();
        human.eat();
    }
}

`;

export const javaCode228 = `
public abstract class Human{
    
    public abstract void sleep() {}

    public void eat() {
        System.out.println("Human is eating");
    }

    public static void main(String[] args) {
        Human human = new Parent();
        human.eat();
    }
}

class Parent extends Human{

    @Override
    public void eat() {
        System.out.println("Parent is eating");
    }

    @Override
    public void sleep() {
        System.out.println("Parent is sleeping");
    }
}
`;

export const javaCode229 = `
public abstract class Human{

    public final void sleep() {
        System.out.println("Human is sleeping");
    }

    public static void main(String[] args) {
        Human human = new Parent();
        human.sleep();
    }
}
class Parent extends Human{

    @Override
    public void sleep() {
        System.out.println("Parent is sleeping");
    }
}
`;

export const javaCode230 = `
public interface IGrow {
    public Double getAge(int estimate) {
        return new Double(30);
    }
}
// Consider separate files

public class Human implements IGrow{

    public Integer getAge(int estimate) {
        return 25;
    }
    public static void main(String[] args) {
        System.out.println(new Human().getAge(7));
    }
}
`;

export const javaCode231 = `
public class Human {

    public int age;

    public static void main(String[] args) {
        Human hm = new Human();
        hm.age = 19;
        check(hm, (Human h) -> h.age > 18);
    }


    private static void check(Human hm, Predicate<Human> pred) {
        String result = pred.test(hm) ? "adult" : "teenager";
        System.out.println(result);
    }

}
`;

export const javaCode232 = `
public interface Geometry {  
    boolean isSquare(int height, int width); // 1  
}

public class Shape {  

    public int width;  
    public int height; 

    public static void main(String[] args) {  
        Shape shape = new Shape();  
        shape.width = 12;  
        shape.height = 7;  
        check((int height, int width) -> {  
        return height.equals(width);  
        }, shape); //2  
    }

    private static void check(Geometry check, Shape shape) { // 3  
        if(check.isSquare(shape.height, shape.width)) { // 4  
            System.out.println("Square");  
        }else{  
            System.out.println("Rectangle");  
        } 
    }

}
`;

export const javaCode233 = `
public class Human {

    public int age;

    public static void main(String[] args) {
        Human hm = new Human();
        hm.age = 19;
        check(hm, ______);
    }
    private static void check(Human hm, Predicate<Human> pred) {
        String result = pred.test(hm) ? "adult" : "teenager";
        System.out.println(result);
    }
}
`;

export const javaCode234 = `
public class RandomClass {

    public static void main(String[] args) {
        List<Integer> numbers = new ArrayList<>(); 
        numbers.add(12); numbers.add(3); 
        numbers.add(7); numbers.add(5); 
        numbers.removeIf(n -> n%2==0); 
        System.out.println(numbers); 
    }

}
`;

export const javaCode236 = `
public void randomMethod() ______ Exception {  // 1
    ______ Exception();  // 2
}
`;

export const javaCode237 = `
public static void randomMethod() {  
    int x = 0;  
    int[] arr = new int[1];  
    int val = arr[0] / x * arr[2];  
    System.out.println(val); 
}
`;

export const javaCode238 = `
public static void randomMethod() {  
    int x = 0;  
    int[] arr = new int[1];  
    int val = arr[2] / 0 ;  
    System.out.println(val); 
}
`;

export const javaCode239 = `
public class Car {
    public static void main(String[] args) { 
        go(); 
    }

    public static void go() { 
        danger(); 
        System.out.print("B");
    }

    public static void danger() { 
        System.out.print("A"); 
        try{
            int x = 0; 
            int[] arr = new int[1]; 
            int val = arr[2] / 0 ;
            System.out.print("C"); 
        }catch(ArithmeticException e) { 
            System.out.print("D"); 
        }catch(ArrayIndexOutOfBoundsException e){ 
            System.out.print("E"); 
        }finally { 
            System.out.print("F"); 
        } 
        System.out.print("G");
    }
}
`;

export const javaCode240 = `
public static int danger(int a, int b) {
    try{
        int val = (a / b);
        return 1;
    } catch(ArithmeticException e) {
        return -1;
    }finally {
        return 0;
    }
        return 2;
}
`;

export const javaCode241 = `
public static int danger(int a, int b) {
    try{
        int val = (a / b);
        return 1;
    } catch(ArithmeticException e) {
        return -1;
    }finally {
        return 0;
    }
}
`;

export const javaCode242 = `
public static int danger(int a, int b) {
    try{
        int val = (a / b);
        return 1;
    }
    catch(Exception e) {
        return -1;
    }
    catch(ArithmeticException e) {
        return -2;
    }
    finally {
        return 0;
    }
}
`;

export const javaCode243 = `
public static void main(String[] args) {    
    danger(0,0); 
} 
public static void danger(int a, int b) {    
    System.out.print("A"); 
    try{    
        int val = (a / b);    
        System.out.print("B");    
        System.exit(0); 
    }catch(ArithmeticException e) {    
        System.out.print("D");    
        System.exit(0); 
    }catch(ArrayIndexOutOfBoundsException e){    
        System.out.print("E");    
        System.exit(0); 
    }finally {    
        System.out.print("C"); 
    } 
    System.out.print("G"); 
}
`;