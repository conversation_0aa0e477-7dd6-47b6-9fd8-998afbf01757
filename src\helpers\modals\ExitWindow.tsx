import React from "react";
import {View, Text, StyleSheet, BackHandler} from "react-native";
import { Button} from "@react-native-material/core";
import {Entypo, MaterialCommunityIcons} from "@expo/vector-icons";
import { useVideoPlayer, VideoView } from 'expo-video';


interface CustomModalWindowProps {
    closeModal: () => void;
}

const ExitWindow: React.FC<CustomModalWindowProps> = (props) => {
    // Video player hook
    const player = useVideoPlayer(require('../../media/bug.mp4'), player => {
        player.loop = true;
        player.muted = true;
        player.play();
    });

    return (
        <View style={styles.container}>
            <VideoView
                style={styles.imgBackground}
                player={player}
            />
            <View style={styles.form}>
                <View style={styles.header}>
                    <View style={styles.column}>
                        <Text style={styles.labelHeader}>Exit</Text>
                        <Text style={styles.labelMyNumber}>
                           Are you sure you want to exit?
                        </Text>

                    </View>
                </View>
                <View style={styles.buttonContainer}>
                    <Button
                        title="Exit"
                        style={styles.buttonStyle}
                        titleStyle={styles.buttonText}
                        onPress={() =>  {
                            props.closeModal();
                            BackHandler.exitApp();
                        }}
                        trailing={(props) => <Entypo name="paper-plane" {...props}/>}/>

                    <Button
                        title="Close"
                        style={styles.buttonStyle}
                        titleStyle={styles.buttonText}
                        onPress={() => props.closeModal()}
                        trailing={(props) => (<MaterialCommunityIcons name="close-box" {...props}/>)}/>
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: '100%',
        backgroundColor: 'black',
        justifyContent: 'center',
        alignItems: 'center'
    },
    imgBackground: {
        height: '100%',
        width: '100%',
        position: 'absolute'
    },
    buttonStyle: {
        backgroundColor: "#215e9d",
        fontFamily: "monospace"
    },
    form: {
        width: "100%",
        marginHorizontal: 15,
        paddingVertical: "8%",
        //backgroundColor: "white",
        justifyContent: "center",
        alignItems: "center"
    },
    header: {
        flexDirection: "row",
        justifyContent: "space-around"
    },
    column: {
        flexDirection: "column",
        width: "80%"
    },
    labelMyNumber: {
        fontFamily: "LatoRegular",
        fontSize: 18,
        textAlign: "center",
        color:'white'
    },
    labelHeader: {
        fontFamily: "AmorriaBrush",
        fontSize: 22,
        textAlign: "center",
        color:'white'
    },
    buttonContainer: {
        paddingTop: "7%",
        width: "70%",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-around"
    },
    buttonText: {
        fontSize: 14,
        fontFamily:'LatoRegular'
    },
});

export default ExitWindow;
