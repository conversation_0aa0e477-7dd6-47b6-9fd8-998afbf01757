import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';

import {AdEventType, InterstitialAd, TestIds} from 'react-native-google-mobile-ads';
import BouncyCheckbox from 'react-native-bouncy-checkbox';
import { useVideoPlayer, VideoView } from 'expo-video';
import { InitExamService } from '../../service/InitExamService';
import { QuestionContentService } from '../../service/QuestionContentService';

interface CustomModalWindowProps {
  closeModal?: () => void;
  callUnmountComponent: () => void;
  title:string;
  examId:number;
  navigation: any;
}

interface Picker{
  label:string;
  value:string;
}

const values:Picker[] =[
  {label: '10', value: '10'},
  {label: '20', value: '20'},
  {label: '35', value: '35'},
  {label: '50', value: '50'},
  {label: '65', value: '65'}
]


const interstUNIT_ID = __DEV__ ? TestIds.INTERSTITIAL : 'ca-app-pub-5981144475529351/4273715172';

const EntryExam: React.FC<CustomModalWindowProps> = (props) => {
  // State hooks
  const [newExam, setNewExam] = useState<boolean>(true);
  const [oldExamId, setOldExamId] = useState<number>(0);
  const [amountRandom, setAmountRandom] = useState<string>('10');
  const [oldExamTitle, setOldExamTitle] = useState<string>('');

  // Refs for interstitial ad
  const interstitialRef = useRef<InterstitialAd | null>(null);

  // Video player hook
  const player = useVideoPlayer(require('../../media/entry.mp4'), player => {
    player.loop = true;
    player.muted = true;
    player.play();
  });

  // Initialize interstitial ad
  useEffect(() => {
    interstitialRef.current = InterstitialAd.createForAdRequest(interstUNIT_ID, {
      requestNonPersonalizedAdsOnly: false,
    });

    loadInterAd();
  }, []);

  // Check for ongoing exam on mount
  useEffect(() => {
    const checkOngoingExam = async () => {
      try {
        const ongoingExam = await InitExamService.getOngoingExam();
        if (ongoingExam) {
          setNewExam(false);
          setOldExamId(ongoingExam.id);
          setOldExamTitle(ongoingExam.name);
        }
      } catch (error) {
        console.log('Error checking ongoing exam:', error);
      }
    };

    checkOngoingExam();
  }, []);

  const loadInterAd = () => {
    const interstitial = interstitialRef.current;
    if (!interstitial) return;

    try {
      interstitial.addAdEventListener(AdEventType.LOADED, () => {
        console.log('Interstitial ad loaded');
      });

      interstitial.addAdEventListener(AdEventType.ERROR, (error) => {
        // console.log('Interstitial ad failed to load:', error);
        interstitial.load();
      });

      interstitial.addAdEventListener(AdEventType.CLOSED, () => {
        interstitial.load();
      });

      interstitial.load();
    } catch (error) {
      console.log('Failed to create interstitial ad:', error);
    }
  };

  const startExam = async () => {
    props.closeModal?.();
    const interstitial = interstitialRef.current;
    const { examId } = props;

    await InitExamService.markAsCompleted(oldExamId);
    await InitExamService.markAsOngoing(examId);
    QuestionContentService.generateAnswerSheet(examId, Number.parseInt(amountRandom));

    props.navigation.navigate('Question', {id: 0});
    if(interstitial?.loaded){
      interstitial.show();
    }
  };

  const loadExam = () => {
    props.closeModal?.();
    props.navigation.navigate('ListQuestions');
  };

  const closeModal = () => {
    props.closeModal?.();
  };

  const renderCheckboxList = () => {
    return (
      <View style={styles.checkboxContainer}>
        {values.map((item) => (
          <BouncyCheckbox
            key={item.value}
            size={25}
            fillColor="#215e9d"
            unFillColor="#FFFFFF"
            text={`${item.label} questions`}
            iconStyle={{ borderColor: "#215e9d" }}
            innerIconStyle={{ borderWidth: 2 }}
            textStyle={{
              fontFamily: "LatoRegular",
              color: "white",
              textDecorationLine: "none",
            }}
            isChecked={amountRandom === item.value}
            onPress={(isChecked: boolean) => {
              if (isChecked) {
                setAmountRandom(item.value);
              }
            }}
          />
        ))}
      </View>
    );
  };

  const renderOld = () => {
    const { title, examId } = props;
    return (
      <>
        <Text style={styles.textStyles}>
          You have unfinished <Text style={styles.exam}>{oldExamTitle}</Text> exam. Do you wish to cancel it and proceed to  <Text style={styles.exam}>{title}</Text> exam?
          </Text>
          {
            examId == 100
            ?
            <>
            <Text style={styles.textStyles}>
              If you wish to continue please select amount of questions:
            </Text>

            {renderCheckboxList()}
            </>
            :
            null
          }
          <View style={styles.buttonContainer3}>
              <TouchableOpacity style={styles.button} onPress={() => loadExam()}>
                  <Text style={styles.buttonText}>Continue</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.button} onPress={() => startExam()}>
                  <Text style={styles.buttonText}>New</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.button} onPress={() => closeModal()}>
                  <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
          </View>
        </>
       );
  };

  const renderNew = () => {
    const { title, examId } = props;
    if(examId !== 100){
      return (
        <>
          <Text style={styles.textStyles}>
            You are going to enter <Text style={styles.exam}>{title}</Text> exam. You will not be able to enter another exam before ending or discarding this current exam.
            Do you wish to continue?
            </Text>
            <View style={styles.buttonContainer}>
                <TouchableOpacity style={styles.button} onPress={() => startExam()}>
                    <Text style={styles.buttonText}>Proceed</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.button} onPress={() => closeModal()}>
                    <Text style={styles.buttonText}>Cancel</Text>
                </TouchableOpacity>
            </View>
          </>
         );
    }

    return (
      <>
          <Text style={styles.textStyles}>
            <Text style={styles.exam}>{title}</Text> exam. You will not be able to enter another exam before ending or discarding this current exam.
          </Text>
          <Text style={styles.textStyles}>
            If you wish to continue please select amount of questions:
          </Text>

          {renderCheckboxList()}

          <View style={styles.buttonContainer}>
              <TouchableOpacity style={styles.button} onPress={() => startExam()}>
                  <Text style={styles.buttonText}>Proceed</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.button} onPress={() => closeModal()}>
                  <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
          </View>
        </>
       );

  };

  return (
    <View style={styles.container}>
      <VideoView
        style={styles.imgBackground}
        player={player}
      />
      <View style={styles.form}>
        {
        newExam
        ?
        renderNew()
        :
        renderOld()
        }
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor:'black',
    justifyContent: 'center', 
    alignItems: 'center',
  },
  imgBackground:{
    height:'100%',
    width:'100%',
    position:'absolute',
    
  },
  form: {
    width: '100%',
    marginHorizontal:15,
    padding:'8%',
    //backgroundColor:'white',
    justifyContent: 'center', 
    alignItems: 'center',
  },
  textStyles:{
    fontFamily:'LatoRegular',
    textAlign:'center',
    color:'white',
    fontSize:17,
  },
  exam:{
    fontFamily:"AmorriaBrush"
  },
  checkboxContainer: {
    marginVertical: 15,
    width: '100%',
    alignItems: 'flex-start',
  },
  buttonContainer:{
    flexDirection: 'row',
    alignItems:'center',
    justifyContent: 'space-around',
  },
  buttonContainer3:{
    flexDirection: 'row',
    alignItems:'center',
    justifyContent: 'space-between',
  },
  button: {
    marginTop: 20,
    marginLeft:10,
    marginRight:10,
    backgroundColor: '#215e9d',
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  buttonText: {
    color: '#fff',
    fontSize: 15,
    fontFamily:'LatoRegular'
  },
});

export default EntryExam;