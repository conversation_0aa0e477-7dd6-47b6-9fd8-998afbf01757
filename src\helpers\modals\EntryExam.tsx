import React, { Component } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { useVideoPlayer, VideoView, VideoPlayer } from 'expo-video';
import {AdEventType, InterstitialAd, TestIds} from 'react-native-google-mobile-ads';
import DropDownPicker from 'react-native-dropdown-picker';
import { Video, ResizeMode } from 'expo-av';
import { InitExamService } from '../../service/InitExamService';
import { QuestionContentService } from '../../service/QuestionContentService';

interface HelperState {
  newExam:boolean;
  oldExamId: number;
  amountRandom:string;
  open:boolean;
  oldExamTitle: string;
}

interface CustomModalWindowProps {
  closeModal?: () => void;
  callUnmountComponent: () => void;
  title:string;
  examId:number;
  navigation: any;
}

interface Picker{
  label:string;
  value:string;
}

const values:Picker[] =[
  {label: '10', value: '10'},
  {label: '20', value: '20'},
  {label: '35', value: '35'},
  {label: '50', value: '50'},
  {label: '65', value: '65'}
]


const interstUNIT_ID = __DEV__ ? TestIds.INTERSTITIAL : 'ca-app-pub-5981144475529351/**********';

class EntryExam extends Component<CustomModalWindowProps, HelperState> {
  private interstitial: InterstitialAd;
  private player: VideoPlayer | null = null;
  constructor(props) {
    super(props);
    this.state = {
      newExam:true,
      open:false,
      oldExamId: 0,
      oldExamTitle:'',
      amountRandom:'10',
    };
    this.interstitial = InterstitialAd.createForAdRequest(interstUNIT_ID, {
      requestNonPersonalizedAdsOnly: false,
    });
  }

  componentDidMount(): void {
      var ongoingExam = InitExamService.getOngoingExam();
      ongoingExam.then((onGoing) => {
        if(onGoing){
            this.setState({newExam:false, oldExamId:onGoing.id, oldExamTitle: onGoing.name});
        }
      });
      this.loadInterAd = this.loadInterAd.bind(this);
      this.loadInterAd();
      this.player = useVideoPlayer('../../media/entry.mp4', (playerInstance) => {
        playerInstance.loop = true;
        playerInstance.muted= true;
        playerInstance.play();
      });
  }

    componentWillUnmount() {
    // Clean up listener
      if (this.player) {      
        this.player = null;
      }
    }

  loadInterAd() {
    const {interstitial} = this;
  
    try {      
      interstitial.addAdEventListener( AdEventType.LOADED, () => {
          console.log('Intertitial ad loaded');
        });
  
        interstitial.addAdEventListener(AdEventType.ERROR,(error) => {
         // console.log('Intertitial ad failed to load:', error);
          interstitial.load();
        });
  
        
  
        interstitial.addAdEventListener(AdEventType.CLOSED, () => {
          interstitial.load();
        });
        interstitial.load();
        
      }catch (error) {
        console.log('Intertitial to create rewarded ad:', error);
      }
      
  }

  startExam = async () => {
    this.props.closeModal();
    const {interstitial} = this;
    const {examId}   = this.props;
    
    const {oldExamId} = this.state;
    await InitExamService.markAsCompleted(oldExamId);
    await InitExamService.markAsOngoing(examId);
    QuestionContentService.generateAnswerSheet(examId, Number.parseInt(this.state.amountRandom));    

    this.props.navigation.navigate('Question', {id: 0})
    if(interstitial.loaded){
      interstitial.show();
    }
  }

  loadExam = () => {
    this.props.closeModal();
    this.props.navigation.navigate('ListQuestions');
  }


  closeModal = () =>{
    this.props.closeModal(); 
  }


  setOpen(open) {
    this.setState({
      open
    });
  }

  renderOld = () => {
    const {title, examId} = this.props;
    const {oldExamTitle} = this.state;
    const {amountRandom,open } = this.state;
    return (           
      <>
        <Text style={styles.textStyles}>
          You have unfinished <Text style={styles.exam}>{oldExamTitle}</Text> exam. Do you wish to cancel it and proceed to  <Text style={styles.exam}>{title}</Text> exam?
          </Text>
          {
            examId == 100
            ?
            <>
            <Text style={styles.textStyles}>
              If you wish to continue please enter amount of questions:
            </Text>
          
            <DropDownPicker
            open={open}
            style={styles.dropDown}
            value={amountRandom}
            items={values}
            setOpen={() => this.setState({open:!open})}
            setValue={(callback)=>  this.setState({amountRandom:callback(this.state.amountRandom)})}
            />
            </>
            :
            null
          }
          <View style={styles.buttonContainer3}>
              <TouchableOpacity style={styles.button} onPress={() => this.loadExam()}>
                  <Text style={styles.buttonText}>Continue</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.button} onPress={() => this.startExam()}>
                  <Text style={styles.buttonText}>New</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.button} onPress={() => this.closeModal()}>
                  <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
          </View>
        </>
       )
  }

  renderNew = () => {
    const {title, examId} = this.props;
    const {amountRandom,open } = this.state;
    if(examId!==100){
      return (           
        <>
          <Text style={styles.textStyles}>
            You are going to enter <Text style={styles.exam}>{title}</Text> exam. You will not be able to enter another exam before ending or discarding this current exam.
            Do you wish to continue?
            </Text>
            <View style={styles.buttonContainer}>
                <TouchableOpacity style={styles.button} onPress={() => this.startExam()}>
                    <Text style={styles.buttonText}>Proceed</Text>
                </TouchableOpacity>
  
                <TouchableOpacity style={styles.button} onPress={() => this.closeModal()}>
                    <Text style={styles.buttonText}>Cancel</Text>
                </TouchableOpacity>
            </View>
          </>
         )
    }



    return (           
      <>
          <Text style={styles.textStyles}>
            <Text style={styles.exam}>{title}</Text> exam. You will not be able to enter another exam before ending or discarding this current exam.
          </Text>
          <Text style={styles.textStyles}>
            If you wish to continue please enter amount of questions:
          </Text>
          
          <DropDownPicker
            open={open}
            style={styles.dropDown}
            value={amountRandom}
            items={values}
            setOpen={() => this.setState({open:!open})}
            setValue={(callback)=>  this.setState({amountRandom:callback(this.state.amountRandom)})}
          />
          
          <View style={styles.buttonContainer}>
              <TouchableOpacity style={styles.button} onPress={() => this.startExam()}>
                  <Text style={styles.buttonText}>Proceed</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.button} onPress={() => this.closeModal()}>
                  <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
          </View>
        </>
       )

  }

  render() {
    const {newExam} = this.state;
    return (
      <View style={styles.container}>
          {/* <Video
            source={require('../../media/entry.mp4')}
            style={styles.imgBackground}        
            isMuted={true}
            isLooping={true}
            shouldPlay={true}
            resizeMode={ResizeMode.COVER}
            rate={1.0}
          /> */}
          <VideoView 
            style={styles.imgBackground}
            player={
              useVideoPlayer(require('../../media/entry.mp4'), player => {

                player.play();
              })
            }
          />
        <View style={styles.form}>
          {
          newExam
          ?
          this.renderNew()
          :
          this.renderOld()
          }
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor:'black',
    justifyContent: 'center', 
    alignItems: 'center',
  },
  imgBackground:{
    height:'100%',
    width:'100%',
    position:'absolute',
    
  },
  form: {
    width: '100%',
    marginHorizontal:15,
    padding:'8%',
    //backgroundColor:'white',
    justifyContent: 'center', 
    alignItems: 'center',
  },
  textStyles:{
    fontFamily:'LatoRegular',
    textAlign:'center',
    color:'white',
    fontSize:17,
  },
  exam:{
    fontFamily:"AmorriaBrush"
  },
  dropDown: {
    marginVertical: 10,
  },
  buttonContainer:{
    flexDirection: 'row',
    alignItems:'center',
    justifyContent: 'space-around',
  },
  buttonContainer3:{
    flexDirection: 'row',
    alignItems:'center',
    justifyContent: 'space-between',
  },
  button: {
    marginTop: 20,
    marginLeft:10,
    marginRight:10,
    backgroundColor: '#215e9d',
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  buttonText: {
    color: '#fff',
    fontSize: 15,
    fontFamily:'LatoRegular'
  },
});

export default EntryExam;