import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { task1000, task1001, task1002, task1004, task1007, task1008, task1010, task1012, task1014 } from "./snippets/code";

export const basicsDB : Questions[] = [
    {
        id: 1000,
        category: 1,
        isImage: false,
        title: "What is the correct print order?",
        image: task1000,
        answers: [
            {
                id: 1,
                content: '<PERSON> / Jack / Ben / Alex / ',
                correct: false
            }, {
                id: 2,
                content: 'John / Jack / Alex / Ben / ',
                correct: false
            }, {
                id: 3,
                content: '<PERSON> / Ben / Jack / Alex / ',
                correct: true
            }, {
                id: 4,
                content: '<PERSON> / <PERSON> / <PERSON> / Jack / ',
                correct: false
            }
        ],
        explanation: 'There is an execution order of the code: \n \nstatic variables of the class -> n' +
                'on static variables -> constructors and methods. \n \nResult is => <PERSON> / <PERSON> / ' +
                'Jack / <PERSON> /'
    }, {
        id: 1001,
        category: 1,
        isImage: false,
        title: "What imports are missing?",
        image: task1001,

        answers: [
            {
                id: 1,
                content: 'import static java.lang.Integer.*;',
                correct: true
            }, {
                id: 2,
                content: 'static import java.lang.Integer.*;',
                correct: false
            }, {
                id: 3,
                content: 'import static java.Math.PI;',
                correct: false
            }, {
                id: 4,
                content: 'import static java.lang.*;',
                correct: false
            }, {
                id: 5,
                content: 'import static java.lang.System.*;',
                correct: true
            }, {
                id: 6,
                content: 'import static java.lang.Math.*;',
                correct: true
            }
        ],
        explanation: 'Importing a package always starts from keyword import, regardless of static or n' +
                'ot.\nOne more thing that static imports cannot be applied to entire package, it ' +
                'will cause compilation error. We need to specify class as well.\nIn this snippet' +
                ' we have static members of Integer -> MAX_VALUE, Math -> PI and System -> out.\n' +
                'Therefore we have 3 answers.'
    }, {
        id: 1002,
        category: 1,
        isImage: false,
        title: "What combinations are allowed?",
        image: task1002,
        answers: [
            {
                id: 1,
                content: 'static grade variable can occur with getGrade()',
                correct: true
            }, {
                id: 2,
                content: 'static grade variable can occur with static getGrade()',
                correct: true
            }, {
                id: 3,
                content: 'grade variable can occur with getGrade()',
                correct: true
            }, {
                id: 4,
                content: 'grade variable can occur with static getGrade()',
                correct: false
            }, {
                id: 5,
                content: 'getGrade() method can occur with static getGrade()',
                correct: false
            }, {
                id: 6,
                content: 'grade variable can occur with static grade variable',
                correct: false
            }
        ],
        explanation: 'Member variables cannot have the same name. Therefore grade naming of variable c' +
                'an only be used once. \n \nBoth static and non static method can return static m' +
                'ember variable, but non static variable can only be used in non static method.\n' +
                ' \nTwo methods can have the same name in a single class only if they are overloa' +
                'ding each other.'
    }, {
        id: 1003,
        category: 1,
        isImage: false,
        title: 'What is encapsulation?',
        answers: [
            {
                id: 1,
                content: 'Encapsulation is a mechanism in Java that allows you to create multiple instance' +
                        's of a class.',
                correct: false
            }, {
                id: 2,
                content: 'Encapsulation is a way to hide the internal implementation details of an object ' +
                        'and restrict direct access to its state.',
                correct: true
            }, {
                id: 3,
                content: 'Encapsulation refers to the process of converting primitive data types into obje' +
                        'cts.',
                correct: false
            }, {
                id: 4,
                content: 'Encapsulation is a technique in Java used for inheriting properties and behavior' +
                        's from one class to another.',
                correct: false
            }
        ],
        explanation: 'Encapsulation is a fundamental concept in object-oriented programming, including' +
                ' Java.\nIt involves bundling data (attributes) and methods (functions) that oper' +
                'ate on the data into a single unit known as a class. By doing so, the internal d' +
                'etails of how the data is stored and manipulated are hidden from the outside wor' +
                'ld, promoting data integrity and security.\nAccess to the internal state of an o' +
                'bject is controlled through getter and setter methods, allowing for controlled m' +
                'odification and retrieval of the objects attributes.'
    }, {
        id: 1004,
        category: 1,
        isImage: false,
        title: "What imports in Student class are missing?",
        image: task1004,
        answers: [
            {
                id: 1,
                content: 'import static app.exam.Exam.duration;',
                correct: true
            }, {
                id: 2,
                content: 'import app.exam.Exam.duration;',
                correct: false
            }, {
                id: 3,
                content: 'import app.exam.Exam;',
                correct: true
            }, {
                id: 4,
                content: 'import static app.exam.Exam;',
                correct: false
            }
        ],
        explanation: 'Student class requires of importing both - static  duration variable and Exam cl' +
                'ass itself.\nStatic imports requires static keyword after import statement. Afte' +
                'rwards we just need to import only Exam class in a regular way.'
    }, {
        id: 1005,
        category: 1,
        isImage: false,
        title: 'Which of the following are valid declarations of the standard main method?',
        answers: [
            {
                id: 1,
                content: 'static void main(String args[ ]) { }',
                correct: false
            }, {
                id: 2,
                content: 'public static void main (String[ ] args) { }',
                correct: true
            }, {
                id: 3,
                content: 'final public static void main (String args) { }',
                correct: false
            }, {
                id: 4,
                content: 'final public static void main (String[] args) { }',
                correct: true
            }
        ],
        explanation: 'A correct declaration of the "main()" method must be either public static void m' +
                'ain(String[] args) or static public void main(String[] args). The placement orde' +
                'r of the static and public keywords is interchangeable.\n \nThe usage of the fin' +
                'al keyword with the method does not alter the method signature.'
    }, {
        id: 1006,
        category: 1,
        isImage: false,
        title: 'What is polymorphism?',
        answers: [
            {
                id: 1,
                content: 'Polymorphism is the process of converting a primitive data type into an object i' +
                        'n Java.',
                correct: false
            }, {
                id: 2,
                content: 'Polymorphism is a way to hide the internal implementation details of an object a' +
                        'nd restrict direct access to its state.',
                correct: false
            }, {
                id: 3,
                content: 'Polymorphism is a mechanism that allows a class to have multiple constructors.',
                correct: false
            }, {
                id: 4,
                content: 'Polymorphism is the ability of different classes to be treated as instances of t' +
                        'he same class through a common interface.',
                correct: true
            }
        ],
        explanation: 'Polymorphism is a core concept in object-oriented programming that allows object' +
                's of different classes to be treated as instances of a common superclass or inte' +
                'rface.\nThis enables code to be more flexible and generic, as you can write code' +
                ' that works with a common interface and then use various implementations of that' +
                ' interface without needing to modify the code. It promotes reusability and exten' +
                'sibility in software design.'
    }, {
        id: 1007,
        category: 1,
        isImage: false,
        title: "What changes are necessary for code to run, considering that both of them are in the same file?",
        image: task1007,

        answers: [
            {
                id: 1,
                content: 'Code is fine',
                correct: false
            }, {
                id: 2,
                content: 'Change int to void in main method',
                correct: true
            }, {
                id: 3,
                content: 'Change String to String[] in main method',
                correct: true
            }, {
                id: 4,
                content: 'Change public into final in main method',
                correct: false
            }, {
                id: 5,
                content: 'Delete public from Exam class',
                correct: true
            }, {
                id: 6,
                content: 'Remove static from main method',
                correct: false
            }
        ],
        explanation: 'A correct declaration of the "main()" method must be either public static void m' +
                'ain(String[] args) or static public void main(String[] args). The placement orde' +
                'r of the static and public keywords is interchangeable. The usage of the final k' +
                'eyword with the method does not alter the method signature.\n \nEach java file c' +
                'an have as many default classes as it required, but only 1 can be a public class' +
                '.'
    }, {
        id: 1008,
        category: 1,
        isImage: false,
        title: "What will be printed if the program is run using the command line: java Exam Mat" +
                "h Art History",
        image: task1008,

        answers: [
            {
                id: 1,
                content: 'Math',
                correct: false
            }, {
                id: 2,
                content: 'Art',
                correct: true
            }, {
                id: 3,
                content: 'History',
                correct: false
            }, {
                id: 4,
                content: 'MathArtHistory',
                correct: false
            }, {
                id: 5,
                content: 'Compilation error',
                correct: false
            }
        ],
        explanation: '3 parameters are passed to the code snippet ->  Math Art History.\n \nSo the str' +
                'ing is splitted using blank spaces and parsed into array -> [[0] - Math, [1] - A' +
                'rt, [2] - History], so exam2 -> Art'
    }, {
        id: 1009,
        category: 1,
        isImage: false,
        title: 'Which statements is/are true?',
        answers: [
            {
                id: 1,
                content: 'Every public class must have a main method so that it can be tested individually' +
                        ' from the command line.',
                correct: false
            }, {
                id: 2,
                content: 'Each class must implement 1 interface',
                correct: false
            }, {
                id: 3,
                content: 'Every class belongs to a package',
                correct: true
            }, {
                id: 4,
                content: 'Java allows class inheritance',
                correct: true
            }
        ],
        explanation: 'Line 1 -> While it is common to include a main method in a class for standalone ' +
                'testing, it is not a strict requirement for every public class in Java. Public c' +
                'lasses can exist without a main method, and the presence of a main method is not' +
                ' a defining characteristic of a public class.\n \nLine 2 -> Java allows classes ' +
                'to implement multiple interfaces, not just one. This feature supports the concep' +
                't of multiple inheritance through interfaces, where a class can provide implemen' +
                'tations for multiple contracts defined by different interfaces. However it can a' +
                'lso be possible that class doesnot have any implemented interfaces\n \nLine 3 ->' +
                ' In Java, all classes are organized into packages. This helps in organizing and ' +
                'structuring code by grouping related classes together. The use of packages enhan' +
                'ces code modularity and readability.\n \nLine 4 -> Java supports class inheritan' +
                'ce, which is a fundamental concept in object-oriented programming. A class can i' +
                'nherit attributes and behaviors from another class, promoting code reuse and ext' +
                'ensibility. \n \nSo the answers 3, 4'
    }, {
        id: 1010,
        category: 1,
        isImage: false,
        title: "What line causes compilation errors?",
        image: task1010,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '2',
                correct: true
            }, {
                id: 3,
                content: '3',
                correct: false
            }, {
                id: 4,
                content: '4',
                correct: false
            }, {
                id: 5,
                content: '5',
                correct: false
            }, {
                id: 6,
                content: '6',
                correct: true
            }
        ],
        explanation: 'The main method should have a return type of void, not double. The correct signa' +
                'ture of the main method in Java is public static void main(String[] args).\nHowe' +
                'ver this cannot be the cause of compilation error, therefore we are dropping it.' +
                ' \n \nYou cannot declare an instance variable (grade) within a method. Instance ' +
                'variables should be declared at the class level, outside of any methods, it give' +
                's compilation error.\n \nThe System.out.println(7) statement is correct syntacti' +
                'cally, and it prints the integer 7.\n \nThe for loop is an infinite loop because' +
                ' the loop condition true will never become false. This might not be a compilatio' +
                'n error, but it could lead to runtime issues and exception. It is a bad practice' +
                ' to use it, but we donot have any compilation errors here.\n \nThe return statem' +
                'ent is trying to return a double value (0.0) from a method that is declared to h' +
                'ave a double return type. It has no compilation errors. Java file cannot have 2 ' +
                'public classes in 1 package, therefore  line 6 is causing compilation error.\n ' +
                '\nAnswers 2 , 6'
    }, {
        id: 1011,
        category: 1,
        isImage: false,
        title: 'What command compiles java file?',
        answers: [
            {
                id: 1,
                content: 'java MyFile',
                correct: false
            }, {
                id: 2,
                content: 'java MyFile.java',
                correct: false
            }, {
                id: 3,
                content: 'javac MyFile',
                correct: false
            }, {
                id: 4,
                content: 'javac MyFile.java',
                correct: true
            }
        ],
        explanation: 'Use the javac command followed by the name of your Java file to compile it.\n \nAnsw' +
                'er is: javac MyFile.java is the correct answer.'
    }, {
        id: 1012,
        category: 1,
        isImage: false,
        title: "What will be printed in console?",
        image: task1012,
        answers: [
            {
                id: 1,
                content: 'java Student 1 2 3 -> 3 arguments',
                correct: true
            }, {
                id: 2,
                content: 'java Student 1 2 3 -> 1 arguments',
                correct: false
            }, {
                id: 3,
                content: 'javac Student 1 2 3 -> 1 arguments',
                correct: false
            }, {
                id: 4,
                content: 'java Student -> null arguments',
                correct: false
            }, {
                id: 5,
                content: 'java Student -> 0 arguments',
                correct: true
            }, {
                id: 6,
                content: 'java Student -> Exception is thrown',
                correct: false
            }
        ],
        explanation: 'When the program is called with n arguments, those n arguments will be converted' +
                ' into array of size n.\n \nUsing this rule if the program is called with no argument' +
                's, the args array will be of length zero, basically it is an empty array.'
    }, {
        id: 1013,
        category: 1,
        isImage: false,
        title: 'What is weak encapsulation?',
        answers: [
            {
                id: 1,
                content: 'It is a concept where the internal state of an object is well-protected, but the' +
                        ' methods to access and modify that state are made public.',
                correct: false
            }, {
                id: 2,
                content: 'It refers to the practice of not using classes to organize code and instead plac' +
                        'ing all code in a single file.',
                correct: false
            }, {
                id: 3,
                content: 'It is an approach in which partially  attributes of an object are marked as public or default, bu' +
                        't not private.',
                correct: true
            }, {
                id: 4,
                content: 'It  is a term used to describe the process of bundling data and methods within a' +
                        ' class in object-oriented programming.',
                correct: false
            }
        ],
        explanation: 'Weak encapsulation, sometimes referred to as "soft encapsulation" involves hidin' +
                'g the internal state of an object by marking attributes as private.\nHowever, in ' +
                'this approach, getter and setter methods are not utilized to control access to t' +
                'hese attributes, essentially allowing unrestricted access to the internal state.' +
                ' This weakens the control and protection of the objects data -> C.'
    }, {
        id: 1014,
        category: 1,
        isImage: false,
        title: "Update the code in a way that it fits into encapsulation mechanism.",
        image: task1014,
        answers: [
            {
                id: 1,
                content: 'Mark name and grade variables as private',
                correct: true
            }, {
                id: 2,
                content: 'Mark name and grade variables as default(remove public statement)',
                correct: false
            }, {
                id: 3,
                content: 'Add public String getName() and public int getGrade() methods',
                correct: true
            }, {
                id: 4,
                content: 'Remove setName() and setGrade() methods',
                correct: false
            }, {
                id: 5,
                content: 'Mark setName() and setGrade() methods as private',
                correct: false
            }
        ],
        explanation: 'The attributes name and grade are marked as private, meaning they can only be ac' +
                'cessed and modified within the class itself. This prevents direct external manip' +
                'ulation and enforces controlled access.\n \nClass should have Getter and Setter meth' +
                'ods : Getter methods like getName() and getGrade() provide read-only access to t' +
                'he attributes. These methods allow controlled retrieval of the attribute values ' +
                'without exposing the internal representation.\nSetter Methods: Setter methods lik' +
                'e setName() and setGrade() allow controlled modification of the attributes. Thos' +
                'e methods should have either public or any other modifier, based on the logic.'
    }
]