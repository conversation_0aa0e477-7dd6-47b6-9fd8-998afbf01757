export const task1 = `
package app;

public class Question {
    public static void main(String[] args) {
        int[][] a = new int[5][]; // 1
        int[][] b = new int[][]{{1}, {2}}; // 2
        int[][] c = {{1, 2}, {3}}; // 3
    }
}
`;

export const task2 = `
package app;

public class Question {
    int questionNumber;

    public void Question() {
        questionNumber = 1;
    }

    private Question() {
        questionNumber = 2;
    }

    public static void main(String[] args) {
        Question q = new Question();
        System.out.println(q.questionNumber);
    }
}
`;

export const task3 = `
public static void main(String[] args) {
    int[] intArr = {65, 66};
    char[] charArr = {'a', 'b'};
    intArr=charArr; 
    for(int i: intArr) {
        System.out.print(i); 
    }
}
`;

export const task4 = `
public class ArrayExample {
    public static void main(String[] args) {
        int[] numbers = {1, 2, 3, 4, 5};
        int sum = 0;

        for (int i : numbers) {
            sum += i;
        }

        System.out.println("Sum of array elements: " + sum);
    }
}
`;
export const task5 = `
package app;

public class Question {
    int number;
    String title;
    Double complexityLevel;

    public static void main(String[] args) {
        Question q = new Question();
        System.out.println(q.number + " " + q.title + "/" + q.complexityLevel);
    }
}
`;

export const task7 = `
package app;

public class Question {
    public static void main(String[] args) {
        System.out.println(args[0]);
    }
}
`;

export const task8 =`
package app;

public class Question {
    public static void main(String[] args) {
        int val = 0;
        String s = "Java is the best";

        if (s.contains("Java")) {
            int value = 20;
            val = val + 30;
        }

        System.out.println(val + "..." + value);
    }
}
`;

export const task9 = `
package app;

public class Question {
    public static void main(String[] args) {
        String[] strs = new String[2];
        System.out.print(strs + "..." + strs[0] + "..." + strs[1] + "..." + strs[2]);
    }
}
`;

export const task10 =`
package app;

public class Question {
    public static void main(String[] args) {
        double pi = 3.14; //1
        int pi1 = (int) 3.14d; //2
        float fl = 10f; //3
        double dl = 14.3f; //4
    }
}
`;

export const task11=`
package app;

public class Question {
    public static void main(String[] args) {
        int[][] intArr = new char[5]; //1
        long[][] longArr = new int[5]; //2
        char[][] charArr = new byte[5]; //3
        Object[] o = new Question[5]; //4
    }
}
`;

export const task12=`
package app;

public class Question {
    public int number;

    public static void changeNumber(int number) {
        number = 20;
    }

    public static void main(String[] args) {
        int value = 15;
        changeNumber(value);
        System.out.println(value);
    }
}
`;

export const task14=`
package app;

public class Question {
    public static void main(String[] args) {
        char b = 'b';
        char c = 'c';
        System.out.println(b+c);
    }
}
`

export const task15=`
public class StringManipulation {
    public static void main(String[] args) {
        String str1 = "Java";
        String str2 = new String("Java");
        String str3 = "Java";

        boolean result1 = (str1 == str2);
        boolean result2 = (str1.equals(str2));
        boolean result3 = (str1 == str3);

        System.out.println("Result 1: " + result1);
        System.out.println("Result 2: " + result2);
        System.out.println("Result 3: " + result3);
    }
}
`;

export const task16 =`
package app;

public class Question {
    public static void main(String[] args) {
        Boolean b1 = new Boolean(null);
        Boolean b2 = Boolean.parseBoolean("TRUE");
        Boolean b3 = Boolean.valueOf("False");
        System.out.println(b1 + " / " + b2 + "/" + b3);
    }
}
`;

export const task17 = `
package app;

public class Question {

    public static void main(String[] args) {
        double d1 = 123_.15d;
        double d2 = 123_45d;
        double d3 = 123._451d;
        double d4 = 123.45_5d;
    }
}
`;

export const task18 = `
package app;

public class Question {

    public static void main(String[] args) {
        double[] arr = new double[3]; // 1
        arr[0] = 10d; // 2
        arr[1] 10.0; // 3
        System.out.println(arr[1] + arr[2] + arr[3]); //4
    }
}
`;

export const task19 = `
package app;

public class Question {

    public static void main(String[] args) {
        int n = 7;
        long lg = 10;
        boolean bl = true;
        System.out.println((n = 3) + "/" + (lg = 4) + "/" + (bl = 7));
    }
}
`;

export const task20 = `
package app;

class Book {
    String title = "Java Questions";
}

public class Question {

    public static void bookRenamed(Book book) {
        book.title = "Java Questions and Answers";
    }

    public static void main(String[] args) {
        Book bk = new Book();
        bookRenamed(bk);
        System.out.println(bk.title);
    }
}
`;

export const task21 = `
package app;

public class Question {

    public static void main(String[] args) {
        double[] db1 = new double[];
        double[] db2 = new double[1][];
        double[] db3 = new double[][1];
        double[] db4 = new double[1][1];
    }
}
`;

export const task22 = `
package app;

public class Question {

    public static void main(String[] args) {
        int[] arr1 = {10, 20, 30};
        int[] arr2 = {10, 20};
        arr1 = arr2;
        arr2[0] = 0;
        for (int i : arr1) {
            System.out.print(i);
        }
    }
}
`;

export const task23 = `
package app;

public class Question {

    public static void main(String[] args) {
        Boolean b1 = Boolean.valueOf(null);
        Boolean b2 = Boolean.valueOf(false);
        System.out.print((b1 == b2) + " ");
        System.out.print(bl.equals(b2));

    }
}
`;

export const task24 = `
package app;

public class Question {

    public static void main(String[] args) {
        String[][] s1 = new String[2][1];
        String[] s2[] = new String[2][1];
        String[] s3 = new String[2][1];
        String[] s4 =
            {{null}, {null}};
        {{null, null}};

    }
}
`;

export const task25 = `
package app;

public class Question {

    public static void main(String[] args) {
        char c1 = 'a';
        char c2 = -5;
        char c3 = 127;
        char c4 = 254;
        char c5 = 124869;
    }
}
`;

export const task26 = `
package app;

public class Question {
    public int intVal;
    public String strVal;
    public char chVal;
    public double dblVal;

    public static void main(String[] args) {
        Question q = new Question();
        System.out.println(q.toString());
    }

    @Override
    public String toString() {
        return "[intVal=" + intVal + ", " +
                "strVal=" + strVal + ", " +
                "chVal=" + chVal + ", " +
                "dblVal=" + dblVal + "]";
    }
}
`;



export const task28 = `
package app;

public class Question {

    public int val = 5;
    public static int statVal = 5;

    public static void main(String[] args) {
        Question q1 = new Question();
        q1.val = 10;
        q1.statVal = 10;
        Question q2 =
            new Question();
        q2.val = 20;
        q2.statVal = 20;
        System.out.println(q1.val + " / " + q1.statVal);
    }
}
`;

export const task29=`
package app;

public class Question {

    public static void main(String[] args) {
        int[] arr = new int[3];

        arr[0] = 1;
        arr[1] = 2;
        System.out.println(arr[0] + arr[1] + arr[2]);

    }
}
`;

export const task30 = `
import java.util.ArrayList;

public class ArrayListExample {

    public static void main(String[] args) {
        ArrayList<String> fruits = new ArrayList<>();
        fruits.add("Apple");
        fruits.add("Orange");
        fruits.add("Banana");
        String firstFruit = fruits.get(0);
        fruits.remove("Apple");
        boolean containsBanana = fruits.contains("Banana");
        System.out.println("First Fruit: " + firstFruit);
        System.out.println("Contains Banana: " + containsBanana);
    }
}
`;


export const task31 = `
public class ExceptionHandling {
    public static void main(String  args) {
        try {
            int result = divideNumbers (10, 0);
            System.out.println("Result: "+ result);
        } catch (ArithmeticException ex) {
            System.out.println("Exception: "+ ex.getMessage());
        }
    }

    public static int divideNumbers (int dividend, int divisor) {
        return dividend / divisor;
    }
}
`;

export const task32 = `
import java.time.LocalDate;
import java.time. Month;

public class Question {
    public static void main(String[] args) {
        LocalDate date new LocalDate (2024, Month. FEBRUARY, 6);
        System.out.println("Number of month: " +date.getMonthValue());
    }
}
`;

export const task33 = `
interface InterSquare {
    public int squarelt (int n);
}

class SquareImpl implements InterSquare {
    public int squarelt (int n) {
        return n*n;
    }
}
`;

export const task34 = `
public class Question{
    public static void run() {
        System.out.print("A");
        try {
            System.out.print("B");
            System.out.print(10/0);
        } catch (ArithmeticException e) {
            System.out.print("C");
        } finally {
            System.out.print("D");
        }
        System.out.print("E");
    }
    public static void main(String[] args) {
        run();
        System.out.print("F");
    }
}
`;

export const task35 = `
class QuestionException extends Exception{}

public class Question{
    public static void ml () throws Exception{
        System.out.print("A");
        throw new QuestionException();
    }

    public static void main(String[] args) {
        try {
            ml();
        }catch (QuestionException e) {
            System.out.println("B");
        }finally {
            System.out.println("C");
        }
    }
}
`;

export const task38 = `
public class Question{
    public static void main(String  args) {
        LocalDate ld = LocalDate.of (2002, Month. MAY, 45);
        System.out.println(ld.getYear()+100);
    }
}
`;









