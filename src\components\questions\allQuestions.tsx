import { Questions } from "../models/Questions";

//From second course partially passed
import { newSetPart1 } from "./new/questionSet1";
import { newSetPart2 } from "./new/questionSet2";
import { newSetPart3 } from "./new/questionSet3";
import { newSetPart4 } from "./new/questionSet4";
import { newSetPart5 } from "./new/questionSet5";
import { newSetPart6 } from "./new/questionSet6";

//From first course passed
import { passedSetPart1 } from "./old/questionSet1";
import { passedSetPart2 } from "./old/questionSet2";
import { passedSetPart3 } from "./old/questionSet3";
import { passedSetPart4 } from "./old/questionSet4";

//mind maze exam--------------------------------------------
import { arrays } from "./exam/arrays";
import { arraylists } from "./db/arraylist";
import { basics } from"./exam/basics";
import { classes } from"./exam/classes";
import { dates } from './exam/dates';
import { exceptions } from "./exam/exceptions";
import { lambdas } from "./exam/lambdas";
import { methods } from "./exam/methods";
import { operators } from "./exam/operators";
import { controlFlow } from"./exam/controlFlow";
import { strings } from "./exam/strings";

export const mindMazeExam: Questions[] = [ ...strings, ...arraylists, ...arrays, ... basics, ...classes, ...dates, ...exceptions, ...lambdas, ...methods, ...operators, ...controlFlow]

//----------------------------------------------------------

import { arrayDB } from "./db/arrays";
import { basicsDB } from "./db/basics";
import { dataTypeDB } from "./db/dataTypes";
import { excepDB } from "./db/exceptions";
import { inherDB } from "./db/inheritance";
import { lambdaDB } from "./db/lambda";
import { loopsBD } from "./db/loops";
import { methodsDB } from "./db/methods";
import { operatorsDB } from "./db/operators";
import { timeDB } from "./db/time";

export const codeMingleExam: Questions[] = [...arrayDB, ...basicsDB, ...dataTypeDB, ...excepDB, ...lambdaDB, ...loopsBD, ...inherDB, ...operatorsDB, ...methodsDB, ...timeDB ];

//----------------------------------------------------------


export const codeQuestExam: Questions[] = [...passedSetPart1, ...passedSetPart2]
export const byteWizExam: Questions[] = [...passedSetPart3, ...passedSetPart4]

//----------------------------------------------------------

export const javaZen : Questions[] = [...newSetPart1, ...newSetPart2];

export const codeQuru : Questions[] = [...newSetPart3, ...newSetPart4]

export const skillJava : Questions[] = [ ...newSetPart5, ...newSetPart6]

//----------------------------------------------------------


export const allQuestions: Questions[] = [...mindMazeExam, ...codeMingleExam, ...codeQuestExam, ...byteWizExam, ...javaZen];
//export const allQuestions: Questions[] = [...javaZen];

//bonus exam 6



