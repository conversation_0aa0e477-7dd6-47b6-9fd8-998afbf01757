import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { task66, task67, task68, task69 } from "./codeSnippets/questionSet2Snippets";
export const passedSetPart3 : Questions[] = [
    {
        id: 66,
        category: 4,
        isImage: false,
        title: "What is the result?",
        image: task66,
        answers: [
            {
                id: 1,
                content: "[1, 2]",
                correct: true
            }, {
                id: 2,
                content: "[1, 3]",
                correct: false
            }, {
                id: 3,
                content: "[2, 3]",
                correct: false
            }, {
                id: 4,
                content: "[1, 2, 3]",
                correct: false
            }
        ],
        explanation: "After add elements, list will become [1,2,3]. l.remove(2); will remove the eleme" +
                "nt locating at 2 index. Hence 3 will be removed."
    }, {
        id: 67,
        category: 2,
        isImage: false,
        title: "What is the result?",
        image: task67,
        answers: [
            {
                id: 1,
                content: "acl",
                correct: true
            }, {
                id: 2,
                content: "cle",
                correct: false
            }, {
                id: 3,
                content: "Compilation error",
                correct: false
            }, {
                id: 4,
                content: "Exception thrown at runtime",
                correct: false
            }
        ],
        explanation: "substring(begin,end) returns the substring from begin index to end-1 index. Afte" +
                "r applying substring() method multiple times, the final content of the String is" +
                " : acl"
    }, {
        id: 68,
        category: 10,
        isImage: false,
        title: "What will be printed in console?",
        image: task68,
        answers: [
            {
                id: 1,
                content: "2002-01-15",
                correct: false
            }, {
                id: 2,
                content: "2002-00-15",
                correct: false
            }, {
                id: 3,
                content: "Compilation error",
                correct: true
            }, {
                id: 4,
                content: "Exception thrown at runtime",
                correct: false
            }
        ],
        explanation: "Class LocalDate donot have plusHours() method and therefore this code throws com" +
                "pilation error"
    }, {
        id: 69,
        category: 7,
        isImage: false,
        title: "What will be printed in console?",
        image: task69,
        answers: [
            {
                id: 1,
                content: "Parent",
                correct: false
            }, {
                id: 2,
                content: "Parent - m1",
                correct: false
            }, {
                id: 3,
                content: "Child",
                correct: false
            }, {
                id: 4,
                content: "Child - m1",
                correct: false
            }, {
                id: 5,
                content: "Compilation error",
                correct: true
            }
        ],
        explanation: "In Java, when you override a method in a subclass, you must use the same or less" +
                " restrictive access modifier compared to the method in the superclass. The acces" +
                "s modifier protected is more restrictive than public, so the method m1() in the " +
                "Child class should only have one access modifier (public) as Parent. If Parent c" +
                "lass had protected access modifier then Child class could use protected together" +
                " with public. As conclusion this code doesnot work."
    }, {
        id: 70,
        category: 7,
        isImage: false,
        title: "Which of the following are false?",
        answers: [
            {
                id: 1,
                content: "Interface can extend only one interface",
                correct: true
            }, {
                id: 2,
                content: "Interface can extend any number of interfaces",
                correct: false
            }, {
                id: 3,
                content: "Class can implement any number of interfaces",
                correct: false
            }, {
                id: 4,
                content: "Class can extend any number of classes",
                correct: true
            }
        ],
        explanation: "An interface in Java can extend any number of interfaces, providing it with the " +
                "ability to inherit multiple sets of method declarations. A class on the other ha" +
                "nd can extend only one class due to single inheritance. However, a class can imp" +
                "lement multiple interfaces. "
    }, {
        id: 71,
        category: 5,
        isImage: false,
        title: "Which of the following are true about loops in Java?",
        answers: [
            {
                id: 1,
                content: "A 'while' loop checks the condition before executing the loop body.",
                correct: true
            }, {
                id: 2,
                content: "A 'do-while' loop may never execute the loop body if the condition is false.",
                correct: false
            }, {
                id: 3,
                content: "A 'for' loop can have an empty initialization block.",
                correct: true
            }, {
                id: 4,
                content: "A 'break' statement can be used to exit from a specific iteration of a loop.",
                correct: false
            }
        ],
        explanation: "A 'while' loop checks the condition before executing the loop body, which means " +
                "it may not execute at all if the condition is false initially. A 'do-while' loop" +
                ", on the other hand, always executes the loop body at least once because it chec" +
                "ks the condition after the loop body. A 'for' loop can indeed have an empty init" +
                "ialization block, allowing the initialization to be done before the loop. A 'bre" +
                "ak' statement exits the entire loop, not just a specific iteration."
    }, {
        id: 72,
        category: 5,
        isImage: false,
        title: "What will be printed in console?",
        image: "\npublic class LoopTest {\n    public static void main(String[] args) {\n       " +
                " int count = 0;\n        for (int i = 0; i < 10; i++) {\n            if (i % 2 =" +
                "= 0) {\n                continue;\n            }\n            count++;\n        " +
                "}\n        System.out.println(count);\n    }\n}\n",
        answers: [
            {
                id: 1,
                content: "5",
                correct: true
            }, {
                id: 2,
                content: "10",
                correct: false
            }, {
                id: 3,
                content: "4",
                correct: false
            }, {
                id: 4,
                content: "9",
                correct: false
            }
        ],
        explanation: "The code iterates from 0 to 9 using a 'for' loop. The 'if' statement checks if t" +
                "he value of 'i' is even, and if it is, it continues to the next iteration, skipp" +
                "ing the increment of 'count'. Therefore, 'count' is incremented only when 'i' is" +
                " odd. Since there are 5 odd numbers between 0 and 9 (1, 3, 5, 7, 9), the output " +
                "will be 5."
    }, {
        id: 73,
        category: 1,
        isImage: false,
        title: "Which of the following are false?",
        image: "public class Example {\n    public static void main(String[] args) {\n        in" +
                "t x = 5;\n        if (x > 3) {\n            System.out.println(\"x is greater th" +
                "an 3\");\n        }\n    }\n}\n",
        answers: [
            {
                id: 1,
                content: "The code will compile without errors",
                correct: false
            }, {
                id: 2,
                content: "The code will print 'x is greater than 3'",
                correct: false
            }, {
                id: 3,
                content: "The 'if' statement checks if x is greater than 3",
                "correct": false
            }, {
                id: 4,
                content: "The code will throw a runtime exception",
                correct: true
            }
        ],
        explanation: "The provided code will compile and run correctly. It will print 'x is greater th" +
                "an 3' because the condition in the 'if' statement is true. Therefore, options 1," +
                " 2, and 3 are true, and option 4 is false. The code will not throw any runtime e" +
                "xceptions."
    }, {
        id: 74,
        category: 1,
        isImage: false,
        title: "What will be the output of the following code?",
        image: "public class Test {\n    public static void main(String[] args) {\n        int a" +
                " = 10;\n        int b = 20;\n        if (a < b) {\n            a = b;\n        }" +
                "\n        System.out.println(a);\n    }\n}\n",
        answers: [
            {
                id: 1,
                content: "10",
                correct: false
            }, {
                id: 2,
                content: "20",
                correct: true
            }, {
                id: 3,
                content: "0",
                correct: false
            }, {
                id: 4,
                content: "30",
                correct: false
            }
        ],
        explanation: "The code compares the values of 'a' and 'b'. Since 'a' (10) is less than 'b' (20" +
                "), the 'if' statement assigns the value of 'b' to 'a'. Therefore, the output wil" +
                "l be '20'."
    }, {
        id: 75,
        category: 1,
        isImage: false,
        title: "What will be the output of the following code?",
        image: "public class Main {\n    public static void main(String[] args) {\n        int n" +
                "um = 5;\n        switch (num) {\n            case 1:\n                System.out" +
                ".println(\"One\");\n                break;\n            case 5:\n               " +
                " System.out.println(\"Five\");\n                break;\n            default:\n  " +
                "              System.out.println(\"Default\");\n        }\n    }\n}\n",
        answers: [
            {
                id: 1,
                content: "One",
                correct: false
            }, {
                id: 2,
                content: "Five",
                correct: true
            }, {
                id: 3,
                content: "Default",
                correct: false
            }, {
                id: 4,
                content: "None of the above",
                correct: false
            }
        ],
        explanation: "The code uses a switch statement to check the value of 'num'. Since 'num' is 5, " +
                "the case '5' will be executed, and it will print 'Five'. The 'break' statement p" +
                "revents the fall-through to the 'default' case."
    }, {
        id: 76,
        category: 1,
        isImage: false,
        title: "What will be the output of the following code?",
        image: "public class Question {\n    public static void main(String[] args) {\n        i" +
                "nt x = 5;\n        int y = x++;\n        int z = ++y;\n        System.out.printl" +
                "n(x+y+z);\n    }\n}\n",
        answers: [
            {
                id: 1,
                content: "16",
                correct: false
            }, {
                id: 2,
                content: "17",
                correct: false
            }, {
                id: 3,
                content: "18",
                correct: true
            }, {
                id: 4,
                content: "19",
                correct: false
            }, {
                id: 5,
                content: "20",
                correct: false
            }
        ],
        explanation: "int x = 5; line declares an integer variable x and initializes it with the value" +
                " 5. int y = x++; line declares an integer variable y and initializes it with the" +
                " value of x, then increments x by 1. y gets the value of x before the increment," +
                " so y will be 5. After this operation, x will be incremented to 6. int z = ++y; " +
                "line declares an integer variable z and initializes it with the value of y after" +
                " it has been incremented by 1. y is incremented first (pre-increment), so y beco" +
                "mes 6, and then z is assigned this new value. z will be 6."
    }, {
        id: 77,
        category: 1,
        isImage: false,
        title: "What will be the output of the following code?",
        image: "public class Main {\n    public static void main(String[] args) {\n        int a" +
                " = 10;\n        int b = 20;\n        System.out.println(a + b);\n    }\n}\n",
        answers: [
            {
                id: 1,
                content: "10",
                correct: false
            }, {
                id: 2,
                content: "20",
                correct: false
            }, {
                id: 3,
                content: "30",
                correct: true
            }, {
                id: 4,
                content: "40",
                correct: false
            }
        ],
        explanation: "The code adds the values of 'a' and 'b'. Since 'a' is 10 and 'b' is 20, the resu" +
                "lt of 'a + b' is 30. Therefore, the output will be '30'."
    }, {
        id: 78,
        category: 2,
        isImage: false,
        title: "What will be the output of the following code?",
        image: "public class Main {\n    public static void main(String[] args) {\n        byte " +
                "b = 100;\n        int i = b * 2;\n        System.out.println(i);\n    }\n}\n",
        answers: [
            {
                id: 1,
                content: "100",
                correct: false
            }, {
                id: 2,
                content: "200",
                correct: true
            }, {
                id: 3,
                content: "0",
                correct: false
            }, {
                id: 4,
                content: "Compile error",
                correct: false
            }
        ],
        explanation: "The code multiplies the value of the byte variable 'b' by 2 and stores the resul" +
                "t in an int variable 'i'. Since 'b' is 100, 'b * 2' is 200. Therefore, the outpu" +
                "t will be '200'."
    }, {
        id: 79,
        category: 3,
        isImage: false,
        title: "What will be the output of the following code?",
        image: "public class Main {\n    public static void main(String[] args) {\n        int x" +
                " = 5;\n        int y = 10;\n        int z = x++ + --y;\n        System.out.print" +
                "ln(z);\n    }\n}\n",
        answers: [
            {
                id: 1,
                content: "14",
                correct: true
            }, {
                id: 2,
                content: "15",
                correct: false
            }, {
                id: 3,
                content: "16",
                correct: false
            }, {
                id: 4,
                content: "Compile error",
                correct: false
            }
        ],
        explanation: "The code demonstrates the use of post-increment (x++) and pre-decrement (--y) op" +
                "erators. Initially, x is 5 and y is 10. The expression 'x++ + --y' is evaluated " +
                "as '5 + 9' because 'x++' uses the current value of x (5) and then increments it," +
                " and '--y' decrements y (10) to 9. Therefore, the output will be '14'."
    }, {
        id: 80,
        category: 4,
        isImage: false,
        title: "What will be the output of the following code?",
        image: "public class Main {\n    public static void main(String[] args) {\n        int[]" +
                " arr = {1, 2, 3, 4, 5};\n        System.out.println(arr[2]);\n    }\n}\n",
        answers: [
            {
                id: 1,
                content: "1",
                correct: false
            }, {
                id: 2,
                content: "2",
                correct: false
            }, {
                id: 3,
                content: "3",
                correct: true
            }, {
                id: 4,
                content: "4",
                correct: false
            }
        ],
        explanation: "The code accesses the third element (index 2) of the array 'arr'. Since arrays a" +
                "re zero-indexed in Java, 'arr[2]' refers to the third element, which is 3. There" +
                "fore, the output will be '3'."
    }, {
        id: 81,
        category: 5,
        isImage: false,
        title: "What will be the output of the following code?",
        image: "public class Main {\n    public static void main(String[] args) {\n        int s" +
                "um = 0;\n        for (int i = 1; i <= 5; i++) {\n            sum += i;\n        " +
                "}\n        System.out.println(sum);\n    }\n}\n",
        answers: [
            {
                id: 1,
                content: "10",
                correct: false
            }, {
                id: 2,
                content: "15",
                correct: true
            }, {
                id: 3,
                content: "20",
                correct: false
            }, {
                id: 4,
                content: "25",
                correct: false
            }
        ],
        explanation: "The code uses a 'for' loop to sum the integers from 1 to 5. The variable 'sum' i" +
                "s initialized to 0 and is incremented by 'i' in each iteration of the loop. The " +
                "sum of the integers from 1 to 5 is 15. Therefore, the output will be '15'."
    }, {
        id: 82,
        category: 6,
        isImage: false,
        title: "What will be the output of the following code?",
        image: "public class Main {\n    public static void main(String[] args) {\n        Main " +
                "obj = new Main();\n        System.out.println(obj.add(3, 4));\n    }\n\n    publ" +
                "ic int add(int a, int b) {\n        return a + b;\n    }\n}\n",
        answers: [
            {
                id: 1,
                content: "3",
                correct: false
            }, {
                id: 2,
                content: "4",
                correct: false
            }, {
                id: 3,
                content: "7",
                correct: true
            }, {
                id: 4,
                content: "Compile error",
                correct: false
            }
        ],
        explanation: "The code defines a method 'add' that takes two integers as parameters and return" +
                "s their sum. In the 'main' method, an instance of 'Main' is created and the 'add" +
                "' method is called with arguments 3 and 4. The result, 7, is printed. Therefore," +
                " the output will be '7'."
    }, {
        id: 83,
        category: 7,
        isImage: false,
        title: "What will be the output of the following code?",
        image: "class A {\n    public void print() {\n        System.out.println(\"Class A\");\n" +
                "    }\n}\n\nclass B extends A {\n    public void print() {\n        System.out.p" +
                "rintln(\"Class B\");\n    }\n}\n\npublic class Main {\n    public static void ma" +
                "in(String[] args) {\n        A obj = new B();\n        obj.print();\n    }\n}\n",
        answers: [
            {
                id: 1,
                content: "Class A",
                correct: false
            }, {
                id: 2,
                content: "Class B",
                correct: true
            }, {
                id: 3,
                content: "Compile error",
                correct: false
            }, {
                id: 4,
                content: "Runtime error",
                correct: false
            }
        ],
        explanation: "The code demonstrates method overriding. An instance of class B is assigned to a" +
                " reference of type A. At runtime, the overridden method in class B is called, so" +
                " the output will be 'Class B'."
    }, {
        id: 84,
        category: 8,
        isImage: false,
        title: "What will be the output of the following code?",
        image: "public class Main {\n    public static void main(String[] args) {\n        try {" +
                "\n            int result = 10 / 0;\n            System.out.println(result);\n   " +
                "     } catch (ArithmeticException e) {\n            System.out.println(\"Arithme" +
                "ticException\");\n        }\n    }\n}\n",
        answers: [
            {
                id: 1,
                content: "10",
                correct: false
            }, {
                id: 2,
                content: "ArithmeticException",
                correct: true
            }, {
                id: 3,
                content: "0",
                correct: false
            }, {
                id: 4,
                content: "Compile error",
                correct: false
            }
        ],
        explanation: "The code attempts to divide 10 by 0, which causes an ArithmeticException. The ex" +
                "ception is caught by the catch block, which prints 'ArithmeticException'. Theref" +
                "ore, the output will be 'ArithmeticException'."
    }, {
        id: 85,
        category: 9,
        isImage: false,
        title: "What will be the output of the following code?",
        image: "import java.util.*;\nimport java.util.stream.*;\n\npublic class Main {\n    publ" +
                "ic static void main(String[] args) {\n        List<Integer> list = Arrays.asList" +
                "(1, 2, 3, 4, 5);\n        long count = list.stream().filter(n -> n % 2 == 0).cou" +
                "nt();\n        System.out.println(count);\n    }\n}\n",
        answers: [
            {
                id: 1,
                content: "1",
                correct: false
            }, {
                id: 2,
                content: "2",
                correct: true
            }, {
                id: 3,
                content: "3",
                correct: false
            }, {
                id: 4,
                content: "4",
                correct: false
            }
        ],
        explanation: "The code uses a stream to filter the list for even numbers and counts them. The " +
                "even numbers in the list are 2 and 4. Therefore, the count of even numbers is 2," +
                " and the output will be '2'."
    }, {
        id: 86,
        category: 10,
        isImage: false,
        title: "What will be the output of the following code?",
        image: "import java.time.*;\nimport java.time.format.*;\n\npublic class Main {\n    publ" +
                "ic static void main(String[] args) {\n        LocalDateTime dateTime = LocalDate" +
                "Time.of(2024, 6, 3, 12, 30);\n        DateTimeFormatter formatter = DateTimeForm" +
                "atter.ofPattern(\"yyyy-MM-dd HH:mm:ss\");\n        String formattedDateTime = da" +
                "teTime.format(formatter);\n        System.out.println(formattedDateTime);\n    }" +
                "\n}\n",
        answers: [
            {
                id: 1,
                content: "2024-06-03 12:30:00",
                correct: true
            }, {
                id: 2,
                content: "2024-06-03T12:30:00",
                correct: false
            }, {
                id: 3,
                content: "2024/06/03 12:30:00",
                correct: false
            }, {
                id: 4,
                content: "Compile error",
                correct: false
            }
        ],
        explanation: "The code creates a LocalDateTime object representing June 3, 2024, at 12:30 PM. " +
                "It then formats the date and time using a custom pattern 'yyyy-MM-dd HH:mm:ss'. " +
                "The output will be '2024-06-03 12:30:00'."
    }, {
        id: 87,
        category: 10,
        isImage: false,
        title: "Which class should you use to obtain the current date and time in Java 8?",
        answers: [
            {
                id: 1,
                content: "Date",
                correct: false
            }, {
                id: 2,
                content: "LocalDate",
                correct: false
            }, {
                id: 3,
                content: "LocalDateTime",
                correct: true
            }, {
                id: 4,
                content: "Calendar",
                correct: false
            }
        ],
        explanation: "In Java 8, you should use the LocalDateTime class to obtain the current date and" +
                " time without time zone information."
    }, {
        id: 88,
        category: 10,
        isImage: false,
        title: "Which of the following is true regarding DateTimeFormatter in Java 8?",
        answers: [
            {
                id: 1,
                content: "It is immutable and thread-safe.",
                correct: true
            }, {
                id: 2,
                content: "It cannot parse strings into LocalDateTime objects.",
                correct: false
            }, {
                id: 3,
                content: "It requires synchronization when used by multiple threads.",
                correct: false
            }, {
                id: 4,
                content: "It is available only in the java.util package.",
                correct: false
            }
        ],
        explanation: "DateTimeFormatter in Java 8 is immutable and thread-safe, which means it can saf" +
                "ely be used by multiple threads without the need for synchronization."
    }, {
        "id": 89,
        "category": 2,
        "isImage": false,
        "title": "What will be the output of the following code?",
        "image": "public class Main {\n    public static void main(String[] args) {\n        doubl" +
                "e num = 10.5;\n        int result = (int) num;\n        System.out.println(resul" +
                "t);\n    }\n}\n",
        "answers": [
            {
                "id": 1,
                "content": "10",
                "correct": true
            }, {
                "id": 2,
                "content": "10.5",
                "correct": false
            }, {
                "id": 3,
                "content": "Compilation error",
                "correct": false
            }, {
                "id": 4,
                "content": "Runtime error",
                "correct": false
            }
        ],
        "explanation": "In this code, a double variable 'num' with value 10.5 is cast to an int using (i" +
                "nt) casting. When you cast a floating-point number to an integer in Java, the fr" +
                "actional part is truncated, and the integer part is retained. Therefore, the out" +
                "put will be '10'."
    }, {
        "id": 90,
        "category": 2,
        "isImage": false,
        "title": "Which of the following data types in Java is NOT a primitive data type?",
        "answers": [
            {
                "id": 1,
                "content": "int",
                "correct": false
            }, {
                "id": 2,
                "content": "double",
                "correct": false
            }, {
                "id": 3,
                "content": "String",
                "correct": true
            }, {
                "id": 4,
                "content": "boolean",
                "correct": false
            }
        ],
        "explanation": "In Java, 'String' is not a primitive data type. It is a reference data type, spe" +
                "cifically a class from the java.lang package."
    }, {
        "id": 91,
        "category": 2,
        "isImage": false,
        "title": "What is the default value of a boolean variable in Java?",
        "answers": [
            {
                "id": 1,
                "content": "0",
                "correct": false
            }, {
                "id": 2,
                "content": "1",
                "correct": false
            }, {
                "id": 3,
                "content": "true",
                "correct": false
            }, {
                "id": 4,
                "content": "false",
                "correct": true
            }
        ],
        "explanation": "The default value of a boolean variable in Java is 'false'."
    }, {
        "id": 92,
        "category": 6,
        "isImage": false,
        "title": "What is the difference between a method declaration and a method call in Java?",
        "answers": [
            {
                "id": 1,
                "content": "A method declaration defines the method's behavior, while a method call invokes " +
                        "the method to execute its behavior.",
                "correct": true
            }, {
                "id": 2,
                "content": "There is no difference between a method declaration and a method call.",
                "correct": false
            }, {
                "id": 3,
                "content": "A method declaration defines the method's return type, while a method call speci" +
                        "fies the arguments passed to the method.",
                "correct": false
            }, {
                "id": 4,
                "content": "A method declaration specifies the class that contains the method, while a metho" +
                        "d call specifies the object on which the method is invoked.",
                "correct": false
            }
        ],
        "explanation": "A method declaration defines the method's behavior, including its name, return t" +
                "ype, parameters, and implementation. On the other hand, a method call invokes th" +
                "e method to execute its behavior by providing arguments (if required) and receiv" +
                "ing the return value (if any)."
    }, {
        "id": 93,
        "category": 8,
        "isImage": false,
        "title": "Which of the following keywords is used to handle exceptions in Java?",
        "answers": [
            {
                "id": 1,
                "content": "throw",
                "correct": false
            }, {
                "id": 2,
                "content": "finally",
                "correct": false
            }, {
                "id": 3,
                "content": "catch",
                "correct": true
            }, {
                "id": 4,
                "content": "try",
                "correct": false
            }
        ],
        "explanation": "The 'catch' keyword is used to handle exceptions in Java. It is followed by a bl" +
                "ock of code that specifies what to do when a particular exception is thrown."
    }, {
        "id": 94,
        "category": 6,
        "isImage": false,
        "title": "What is method overloading in Java?",
        "answers": [
            {
                "id": 1,
                "content": "Method overloading is a feature that allows a class to have multiple methods wit" +
                        "h the same name but different parameter lists.",
                "correct": true
            }, {
                "id": 2,
                "content": "Method overloading is a feature that allows a method to return multiple values.",
                "correct": false
            }, {
                "id": 3,
                "content": "Method overloading is a feature that allows a method to accept multiple argument" +
                        "s of the same type.",
                "correct": false
            }, {
                "id": 4,
                "content": "Method overloading is a feature that allows a method to be invoked with differen" +
                        "t access modifiers.",
                "correct": false
            }
        ],
        "explanation": "Method overloading is a feature in Java that allows a class to have multiple met" +
                "hods with the same name but different parameter lists. This enables developers t" +
                "o create methods with similar functionality while providing flexibility in terms" +
                " of the number or types of arguments accepted."
    }, {
        "id": 95,
        "category": 6,
        "isImage": false,
        "title": "What is method overriding in Java?",
        "answers": [
            {
                "id": 1,
                "content": "Method overriding is a feature that allows a subclass to provide a specific impl" +
                        "ementation of a method that is already defined in its superclass.",
                "correct": true
            }, {
                "id": 2,
                "content": "Method overriding is a feature that allows a method to return multiple values.",
                "correct": false
            }, {
                "id": 3,
                "content": "Method overriding is a feature that allows a method to accept multiple arguments" +
                        " of the same type.",
                "correct": false
            }, {
                "id": 4,
                "content": "Method overriding is a feature that allows a method to be invoked with different" +
                        " access modifiers.",
                "correct": false
            }
        ],
        "explanation": "Method overriding is a feature in Java that allows a subclass to provide a speci" +
                "fic implementation of a method that is already defined in its superclass. This e" +
                "nables polymorphic behavior, where a method call on a superclass reference can i" +
                "nvoke the subclass implementation at runtime."
    }, {
        "id": 96,
        "category": 8,
        "isImage": false,
        "title": "What will happen if you attempt to catch an exception that is not thrown in the " +
                "try block?",
        "answers": [
            {
                "id": 1,
                "content": "Compile error",
                "correct": false
            }, {
                "id": 2,
                "content": "Runtime error",
                "correct": false
            }, {
                "id": 3,
                "content": "No effect, the code will compile and run without errors.",
                "correct": false
            }, {
                "id": 4,
                "content": "Compilation error if the exception is checked, no effect if it is unchecked.",
                "correct": true
            }
        ],
        "explanation": "Attempting to catch an exception that is not thrown in the try block results in " +
                "a compilation error if the exception is checked. This is because the catch block" +
                " is unreachable code. If the exception is unchecked, such as a RuntimeException," +
                " there will be no compilation error, but the catch block will have no effect."
    }, {
        "id": 97,
        "category": 8,
        "isImage": false,
        "title": "What is the purpose of the 'finally' block in a try-catch-finally statement?",
        "answers": [
            {
                "id": 1,
                "content": "To handle exceptions thrown within the try block.",
                "correct": false
            }, {
                "id": 2,
                "content": "To ensure that certain code is always executed, regardless of whether an excepti" +
                        "on is thrown or caught.",
                "correct": true
            }, {
                "id": 3,
                "content": "To specify code that will be executed only if an exception occurs.",
                "correct": false
            }, {
                "id": 4,
                "content": "To specify code that will be executed if the try block completes without throwin" +
                        "g any exceptions.",
                "correct": false
            }
        ],
        "explanation": "The 'finally' block in a try-catch-finally statement is used to ensure that cert" +
                "ain code is always executed, regardless of whether an exception is thrown or cau" +
                "ght. This is useful for releasing resources or performing cleanup operations."
    }, {
        "id": 98,
        "category": 9,
        "isImage": false,
        "title": "What is the syntax for a lambda expression in Java?",
        "answers": [
            {
                "id": 1,
                "content": "() -> {}",
                "correct": false
            }, {
                "id": 2,
                "content": "(parameters) -> expression",
                "correct": true
            }, {
                "id": 3,
                "content": "(parameters) { body }",
                "correct": false
            }, {
                "id": 4,
                "content": "(parameters) => expression",
                "correct": false
            }
        ],
        "explanation": "The syntax for a lambda expression in Java consists of parameters, an arrow (->)" +
                ", and a body. If the body contains a single expression, curly braces are optiona" +
                "l. Otherwise, the body must be enclosed in curly braces."
    }, {
        "id": 99,
        "category": 9,
        "isImage": false,
        "title": "Which of the following represents a valid lambda expression?",
        "answers": [
            {
                "id": 1,
                "content": "() -> System.out.println(\"Hello\")",
                "correct": true
            }, {
                "id": 2,
                "content": "(int x, int y) -> x + y",
                "correct": true
            }, {
                "id": 3,
                "content": "(String s) => s.length()",
                "correct": false
            }, {
                "id": 4,
                "content": "(double d) { return d * d; }",
                "correct": false
            }
        ],
        "explanation": "Both option 1 and option 2 represent valid lambda expressions in Java. Option 1 " +
                "represents a lambda expression with no parameters that prints 'Hello'. Option 2 " +
                "represents a lambda expression with two parameters that calculates their sum. La" +
                "mbda expression in Java uses -> instead of => making option 3 false."
    }, {
        "id": 100,
        "category": 9,
        "isImage": false,
        "title": "What is the output of the following code snippet?",
        "image": "import java.util.function.Predicate;\n\npublic class Main {\n    public static v" +
                "oid main(String[] args) {\n        Predicate<String> predicate = s -> s.length()" +
                " > 5;\n        System.out.println(predicate.test(\"Lambda\"));\n    }\n}\n",
        "answers": [
            {
                "id": 1,
                "content": "true",
                "correct": true
            }, {
                "id": 2,
                "content": "false",
                "correct": false
            }, {
                "id": 3,
                "content": "Compilation error",
                "correct": false
            }, {
                "id": 4,
                "content": "Runtime error",
                "correct": false
            }
        ],
        "explanation": "The code defines a Predicate<String> that checks if the length of the input stri" +
                "ng is greater than 5. When 'predicate.test(\"Lambda\")' is called, it returns 't" +
                "rue' because the length of 'Lambda' is greater than 5."
    }, {
        "id": 101,
        "category": 5,
        "isImage": false,
        "title": "What will be the output of the following code snippet?",
        "image": "public class Main {\n    public static void main(String[] args) {\n        int[]" +
                " numbers = {1, 2, 3, 4, 5};\n        for (int i = 0; i < numbers.length; i++) {" +
                "\n            if (numbers[i] % 2 == 0) {\n                break;\n            }" +
                "\n            System.out.print(numbers[i] + \" \");\n        }\n    }\n}\n",
        "answers": [
            {
                "id": 1,
                "content": "1",
                "correct": true
            }, {
                "id": 2,
                "content": "1 3 5",
                "correct": false
            }, {
                "id": 3,
                "content": "1 2 3 4 5",
                "correct": false
            }, {
                "id": 4,
                "content": "Compilation error",
                "correct": false
            }
        ],
        "explanation": "The code iterates over the elements of the 'numbers' array. If an element is eve" +
                "n (divisible by 2), the 'break' statement exits the loop. Therefore, only one nu" +
                "mber 1 is printed."

    }, {
        "id": 102,
        "category": 5,
        "isImage": false,
        "title": "What is the output of the following code snippet?",
        "image": "public class Main {\n    public static void main(String[] args) {\n        int[]" +
                " numbers = {1, 2, 3, 4, 5};\n        for (int i = numbers.length - 1; i >= 0; i-" +
                "-) {\n            System.out.print(numbers[i] + \" \");\n        }\n    }\n}\n",
        "answers": [
            {
                "id": 1,
                "content": "5 4 3 2 1",
                "correct": true
            }, {
                "id": 2,
                "content": "1 2 3 4 5",
                "correct": false
            }, {
                "id": 3,
                "content": "Compilation error",
                "correct": false
            }, {
                "id": 4,
                "content": "No output",
                "correct": false
            }
        ],
        "explanation": "The code iterates over the elements of the 'numbers' array in reverse order, sta" +
                "rting from the last element (index 'numbers.length - 1') and ending at the first" +
                " element (index '0'). Therefore, it prints the numbers in reverse order: '5 4 3 " +
                "2 1'."
    }, {
        "id": 103,
        "category": 3,
        "isImage": false,
        "title": "What will be the value of 'result' after executing the following code snippet?",
        "image": "public class Main {\n    public static void main(String[] args) {\n        int x" +
                " = 5;\n        int y = 3;\n        int result = x++ * ++y;\n        System.out.p" +
                "rintln(result);\n    }\n}\n",
        "answers": [
            {
                "id": 1,
                "content": "24",
                "correct": false
            }, {
                "id": 2,
                "content": "15",
                "correct": false
            }, {
                "id": 3,
                "content": "18",
                "correct": false
            }, {
                "id": 4,
                "content": "20",
                "correct": true
            }
        ],
        "explanation": "In the expression 'x++ * ++y', 'x++' post-increments 'x' (resulting in 'x' being" +
                " '6' after this operation), while '++y' pre-increments 'y' (resulting in 'y' bei" +
                "ng '4' for the multiplication). Therefore, the expression evaluates to '5 * 4', " +
                "which equals '20'."
    }, {
        "id": 104,
        "category": 3,
        "isImage": false,
        "title": "What will be the output of the following code snippet?",
        "image": "public class Main {\n    public static void main(String[] args) {\n        int a" +
                " = 10;\n        int b = 5;\n        int c = 3;\n        System.out.println(++a *" +
                " b-- / c % a);\n    }\n}\n",
        "answers": [
            {
                "id": 1,
                "content": "4",
                "correct": false
            }, {
                "id": 2,
                "content": "5",
                "correct": false
            }, {
                "id": 3,
                "content": "6",
                "correct": false
            }, {
                "id": 4,
                "content": "7",
                "correct": true
            }
        ],
        "explanation": "The expression '++a * b-- / c % a' involves multiple operators with different pr" +
                "ecedence. First, '++a' increments 'a' to '11'. Then, 'b--' post-decrements 'b' t" +
                "o '4'. Next, '11 * 5' results in '55'. After that, '55 / 3' results in '18'. Fin" +
                "ally, '18 % 11' results in '7'."
    }, {
        "id": 105,
        "category": 4,
        "isImage": false,
        "title": "What will be the output of the following code snippet?",
        "image": "public class Main {\n    public static void main(String[] args) {\n        int[]" +
                " numbers = {1, 2, 3, 4, 5};\n        for (int i = 0; i < numbers.length; i++) {" +
                "\n            System.out.print(numbers[i] + \" \");\n            i++;\n        }" +
                "\n    }\n}\n",
        "answers": [
            {
                "id": 1,
                "content": "1 3 5",
                "correct": true
            }, {
                "id": 2,
                "content": "1 2 3 4 5",
                "correct": false
            }, {
                "id": 3,
                "content": "2 4",
                "correct": false
            }, {
                "id": 4,
                "content": "Compilation error",
                "correct": false
            }
        ],
        "explanation": "The 'for' loop iterates over the elements of the 'numbers' array. Within the loo" +
                "p body, each element of the array is printed. Additionally, 'i' is incremented b" +
                "y 1 in each iteration of the loop, effectively skipping every other element. The" +
                "refore, it prints only the elements at odd indices, resulting in '1 3 5'."
    }

];