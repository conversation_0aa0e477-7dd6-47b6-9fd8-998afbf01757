export { task1000, task1001, task1002, task1008, task1010, task1012, task1014, task1015 };

const task1000 = `
class Name {
    public Name() {}

    public Name(String name) {
        System.out.print(name + " / ");
    }
}

public class Student {
    static Name n1 = new Name("<PERSON>");
    Name n2 = new Name("<PERSON>");

    public static void main(String[] args) {
        Student s1 = new Student();
        Name n3 = new Name("Alex");
    }

    static Name n4 = new Name("Ben");
}
`;

const task1001 = `
public class Student {
    public static int bestGrade = MAX_VALUE;

    public static double returnPi() {
        return PI;
    }

    public static void main(String[] args) {
        System.out.println(returnPi());
    }
}
`;

const task1002 = `
public class Student {
    int grade;
    static int grade; 

    public static int getGrade() {
        return grade;
    }

    public int getGrade() {
        return grade;
    }
}
`;

export const task1004 = `
package app;

public class Student {
    public static void main(String[] args) throws Exception {
    Exam mathExam = new Exam();
    mathExam.start("Math");
    System.out.println("Duration of the exam is " + duration);
}

//-----------------------------------------

package app.exam;

    public class Exam {
    public static int duration;
    public void start(String examType) {
    System.out.println("Starting Math");
    }
}
`;

export const task1007 = `
public class Student {
    public static int main(String args){
    System.out.println("Hello World");
    }
}

public class Exam {
    public static String grade = "A";
}
`;

const task1008 = `
public class Exam {
    public static void main(String[] args) {
        String exam1 = args[0];
        String exam2 = args[1];
        String exam3 = args[2];
        System.out.println(exam2);
    }
}
`;

const task1010 = `
public class Student {
    public static double main(String[] args) { // 1
        public int grade = 12; // 2
        System.out.println(7); // 3
        for (int i = 0; true;) {} // 4
        return 0; //5
    }
}

public class Exam {} //6
`;

const task1012 = `
public class Student {
    public static void main(String[] args) {
        System.out.println(args.length + " arguments");
    }
}
`;

const task1014 = `
public class Student {
    public String name;
    public int grade;

    public void setName(String studentName) {
        this.name = studentName;
    }

    public void setGrade(int stGrade) {
        this.grade = stGrade;
    }
}
`;

const task1015 = `
public static void main(String[] args) {
    int p = 1;
    int[] q = new int[10];
    int r = q[p];
    int s = r - p;
    System.out.println(r = s);
}
`;


export const task1016 = `
public class Student {

    public static void main(String[] args) {
        int grade = 7;
        System.out.println(grade > 10 ? runA() : runB());
    }

    public static int runA() {
        System.out.println("Running A");
        return 0;
    }

    public static void runB() {
        System.out.println("Running B");
        return;
    }
}
`;

// 1017.png
export const task1017 = `
public class Student {

    public static int increment(int i) {
        if(i == 0) {
            return ++i;
        }
        return i++;
    }

    public static void main(String[] args) {
        int amount = increment(args.length);
        amount += 7 + ++amount;
        System.out.println(amount);
    }
}
`;

export const task1018 = `
public class Student {

    public void evaluateGrade(String grade) {
        switch(grade) {
            default: System.out.println("Not evaluated"); break;
            case "B": System.out.println("Good");
            case "B+": System.out.println("Very Good"); break;
            case "A": System.out.println("Best");
        }
    }

    public static void main(String[] args) {
        Student to = new Student();
        to.evaluateGrade("B");
    }
`;

export const task1019 = `
public class Student {

    public static void crazyIf(boolean bool) {
        if (bool = true)
        if (bool = false)
        System.out.println("1");
        else
        System.out.println("2");
        else
        System.out.println("3");
    }

    public static void main(String[] args) {
        crazyIf(true);
        crazyIf(false);
    }
}
`;

// 1020.png
export const task1020 = `
public class Student {

    public static void main(String[] args) {
        int res1 = 1 + 2 * 3 / 2;
        int res2 = 2 * 3 % 4;

        int total = res1 + res2;
        System.out.println(total);
    }
}
`;

// 1021.png
export const task1021 = `
public class Student {

    public static void crazyIf(boolean bool) {
        if (bool = false)
        if (bool = true)
        System.out.println("True");
        else
        System.out.println("False");
    }

    public static void main(String[] args) {
        crazyIf(true);
        crazyIf(!true);
    }
}
`;


