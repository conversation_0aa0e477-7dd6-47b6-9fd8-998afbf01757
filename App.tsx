import React, { useEffect, useState } from 'react';
import Nav from './src/navigation/Nav';
import 'expo-dev-client';
import { StatusBar, View, StyleSheet } from 'react-native';
import { FontProvider } from './src/helpers/FontProvider';
import { InitExamService } from './src/service/InitExamService';
import LoadingDots from 'react-native-loading-dots';
import { scale, verticalScale } from 'react-native-size-matters';
import GradientContainer from './src/helpers/GradientContainer';
import { appColors } from './src/utils/appColors';


export default function App() {
  const [isExamLoaded, setExamLoaded] = useState(false);
  useEffect(() => {
    const initialize = async () => {
      await InitExamService.check();
      setExamLoaded(true)
    };
    initialize();
  }, []);

  if (!isExamLoaded) {
    return (
      <React.Fragment>
          <StatusBar 
            barStyle="default" 
            translucent={true}
            backgroundColor="transparent"
          />
        <GradientContainer>        
          <View style={styles.dotsContainer}>
            <LoadingDots
                size={scale(35)}
                colors={[appColors.white, appColors.white, appColors.white]}
                dots={3}
                borderRadius={scale(15)}/>
          </View>
        </GradientContainer>
      </React.Fragment>
    );
  }

  return (
    <FontProvider>
      <StatusBar 
        barStyle="default" 
        translucent={true}
        backgroundColor="transparent"
      />
      <Nav/>
    </FontProvider>
  );
  
}

const styles = StyleSheet.create({
    dotsContainer: {
      marginHorizontal: scale(40),
      marginTop: verticalScale(190),
      width: '40%',
      alignContent: 'center',
      alignSelf: 'center',
      height: '15%',
      justifyContent: 'center',
    },
});