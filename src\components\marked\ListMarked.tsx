import { FontAwesome, MaterialCommunityIcons } from "@expo/vector-icons";
import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions
} from "react-native";
import { AppBar, IconButton } from "@react-native-material/core";
import { generateRandomCode } from "../../service/RandomService";
import { QuestionService } from "../../service/QuestionService";
import { Questions } from "../models/Questions";
import { MarkedQuestionsService } from "../../service/MarkedQuestionsService";
import {
  BannerAd,
  BannerAdSize,
  TestIds,
} from "react-native-google-mobile-ads";
import { getCategory } from "../models/Categories";
import { scale, verticalScale } from "react-native-size-matters";
import GradientContainer from "../../helpers/GradientContainer";
import { appColors } from "../../utils/appColors";
import LoadingDots from "react-native-loading-dots";


interface GeneralState {
  questions: Questions[];
  loading: boolean;

}

const deviceWidth = Dimensions.get('window').width;

const adUnitId = __DEV__  ? TestIds.BANNER  : "ca-app-pub-5981144475529351/3452174828";

class ListMarked extends Component<{}, GeneralState> {
  constructor(props: {}) {
    super(props);
    this.state = {
      questions: [],
      loading: true,
  
    };
  }

  componentDidMount(): void {
    this.loadMarkedQuestions();
    this.loadMarkedQuestions = this.loadMarkedQuestions.bind(this);    
    this.props.navigation.addListener("focus", this.loadMarkedQuestions);
  }

  async cleanMarkedQuestions(){
      await MarkedQuestionsService.removeAllMarkedQuestions();
      this.setState({questions:[]});
  }

  async loadMarkedQuestions() {
    var markedQuestions: Promise<number[]> = MarkedQuestionsService.getAllQuestions();

    markedQuestions.then((questions) => {
      var questionSet: Questions[] = [];
      if (questions !== null) {
        for (var id of questions) {
          var myQuestions = QuestionService.getQuestionById(id, 0);
          questionSet.push(myQuestions);
        }
      }
      this.setState({ loading: false, questions: questionSet });
    });
  }


  renderFlatList = () => {
    const { questions } = this.state;
    
    return (
      <FlatList
        style={styles.list}
        contentContainerStyle={styles.listContainer}
        data={questions}
        horizontal={false}
        numColumns={2}
        keyExtractor={(item) => {
          return generateRandomCode();
        }}
        ItemSeparatorComponent={() => {
          return <View style={styles.separator} />;
        }}
        renderItem={(question) => {
          const item = question.item;
          return (
            <TouchableOpacity
              style={styles.card}
              onPress={() =>
                this.props.navigation.navigate("Marked", { question: item })
              }
            >
              <View style={styles.cardHeader}>
                  <Text style={styles.price}>#{item.id}</Text>       
                  <Text style={styles.title}>{getCategory(item.category).title}</Text>        
 
              </View>
            </TouchableOpacity>
          );
        }}
      />
    );
  };

  renderEmptyItem = () => {
    return (
     
        <View style={empty.content}>
          <Text style={empty.title}>No Marked Questions</Text>
          <MaterialCommunityIcons
            name="emoticon-wink-outline"
            style={empty.iconStyle} 
            size={100}
            color={appColors.white}
          />          
        </View>
    );
  };

  render() {
    const { questions, loading } = this.state;
    if(loading){
      return (
        <GradientContainer>
          <View style={styles.dotsContainer}>
          <LoadingDots
              size={scale(35)}
              colors={[appColors.white, appColors.white, appColors.white]}
              dots={3}
              borderRadius={scale(15)}/>
          </View>
        </GradientContainer>
      )
    }
    return (
      <GradientContainer>
        <View style={styles.whiteBack}>
          <AppBar
            transparent={true}
            titleStyle={{ fontFamily: "AmorriaBrush", fontSize:scale(25)}}
            contentContainerStyle={{ marginTop:scale(35) }} 
            title="Marked"
            trailing={ props => (
              <IconButton
                onPress={() => this.cleanMarkedQuestions()}
                icon={props => <MaterialCommunityIcons name="trash-can" size={props.size+10} color={"white"} />}
                {...props}
              />
            )}
            leading={() => (
              <IconButton
                icon={(props) => (
                  <FontAwesome name="bookmark" color={"white"} size={30} />
                )}
              />
            )}
          />
          <View style={styles.container}>
          { 
            questions.length === 0 
          ? 
            this.renderEmptyItem()
           : 
            this.renderFlatList()
          }
        </View>
        </View>
        <View style={styles.ads}>
          <BannerAd
            unitId={adUnitId}
            size={BannerAdSize.INLINE_ADAPTIVE_BANNER}
            requestOptions={{
              requestNonPersonalizedAdsOnly: false,
            }}
          />
        </View>
      </GradientContainer>
    );
  }
}
const empty = StyleSheet.create({
  content: {
    marginTop:'30%',
    flex:4,
    justifyContent:'center',
    alignItems:'center',
  },
  title: {
    fontSize:scale(28),
    padding:5,
    color:appColors.white,
    fontFamily:'AmorriaBrush',
  },
  iconStyle:{
    marginTop:scale(5),
    padding:scale(10),
  },
});

const styles = StyleSheet.create({
  whiteBack: {
    height: '90%',
    paddingBottom: '15%',
  },
  list: {
    paddingHorizontal: 5,
  },
  listContainer: {
    alignItems: "center",
  },
  separator: {
    marginTop: 10,
  },
  /******** card **************/
  card: {
    shadowColor: "#00000021",
    shadowOffset: {
      width: 2,
    },
    shadowOpacity: 0.5,
    shadowRadius: 4,
    marginVertical: 8,
    backgroundColor: "white",
    flexBasis: "47%",
    marginHorizontal: 5,
    borderRadius:100,
  },
  cardHeader: {
    paddingVertical:50,
    borderTopLeftRadius: 1,
    borderTopRightRadius: 1,
    textAlign:"center",
    justifyContent: "space-between",
  },
    dotsContainer: {
      marginHorizontal: scale(40),
      marginTop: verticalScale(190),
      width: '40%',
      alignContent: 'center',
      alignSelf: 'center',
      height: '15%',
      justifyContent: 'center',
  },
  /******** card components **************/
  title: {
    fontSize: 18,
    flex: 1,
    fontFamily: "LatoLight",
    textAlign:'center'
  },
  price: {
    fontSize: 25,
    fontFamily: "AmorriaBrush",
    color: "black",
    textAlign:'center'
  },
  container: {
    paddingVertical: scale(12),
    flexDirection: 'row',
    alignItems: 'flex-start',
},
  ads: {
    maxHeight: "10%",
    width: "100%",
  },
});

export default ListMarked;
