{"name": "java-1z0-808", "version": "6.3.2", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start -c", "dev-start": "npx expo start --dev-client", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-material/core": "^1.3.7", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.1", "@rivascva/react-native-code-editor": "^1.2.2", "@types/react": "~19.0.10", "axios": "^1.9.0", "expo": "^53.0.15", "expo-av": "~15.1.7", "expo-dev-client": "~5.2.2", "expo-font": "~13.3.2", "expo-linear-gradient": "~14.1.5", "expo-video": "~2.2.2", "qs": "^6.14.0", "react": "19.0.0", "react-native": "0.79.4", "react-native-bouncy-checkbox": "^4.0.1", "react-native-dropdown-picker": "^5.4.6", "react-native-gesture-handler": "~2.24.0", "react-native-google-mobile-ads": "^14.8.1", "react-native-loading-dots": "^1.3.5", "react-native-modal": "^13.0.1", "react-native-pie-chart": "^3.0.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-size-matters": "^0.4.2", "react-native-svg": "15.11.2", "react-native-svg-transformer": "^1.5.0", "react-native-typewriter": "^0.7.0", "react-native-vector-icons": "^10.1.0"}, "devDependencies": {"@babel/core": "^7.24.8", "typescript": "~5.8.3"}, "private": true}