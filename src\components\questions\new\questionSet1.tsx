import {Questions} from "../../models/Questions";
import {
    task300,
    task301,
    task302,
    task303,
    task304,
    task305,
    task306,
    task307,
    task308,
    task309,
    task310,
    task311,
    task312,
    task313,
    task314,
    task315,
    task316,
    task317,
    task318,
    task319,
    task320,
    task321,
    task322,
    task323,
    task324,
    task325,
    task328,
    task329,
    task331,
    task332
} from "./snippets/zenPart1";

export const newSetPart1 : Questions[] = [
    {
        id: 300,
        category: 1,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task300,
        answers: [
            {
                id: 1,
                content: "10",
                correct: false
            }, {
                id: 2,
                content: "11",
                correct: false
            }, {
                id: 3,
                content: "12",
                correct: false
            }, {
                id: 4,
                content: "13",
                correct: true
            }
        ],
        explanation: "The expression evaluates as follows:\n  - Initial x = 5\n  - x++ (post-increment" +
                ") returns 5 but increments x to 6\n  - ++x (pre-increment) increments x to 7 bef" +
                "ore returning 7\n  - y = 5 + 7 = 13\nThus, the output is 13."
    }, {
        id: 301,
        category: 5,
        isImage: false,
        title: "How many times will the loop execute?",
        image: task301,
        answers: [
            {
                id: 1,
                content: "4",
                correct: false
            }, {
                id: 2,
                content: "5",
                correct: true
            }, {
                id: 3,
                content: "6",
                correct: false
            }, {
                id: 4,
                content: "Infinite loop",
                correct: false
            }
        ],
        explanation: "The loop runs from i = 0 to i < 5, incrementing i each time, which means it exec" +
                "utes exactly 5 times."
    }, {
        id: 302,
        category: 2,
        isImage: false,
        title: "What will be the output of the following code?",
        image: task302,
        answers: [
            {
                id: 1,
                content: "127",
                correct: false
            }, {
                id: 2,
                content: "128",
                correct: false
            }, {
                id: 3,
                content: "-128",
                correct: true
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "In Java, a byte has a range from -128 to 127. When 127 is incremented, it overfl" +
                "ows and wraps around to -128 due to Java's two's complement representation."
    }, {
        id: 303,
        category: 8,
        isImage: false,
        title: "What will be the output of the following code?",
        image: task303,
        answers: [
            {
                id: 1,
                content: "Caught: NullPointerException",
                correct: false
            }, {
                id: 2,
                content: "Caught: Custom message",
                correct: true
            }, {
                id: 3,
                content: "Finally executed",
                correct: true
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "The try block explicitly throws a NullPointerException with a custom message. Th" +
                "e catch block handles it and prints the message. The finally block always execut" +
                "es, ensuring 'Finally executed' is printed."
    }, {
        id: 304,
        category: 6,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task304,
        answers: [
            {
                id: 1,
                content: "Integer: 5 Double: 5.5 String: Hello",
                correct: true
            }, {
                id: 2,
                content: "Integer: 5.5 Double: 5 String: Hello",
                correct: false
            }, {
                id: 3,
                content: "Compilation error due to ambiguous method calls",
                correct: false
            }, {
                id: 4,
                content: "Runtime error",
                correct: false
            }
        ],
        explanation: "This program demonstrates method overloading. The compiler determines the approp" +
                "riate method based on the argument type. The calls resolve as:\n  - display(5) →" +
                " calls display(int)\n  - display(5.5) → calls display(double)\n  - display(\"Hel" +
                "lo\") → calls display(String)\nThus, the output is:\nInteger: 5\nDouble: 5.5\nSt" +
                "ring: Hello."
    }, {
        id: 305,
        category: 7,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task305,
        answers: [
            {
                id: 1,
                content: "Parent class method\nChild class method",
                correct: true
            }, {
                id: 2,
                content: "Parent class method\nParent class method",
                correct: false
            }, {
                id: 3,
                content: "Child class method\nChild class method",
                correct: false
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "This example demonstrates method overriding and runtime polymorphism. \n - obj1 " +
                "is of type Parent and calls Parent's show() method.\n  - obj2 is a Parent refere" +
                "nce but holds a Child object, so the overridden show() method in Child is execut" +
                "ed (dynamic method dispatch). \nThus, the output is:\nParent class method\nChild" +
                " class method."
    }, {
        id: 306,
        category: 4,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task306,
        answers: [
            {
                id: 1,
                content: "20",
                correct: true
            }, {
                id: 2,
                content: "24",
                correct: false
            }, {
                id: 3,
                content: "18",
                correct: false
            }, {
                id: 4,
                content: "30",
                correct: false
            }
        ],
        explanation: "This program loops through a 2D array and adds only the even numbers to the sum." +
                " The even numbers in the array are 2, 4, 6, and 8. Therefore, the sum is 2 + 4 +" +
                " 6 + 8 = 20."
    }, {
        id: 307,
        category: 9,
        isImage: false,
        title: "What will be the output of the following Java program using lambda expressions?",
        image: task307,
        answers: [
            {
                id: 1,
                content: "John, Jane, Alex, Sophia",
                correct: false
            }, {
                id: 2,
                content: "John, Jane, Sophia",
                correct: false
            }, {
                id: 3,
                content: "John, Alex",
                correct: false
            }, {
                id: 4,
                content: "Sophia",
                correct: true
            }
        ],
        explanation: "The lambda expression filters the list and prints only the names with more than " +
                "4 characters. Therefore, the output will be just 'Sophia'."
    }, {
        id: 308,
        category: 10,
        isImage: false,
        title: "What will be the output of the following Java program using Date/Time API?",
        image: task308,
        answers: [
            {
                id: 1,
                content: "Date: 2025-03-04\nYear: 2025\nMonth: MARCH\nHours: 3",
                correct: false
            }, {
                id: 2,
                content: "Date: 2025-03-04\nYear: 2024\nMonth: MARCH\nHours: 12",
                correct: false
            }, {
                id: 3,
                content: "Date: 2025-03-04\nYear: 2025\nMonth: MARCH\nHours: 0",
                correct: false
            }, {
                id: 4,
                content: "Compilation error",
                correct: true
            }
        ],
        explanation: "This code throws compilation error since getHours() is undefined for the type Lo" +
                "calDate"
    }, {
        id: 309,
        category: 2,
        isImage: false,
        title: "What will be the output of the following Java program involving auto-boxing, typ" +
                "e casting, and unboxing?",
        image: task309,
        answers: [
            {
                id: 1,
                content: "a: 12\nb: 7.5\nc: 19.5",
                correct: true
            }, {
                id: 2,
                content: "a: 12\nb: 7.5\nc: 20.0",
                correct: false
            }, {
                id: 3,
                content: "a: 10\nb: 7.5\nc: 17.5",
                correct: false
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "In this program, auto-boxing and unboxing come into play with wrapper classes (`" +
                "Integer` and `Double`).\n  - `a = (int) (b + a);` performs unboxing of `a` and `" +
                "b`, adds them, and casts the result back to `Integer`, which gives 12.\n  - `c =" +
                " (double) (a + b);` adds the newly casted value of `a` (12) with `b` (7.5) and a" +
                "ssigns it to `c` as a `Double`, resulting in 19.5.\nThus, the output is `a: 12`," +
                " `b: 7.5`, and `c: 19.5`."
    }, {
        "id": 310,
        "category": 7,
        "isImage": false,
        "title": "Which of the following statements are true?",
        "image": task310,
        "answers": [
            {
                "id": 1,
                "content": "The code will fail to compile because a Dog object cannot be assigned to an Anim" +
                        "al reference.",
                "correct": false
            }, {
                "id": 2,
                "content": "The code will compile and run successfully, printing 'Animal is eating'.",
                "correct": true
            }, {
                "id": 3,
                "content": "Uncommenting the line 'myDog.bark()' will cause a compilation error.",
                "correct": true
            }, {
                "id": 4,
                "content": "The code will compile and run successfully, printing 'Dog is barking'.",
                "correct": false
            }
        ],
        "explanation": "Key points:\n1. A Dog object can be assigned to an Animal reference (polymorphis" +
                "m).\n2. The Animal reference can only access methods defined in the Animal class" +
                ", even if the object is of type Dog.\n3. Uncommenting 'myDog.bark()' causes a co" +
                "mpilation error because 'bark()' is not defined in the Animal class.\n4. The Dog" +
                " class inherits the eat() method from the Animal class, so calling 'myDog.eat()'" +
                " works."
    }, {
        "id": 311,
        "category": 2,
        "isImage": false,
        "title": "What will be the output of the following code?",
        "image": task311,
        "answers": [
            {
                "id": 1,
                "content": "15.5",
                "correct": true
            }, {
                "id": 2,
                "content": "25.0",
                "correct": true
            }, {
                "id": 3,
                "content": "true",
                "correct": true
            }, {
                "id": 4,
                "content": "A compilation error will occur.",
                "correct": false
            }
        ],
        "explanation": "Key points:\n1. 'a + b' results in 15.5 because 'int' is promoted to 'double' du" +
                "ring addition.\n2. 'c + a' results in 25.0 because 'float' is promoted to 'int' " +
                "during addition.\n3. 'd' prints 'true' as it is a boolean value.\n4. No compilat" +
                "ion error occurs as all operations are valid."
    }, {
        id: 312,
        category: 3,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task312,
        answers: [
            {
                id: 1,
                content: "x = 11\ny = 4\nz = 28",
                correct: false
            }, {
                id: 2,
                content: "x = 12\ny = 3\nz = 30",
                correct: true
            }, {
                id: 3,
                content: "x = 12\ny = 3\nz = 28",
                correct: false
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "Key points:\n1. 'x++' uses the current value of x (10) and then increments x to " +
                "11.\n2. '--y' decrements y to 4 and uses the new value (4).\n3. '++x' increments" +
                " x to 12 and uses the new value (12).\n4. 'y--' uses the current value of y (4) " +
                "and then decrements y to 3.\n5. z = 10 (x++) + 4 (--y) + 12 (++x) + 4 (y--) = 30" +
                ".\n6. Final values: x = 12, y = 3, z = 30."
    }, {
        id: 313,
        category: 8,
        isImage: false,
        title: "What will be the final output of the following Java program?",
        image: task313,
        answers: [
            {
                id: 5,
                content: "3",
                correct: false
            }, {
                id: 1,
                content: "Caught an ArrayIndexOutOfBoundsException\nFinally block executed",
                correct: true
            }, {
                id: 2,
                content: "Caught a generic Exception\nFinally block executed",
                correct: false
            }, {
                id: 3,
                content: "Finally block executed",
                correct: false
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "Key points:\n1. Accessing numbers[3] throws an ArrayIndexOutOfBoundsException be" +
                "cause the array has only 3 elements.\n2. The exception is caught by the first ca" +
                "tch block, which prints 'Caught an ArrayIndexOutOfBoundsException'.\n3. The fina" +
                "lly block is always executed, printing 'Finally block executed'.\n4. There is no" +
                " compilation error in the code."
    }, {
        id: 314,
        category: 1,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task314,
        answers: [
            {
                id: 1,
                content: "40",
                correct: false
            }, {
                id: 2,
                content: "50",
                correct: false
            }, {
                id: 3,
                content: "60",
                correct: true
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "Key points:\n1. There are two variables named 'x': a static variable (ScopeExamp" +
                "le.x = 10) and a local variable in main (x = 20).\n2. In the main method, 'x' re" +
                "fers to the local variable (20), and 'ScopeExample.x' refers to the static varia" +
                "ble (10).\n3. The method m1(x) is called with the local variable x (20), and it " +
                "returns x + ScopeExample.x (20 + 10 = 30).\n4. The final output is 20 (local x) " +
                "+ 10 (static x) + 30 (m1 result) = 60."
    }, {
        id: 315,
        category: 10,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task315,
        answers: [
            {
                id: 1,
                content: "Formatted Date and Time: 04-03-2025 03:30 PM",
                correct: false
            }, {
                id: 2,
                content: "Formatted Date and Time: 04-03-2025 15:30",
                correct: true
            }, {
                id: 3,
                content: "Formatted Date and Time: 2025-03-04 15:30",
                correct: false
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "Key points:\n1. The LocalDate object is created with the date 2025-03-04.\n2. Th" +
                "e LocalTime object is created with the time 15:30.\n3. The DateTimeFormatter is " +
                "defined with the pattern 'dd-MM-yyyy HH:mm'.\n4. The atTime() method combines th" +
                "e LocalDate and LocalTime into a LocalDateTime object.\n5. The format() method a" +
                "pplies the formatter to the LocalDateTime, resulting in '04-03-2025 15:30'.\n6. " +
                "There is no compilation error in the code."
    }, {
        id: 316,
        category: 7,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task316,
        answers: [
            {
                id: 1,
                content: "Dog is fetching",
                correct: false
            }, {
                id: 2,
                content: "Animal is making a sound",
                correct: false
            }, {
                id: 3,
                content: "Dog is barking",
                correct: true
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "Key points:\n1. The Dog class overrides the makeSound() method from the Animal c" +
                "lass.\n2. The reference type is Animal, but the object type is Dog, so the overr" +
                "idden method in Dog is called (polymorphism).\n3. The output is 'Dog is barking'" +
                " because myDog.makeSound() calls the overridden method in Dog.\n4. Uncommenting " +
                "myDog.fetch() would cause a compilation error because fetch() is not defined in " +
                "the Animal class.\n5. There is no compilation error in the current code."
    }, {
        id: 317,
        category: 4,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task317,
        answers: [
            {
                id: 1,
                content: "Original Array: [1, 2, 3, 4, 5]\nReversed Copy: [5, 4, 3, 2, 1]",
                correct: true
            }, {
                id: 2,
                content: "Original Array: [1, 2, 3, 4, 5]\nReversed Copy: [1, 2, 3, 4, 5]",
                correct: false
            }, {
                id: 3,
                content: "Original Array: [5, 4, 3, 2, 1]\nReversed Copy: [1, 2, 3, 4, 5]",
                correct: false
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "Key points:\n1. The original array 'numbers' is initialized with values [1, 2, 3" +
                ", 4, 5].\n2. A new array 'copy' is created with the same length as 'numbers'.\n3" +
                ". The for loop iterates through 'numbers' and assigns values to 'copy' in revers" +
                "e order.\n4. The original array remains unchanged, and the reversed copy is [5, " +
                "4, 3, 2, 1].\n5. There is no compilation error in the code."
    }, {
        id: 318,
        category: 9,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task318,
        answers: [
            {
                id: 1,
                content: "Alice\nCharlie",
                correct: false
            }, {
                id: 2,
                content: "Alice\nChuck",
                correct: false
            }, {
                id: 3,
                content: "Chuck\nCharlie",
                correct: false
            }, {
                id: 4,
                content: "Alice\nCharlie\nChuck",
                correct: true
            }, {
                id: 5,
                content: "Alice\nBob\nCharlie\nDavid",
                correct: false
            }, {
                id: 6,
                content: "No output",
                correct: false
            }
        ],
        explanation: "Key points:\n1. The list 'names' contains four elements: ['Alice', 'Bob', 'Charl" +
                "ie', 'David', 'Chuck'].\n2. The stream() method is called on the list to create " +
                "a stream.\n3. The filter() method uses a lambda expression to filter names start" +
                "ing with 'C' or ending with 'e'.\n4. The forEach() method prints each filtered e" +
                "lement using a method reference (System.out::println).\n5. Both 'Charlie' and 'C" +
                "huck' starts with 'C', plus 'Alice' ends with 'e', so it is printed.\n6. There i" +
                "s no compilation error in the code."
    }, {
        id: 319,
        category: 3,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task319,
        answers: [
            {
                id: 1,
                content: "x: 12\ny: 4\nz: 1",
                correct: false
            }, {
                id: 2,
                content: "x: 13\ny: 3\nz: 1",
                correct: false
            }, {
                id: 3,
                content: "x: 13\ny: 4\nz: 1",
                correct: true
            }, {
                id: 4,
                content: "x: 13\ny: 3\nz: 2",
                correct: false
            }, {
                id: 5,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "Key points:\n1. The expression 'x += y++ * --z + x / z' is evaluated as follows:" +
                "\n   - 'y++' uses the current value of y (3) and then increments y to 4.\n   - '" +
                "--z' decrements z to 1 and uses the new value (1).\n   - 'y++ * --z' evaluates t" +
                "o 3 * 1 = 3.\n   - 'x / z' evaluates to 5 / 1 = 5 (integer division).\n   - The " +
                "sum is 3 + 5 = 8.\n   - 'x += 8' is equivalent to x = x + 8, so x becomes 5 + 8 " +
                "= 13.\n2. The final values are:\n   - x = 13\n   - y = 4\n   - z = 1.\n3. There " +
                "is no compilation error in the code."
    }, {
        id: 320,
        category: 4,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task320,
        answers: [
            {
                id: 1,
                content: "Modified List: [1, 3]",
                correct: false
            }, {
                id: 2,
                content: "Modified List: [2, 6]",
                correct: true
            }, {
                id: 3,
                content: "Modified List: [4, 8]",
                correct: false
            }, {
                id: 4,
                content: "Modified List: [2, 4, 6, 8]",
                correct: false
            }
        ],
        explanation: "Key points:\n1. The ArrayList 'numbers' is initialized with values [1, 2, 3, 4]." +
                "\n2. The removeIf() method removes all even numbers, leaving [1, 3].\n3. The rep" +
                "laceAll() method multiplies each remaining element by 2, resulting in [2, 6].\n4" +
                ". The output is 'Modified List: [2, 6]'.\n5. There is no compilation error in th" +
                "e code."
    }, {
        id: 321,
        category: 5,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task321,
        answers: [
            {
                id: 1,
                content: "Sum: 24",
                correct: false
            }, {
                id: 2,
                content: "Sum: 18",
                correct: false
            }, {
                id: 3,
                content: "Sum: 12",
                correct: true
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "Key points:\n1. The outer loop runs for i = 1, 2, 3.\n2. The inner loop runs for" +
                " j = 1, 2, 3.\n3. When i == j, the continue outer statement skips the rest of th" +
                "e inner loop and moves to the next iteration of the outer loop.\n4. The sum is c" +
                "alculated as follows:\n   - For i=1, j=2: sum += 1 + 2 = 3\n   - For i=1, j=3: s" +
                "um += 1 + 3 = 4\n   - For i=2, j=1: sum += 2 + 1 = 3\n   - For i=2, j=3: sum += " +
                "2 + 3 = 5\n   - For i=3, j=1: sum += 3 + 1 = 4\n   - For i=3, j=2: sum += 3 + 2 " +
                "= 5\n5. The total sum is 3 + 4 + 3 + 5 + 4 + 5 = 24.\n6. There is no compilation" +
                " error in the code."
    }, {
        id: 322,
        category: 6,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task322,
        answers: [
            {
                id: 1,
                content: "Factorial: 1",
                correct: false
            }, {
                id: 2,
                content: "Factorial: 2",
                correct: false
            }, {
                id: 3,
                content: "Factorial: 3",
                correct: false
            }, {
                id: 4,
                content: "Compilation error",
                correct: true
            }
        ],
        explanation: "The method factorial(int n) is an instance method, but you are calling it direct" +
                "ly from the main method, which is static. Since main is a static method, it cann" +
                "ot directly call an instance method (factorial) without creating an instance of " +
                "the class, leading to compilation error."
    }, {
        id: 323,
        category: 7,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task323,
        answers: [
            {
                id: 1,
                content: "Vehicle is starting\nCar is driving",
                correct: false
            }, {
                id: 2,
                content: "Car is starting\nCar is driving",
                correct: true
            }, {
                id: 3,
                content: "Vehicle is starting",
                correct: false
            }, {
                id: 4,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "Key points:\n1. The Car class overrides the start() method from the Vehicle clas" +
                "s.\n2. The reference type is Vehicle, but the object type is Car, so the overrid" +
                "den method in Car is called (polymorphism).\n3. The output is 'Car is starting' " +
                "because myCar.start() calls the overridden method in Car.\n4. Explicit casting (" +
                "(Car) myCar) allows calling the drive() method, which is specific to the Car cla" +
                "ss.\n5. There is no compilation error in the code."
    }, {
        id: 324,
        category: 8,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task324,
        answers: [
            {
                id: 1,
                content: "Caught Exception\nFinally block executed",
                correct: false
            }, {
                id: 2,
                content: "Result: 0\nFinally block executed",
                correct: false
            }, {
                id: 3,
                content: "Caught ArithmeticException\nFinally block executed",
                correct: true
            }, {
                id: 4,
                content: "Finally block executed",
                correct: false
            }, {
                id: 5,
                content: "Compilation error",
                correct: false
            }
        ],
        explanation: "Key points:\n1. The divide() method throws an ArithmeticException when dividing " +
                "by zero.\n2. The exception is caught by the catch block, which prints 'Caught Ar" +
                "ithmeticException'.\n3. The finally block is always executed, printing 'Finally " +
                "block executed'.\n4. Thrown new exception is never reached therefore Caught Exce" +
                "ption isnot printed \n5. There is no compilation error in the code."
    }, {
        id: 325,
        category: 9,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task325,
        answers: [
            {
                id: 1,
                content: "Sum: 9",
                correct: true
            }, {
                id: 2,
                content: "Sum: 12",
                correct: false
            }, {
                id: 3,
                content: "Sum: 15",
                correct: false
            }, {
                id: 4,
                content: "Sum: 18",
                correct: true
            }, {
                id: 5,
                content: "Sum: 21",
                correct: false
            }
        ],
        explanation: "Key points:\n1. The list 'numbers' contains five elements: [1, 2, 3, 4, 5].\n2. " +
                "The stream() method is called on the list to create a stream.\n3. The filter() m" +
                "ethod uses a lambda expression to filter even numbers (n % 2 == 1), leaving [1, " +
                "3, 5].\n4. The mapToInt() method converts the stream to an IntStream also multip" +
                "lying each element to 2 leaving [2, 6, 10].\n5. The sum() method calculates the " +
                "sum of the filtered even numbers: 2 + 6 + 10 = 18."
    }, {
        id: 326,
        category: 3,
        isImage: false,
        title: "Which statement is true about the switch statement?",
        answers: [
            {
                id: 1,
                content: "It must contain the default section.",
                correct: false
            }, {
                id: 2,
                content: "The break statement, at the end of each case block, is optional.",
                correct: true
            }, {
                id: 3,
                content: "Its case label literals can be changed at runtime.",
                correct: false
            }, {
                id: 4,
                content: "Its expression must evaluate to a collection of values.",
                correct: false
            }
        ],
        explanation: "Key points:\n1. The Default statement is optional in Switch.\n2.Case label liter" +
                "als must be final constant values "
    }, {
        id: 327,
        category: 8,
        isImage: false,
        title: "What is the name of the Java concept that uses access modifiers to protect varia" +
                "bles and hide them within a class?",
        answers: [
            {
                id: 1,
                content: "Encapsulation ",
                correct: true
            }, {
                id: 2,
                content: "Inheritance",
                correct: false
            }, {
                id: 3,
                content: "Abstraction",
                correct: false
            }, {
                id: 4,
                content: "Instantiation",
                correct: false
            }, {
                id: 5,
                content: "Polymorphism",
                correct: false
            }
        ],
        explanation: "The Java concept that uses access modifiers to protect variables and hide them w" +
                "ithin a class is called Encapsulation."
    }, {
        id: 328,
        category: 2,
        isImage: false,
        title: "Given the code fragment, what is the output?",
        image: task328,
        answers: [
            {
                id: 1,
                content: "Sum is 600",
                correct: false
            }, {
                id: 2,
                content: "Compilation fails at line n1",
                correct: false
            }, {
                id: 3,
                content: "Compilation fails at line n2.",
                correct: true
            }, {
                id: 4,
                content: "ClassCastException is thrown at line n1.",
                correct: false
            }, {
                id: 5,
                content: "ClassCastException is thrown at line n2.",
                correct: false
            }
        ],
        explanation: "This is a casting issue but the code fails to compile so the answer is c. The Cl" +
                "assCastException is a runtime exception where you 're trying to cast an object t" +
                "o a different object type, but here the code fails to compile because you cannot" +
                " cast a long to a string. Basically we cannot cast from long to string."

    }, {
        id: 329,
        category: 4,
        isImage: false,
        title: "Given the code fragment, what is the result?",
        image: task329,
        answers: [
            {
                id: 1,
                content: "Same",
                correct: true
            }, {
                id: 2,
                content: "Not Same",
                correct: false
            }
        ],
        explanation: "The Arrays.equals() method compares the contents of two arrays, not just their r" +
                "eferences. In this case, both arr1 and arr2 contain the same values {1, 2, 3}. T" +
                "herefore, Arrays.equals(arr1, arr2) returns true, and the program prints Same."
    }, {
        id: 330,
        category: 5,
        isImage: false,
        title: "Given the code fragment, How many times 'Hello' is printed?",
        image: task329,
        answers: [
            {
                id: 1,
                content: "Same",
                correct: false
            }, {
                id: 2,
                content: "Not Same",
                correct: false
            }, {
                id: 3,
                content: "Compilation fails",
                correct: true
            }, {
                id: 4,
                content: "Exception is thrown",
                correct: false
            }
        ],
        explanation: "The code fails because the variable i is modified outside the for loop, where it" +
                "'s not accessible, as it is declared within the loop's scope."
    }, {
        id: 331,
        category: 6,
        isImage: false,
        title: "What will be the output of the following Java program?",
        image: task331,
        answers: [
            {
                id: 1,
                content: "Result 1: 5\nResult 2: 8\nResult 3: 16",
                correct: false
            }, {
                id: 2,
                content: "Result 1: 2\nResult 2: 5\nResult 3: 24",
                correct: false
            }, {
                id: 3,
                content: "Result 1: 2\nResult 2: 4\nResult 3: 12",
                correct: true
            }, {
                id: 4,
                content: "Result 1: 1\nResult 2: 4\nResult 3: 12",
                correct: false
            }
        ],
        explanation: "Key points:\n1. The method calculate() is overloaded with three different parame" +
                "ter lists.\n2. calculate(int a) multiplies the input by 2: calculate(1) returns " +
                "2.\n3. calculate(int a, int b) calls calculate(int a) and adds b: calculate(1, 2" +
                ") returns 2 + 2 = 4.\n4. calculate(int a, int b, int c) calls calculate(int a, i" +
                "nt b) and multiplies by c: calculate(1, 2, 3) returns 4 * 2 = 12.\n5. The output" +
                " is:\n   - Result 1: 2\n   - Result 2: 4\n   - Result 3: 12.\n6. There is no com" +
                "pilation error in the code."
    }, {
        id: 332,
        category: 4,
        isImage: false,
        title: "Given the code fragment, How many times 'Hello' is printed?",
        image: task332,
        answers: [
            {
                id: 1,
                content: "Compilation error",
                correct: false
            }, {
                id: 2,
                content: "2",
                correct: false
            }, {
                id: 3,
                content: "1",
                correct: false
            }, {
                id: 4,
                content: "0",
                correct: true
            }
        ],
        explanation: "Nothing is printed because loop will not run; 0>5 is false condition"
    }, {
        id: 333,
        category: 10,
        isImage: false,
        title: "How do you create a LocalDate object representing the current date?",
        answers: [
            {
                id: 1,
                content: "LocalDate today = LocalDate();",
                correct: false
            }, {
                id: 2,
                content: "LocalDate today = new LocalDate();",
                correct: false
            }, {
                id: 3,
                content: "LocalDate today = LocalDate.now();",
                correct: true
            }, {
                id: 4,
                content: "LocalDate today = LocalDate.current();",
                correct: false
            }
        ],
        explanation: "The correct answer is: LocalDate today = LocalDate.now();. The now() static meth" +
                "od of the LocalDate class returns a LocalDate object representing the current da" +
                "te based on the system clock."
    }, {
        id: 334,
        category: 10,
        isImage: false,
        title: "How do you add 7 days to a LocalDate object?",
        answers: [
            {
                id: 1,
                content: "date.plusDays(7)",
                correct: true
            }, {
                id: 2,
                content: "date + 7",
                correct: false
            }, {
                id: 3,
                content: "date.addDays(7)",
                correct: false
            }, {
                id: 4,
                content: "date.increaseDays(7)",
                correct: false
            }
        ],
        explanation: "The correct answer is: date.plusDays(7). The plusDays() method is used to add a " +
                "specified number of days to a LocalDate object and returns a new LocalDate with " +
                "the updated date."
    }, {
        id: 335,
        category: 8,
        isImage: false,
        title: "Which of the following statements is true about exception",
        answers: [
            {
                id: 1,
                content: "A try block can have many catch blocks and many finally blocks",
                correct: false
            }, {
                id: 2,
                content: "A try block can have many catch blocks but only one finally block",
                correct: true
            }, {
                id: 3,
                content: "A try block must have one finally block for each catch block",
                correct: false
            }, {
                id: 4,
                content: "A try block must have at least one catch block to have a finally block",
                correct: false
            }
        ],
        explanation: "A try block can only have one finally block. However, multiple catches are allow" +
                "ed. It is even allowable to have no catch blocks and only a finally block."
    }
];