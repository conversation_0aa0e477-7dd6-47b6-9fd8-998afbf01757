import React, {Component} from "react";
import {View, Text, StyleSheet} from "react-native";

import {<PERSON><PERSON><PERSON>, Button, TextInput, Snackbar} from "@react-native-material/core";
import {Entypo, MaterialCommunityIcons} from "@expo/vector-icons";
import {Video, ResizeMode} from 'expo-av';
import { Questions } from "../../components/models/Questions";
import MailgunService from "../../service/email/MailgunService";
import { appColors } from "../../utils/appColors";
import { scale } from "react-native-size-matters";
import { createVideoPlayer, VideoPlayer, VideoView } from 'expo-video';

const videoSource = '../../media/bug.mp4';

interface ReportI {
    userReport : string;
    sent:boolean;
}

interface CustomModalWindowProps {
    closeModal?: () => void;
    currentQuestion : Questions;
}


class ReportQuestion extends Component < CustomModalWindowProps, ReportI > {
    private player: VideoPlayer;
    constructor(props) {
        super(props);
        this.state = {
            userReport: "",
            sent:false,
        };
        this.player = createVideoPlayer(videoSource);
    }

    closeModal = () => {
        this.props.closeModal();
    };

    componentDidMount(): void {
        this.player.loop = true;
        this.player.muted = true;
        this.player.play();
    }

    componentWillUnmount() {
        this.player.release(); 
    }

    sendEmail = async() => {
        const {currentQuestion} = this.props;
        const {userReport} = this.state;
        const subject = "Question #" + currentQuestion.id;
        const body = "Response from user: " + userReport;
        await MailgunService.sendSimpleMessage(subject, body);
        this.setState({sent:true})
    };

    render() {
        const {currentQuestion} = this.props;
        const {sent} = this.state;
        return (
            <View style={styles.container}>
                <VideoView
                    style={styles.imgBackground}
                    player={this.player}
                    />
                <View style={styles.form}>
                    <View style={styles.header}>
                        <View style={styles.column}>
                            <Text style={styles.labelHeader}>Report</Text>
                            <Text style={styles.labelMyNumber}>
                                Bug in #{currentQuestion.id} question
                            </Text>
                            <Divider
                                style={{
                                marginVertical: 30,
                                backgroundColor:'white'
                            }}/>
                            {
                                sent
                                ?
                                    <Text style={styles.successFeedback}>Your feedback is submitted</Text>
                                :
                                <TextInput
                                    leading={(props) => (<MaterialCommunityIcons name="bug" color={"#215e9d"} {...props}/>)}
                                    onChangeText={(val) => this.setState({userReport: val})}
                                    style={{
                                        width: "100%"
                                    }}
                                    selectionColor={"#215e9d"}
                                    placeholderTextColor={"#215e9d"}
                                    color="#215e9d"/>
                            }

                        </View>
                    </View>
                    <View style={styles.buttonContainer}>
                        {
                                !sent
                                ?
                                    <Button
                                        title="Send"
                                        style={styles.buttonStyle}
                                        titleStyle={styles.buttonText}
                                        onPress={() => this.sendEmail()}
                                        trailing={(props) => <Entypo name="paper-plane" color={appColors.black} size={scale(props.size)}/>}
                                    />
                                :
                                null
                        }


                        <Button
                            title="Close"
                            style={styles.buttonStyle}
                            titleStyle={styles.buttonText}
                            onPress={() => this.props.closeModal()}
                            trailing={(props) => <MaterialCommunityIcons name="close-box" color={appColors.black} size={scale(props.size)}/>}/>
                    </View>
                </View>

            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        width: '100%',
        backgroundColor: 'black',
        justifyContent: 'center',
        alignItems: 'center'
    },
    imgBackground: {
        height: '100%',
        width: '100%',
        position: 'absolute'
    },
    buttonStyle: {
        backgroundColor: appColors.white,
        fontFamily: "monospace"
    },
    form: {
        width: "100%",
        marginHorizontal: 15,
        paddingVertical: "8%",
        justifyContent: "center",
        alignItems: "center"
    },
    header: {
        flexDirection: "row",
        justifyContent: "space-around"
    },
    column: {
        flexDirection: "column",
        width: "80%"
    },
    labelMyNumber: {
        fontFamily: "LatoRegular",
        fontSize: 18,
        textAlign: "center",
        color:'white'
    },
    successFeedback:{
        fontFamily: "AmorriaBrush",
        fontSize: 22,
        textAlign: "center",
        color:'white'
    },
    labelHeader: {
        fontFamily: "AmorriaBrush",
        fontSize: 22,
        textAlign: "center",
        color:'white'
    },
    buttonContainer: {
        paddingTop: "7%",
        width: "70%",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-around"
    },
    buttonText: {
        fontSize: 14,
        fontFamily:'LatoRegular',
        color:appColors.black
    },
});

export default ReportQuestion;
