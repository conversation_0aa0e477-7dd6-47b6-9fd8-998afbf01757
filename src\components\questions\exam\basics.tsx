import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { task150, task151, task152, task155 } from "./snips/basicSnips";

export const basics : Questions[] = [
    {
        id: 150,
        category: 1,
        isImage: false,
        title: "What variable declarations aren't allowed in Java?",
        image: task150,
        answers: [
            {
                id: 1,
                content: 'A$B',
                correct: false
            }, {
                id: 2,
                content: '_isWrong',
                correct: false
            }, {
                id: 3,
                content: 'false',
                correct: true
            }, {
                id: 4,
                content: 'java.util',
                correct: true
            }, {
                id: 5,
                content: 'Public',
                correct: false
            }, {
                id: 6,
                content: '7_cd',
                correct: true
            }
        ],
        explanation: 'The word "false" is a reserved keyword in Java, used to represent the boolean va' +
                'lue false. Variable names cannot be the same as reserved keywords.\n \njava.util' +
                ' is a package name in Java, and you cannot use it as a variable name.\n \nVariab' +
                'le names cannot contain dots or any special characters.\n \nVariable names canno' +
                't start with a digit. They must start with a letter, underscore, or dollar s' +
                'ign. This criteria marks 7_cd as invalid.'
    }, {
        id: 151,
        category: 1,
        isImage: false,
        title: "What is the correct variable declaration?",
        image: task151,
        answers: [
            {
                id: 1,
                content: '1',
                correct: true
            }, {
                id: 2,
                content: '2',
                correct: false
            }, {
                id: 3,
                content: '3',
                correct: true
            }, {
                id: 4,
                content: '4',
                correct: false
            }, {
                id: 5,
                content: '5',
                correct: false
            }
        ],
        explanation: 'Line 1 -> This line is correct. It initializes a double variable d with the valu' +
                'e 12.0 represented as a floating-point literal (12.f).\n \nLine 2 -> This line i' +
                's incorrect. It attempts to assign a floating-point literal 12.4 to an int varia' +
                'ble in, which is not allowed because it involves narrowing conversion.\n \nLine ' +
                '3 -> This line is correct. It initializes a String variable str with the value H' +
                'ello World.\n \nLine 4 -> This line is incorrect. The length property of a Strin' +
                'g requires parentheses (), like str.length().\n \nLine 5 -> is incorrect. The va' +
                'riable in is declared as an int, and the length() method is not applicable to in' +
                't types. It is only applicable to String objects. This line would result in a co' +
                'mpilation error.\n \nTherefore an answer is 1 and 3'
    }, {
        id: 152,
        category: 1,
        isImage: false,
        title: "What will be printed in console?",
        image: task152,
        answers: [
            {
                id: 1,
                content: 'null / null',
                correct: false
            }, {
                id: 2,
                content: 'null / 0',
                correct: true
            }, {
                id: 3,
                content: ' / 0',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }, {
                id: 5,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'In Java, instance variables of reference types (like String) are automatically i' +
                'nitialized to null, and instance variables of numeric types (like int) are initi' +
                'alized to 0 by default, leading answer into null / 0'
    }, {
        id: 153,
        category: 1,
        isImage: false,
        title: 'What is/are the method name which serves as entry point in any java programm?',
        answers: [
            {
                id: 1,
                content: 'public static void main(String args)',
                correct: false
            }, {
                id: 2,
                content: 'public void main(String[] args)',
                correct: false
            }, {
                id: 3,
                content: 'public static void main()',
                correct: false
            }, {
                id: 4,
                content: 'public static void final(String[] args)',
                correct: false
            }, {
                id: 5,
                content: 'public static void main(String[] args)',
                correct: true
            }
        ],
        explanation: 'In Java, the method that serves as the entry point for any Java program is calle' +
                'd the main method. The main method is where the execution of a Java program begi' +
                'ns. It has a specific signature that allows the Java Virtual Machine (JVM) to id' +
                'entify and invoke it\n \npublic static void main(String[] args)'
    }, {
        id: 154,
        category: 1,
        isImage: false,
        title: 'Choose what is/are true',
        answers: [
            {
                id: 1,
                content: 'Local boolean variable equals to false',
                correct: false
            }, {
                id: 2,
                content: 'Local String variable equals to null',
                correct: false
            }, {
                id: 3,
                content: 'Local Integer variable equals to null',
                correct: false
            }, {
                id: 4,
                content: 'All are false',
                correct: true
            }
        ],
        explanation: 'The initial values of local variables are not guaranteed and can be considered t' +
                'o be undefined.\n \nAttempting to use the value of an uninitialized local variab' +
                'le before assigning a valid value to it will result in a compilation error.'
    }, {
        id: 155,
        category: 1,
        isImage: false,
        title: "What is the ouput of this snippet?",
        image: task155,
        answers: [
            {
                id: 1,
                content: 'Code fails at line 1',
                correct: false
            }, {
                id: 2,
                content: 'Code fails at line 2',
                correct: false
            }, {
                id: 3,
                content: 'Code fails at line 3',
                correct: false
            }, {
                id: 4,
                content: 'Code compiles just fine',
                correct: true
            }, {
                id: 5,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'grading(10125412L) calls the grading method with a long value 10125412L.\n \ngrading' +
                '(0b111110) calls the grading method with a binary value 0b111110, which is equiv' +
                'alent to 62 in decimal.\n \ngrading(0xCAB) calls the grading method with a hexadecim' +
                'al value 0xCAB, which is equivalent to 3243 in decimal.\n \ngrading(7_7_7) calls the' +
                ' grading method with an integer value 777. The underscores in 7_7_7 are used for' +
                ' readability and have no effect on the value.\n \nAs a conclusion this snippet has n' +
                'one compilation errors and no thrown exceptions'
    }, {
        id: 156,
        category: 1,
        isImage: false,
        title: 'Choose what is true about Java',
        answers: [
            {
                id: 1,
                content: 'It is a functional programming language',
                correct: false
            }, {
                id: 2,
                content: 'Has overloading operator',
                correct: false
            }, {
                id: 3,
                content: 'Code which was compiled in java, can be executed in Mac',
                correct: true
            }, {
                id: 4,
                content: 'It is an object oriented language',
                correct: true
            }
        ],
        explanation: 'Compiled java code can be executed at any platform and java is object oriented l' +
                'anguage.'
    }
]