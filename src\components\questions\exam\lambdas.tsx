import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { javaCode231, javaCode232, javaCode233, javaCode234 } from "./snips/basicSnips";

export const lambdas : Questions[] = [
    {
        id: 231,
        category: 9,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode231,
        answers: [
            {
                id: 1,
                content: 'adult',
                correct: true
            }, {
                id: 2,
                content: 'teenager',
                correct: false
            }, {
                id: 3,
                content: 'Compilation error',
                correct: false
            }, {
                id: 4,
                content: 'Exception is thrown at runtime',
                correct: false
            }
        ],
        explanation: 'The provided Java code defines a class named Human with a main method and a stat' +
                'ic helper method check.\n \nInside the main method: An instance of the Human cla' +
                'ss named hm is created. The age field of hm is set to 19.\n \nThe check method i' +
                's called, passing in the hm instance and a lambda expression as arguments.\n \nT' +
                'he check method is a static method that takes a Human object and a Predicate<Hum' +
                'an> as parameters: The pred predicate is used to test whether the given Human ob' +
                'jects age is greater than 18.\n \nDepending on the result of the predicate test,' +
                ' the result variable is assigned either "adult" or "teenager".\n \nIn this case, the' +
                ' lambda expression\n \n(Human h) -> h.age > 18\n \nchecks if the age of the Human object' +
                ' is greater than 18. Since the hm objects age is set to 19, the predicate test r' +
                'eturns true, and therefore, the output will be "adult".'
    }, {
        id: 232,
        category: 9,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode232,

        answers: [
            {
                id: 1,
                content: 'Square',
                correct: false
            }, {
                id: 2,
                content: 'Rectangle',
                correct: false
            }, {
                id: 3,
                content: 'Compilation error at //1',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error at //2',
                correct: true
            }, {
                id: 5,
                content: 'Compilation error at //3',
                correct: false
            }, {
                id: 6,
                content: 'Compilation error at //4',
                correct: false
            }
        ],
        explanation: 'The provided code snippet has a compilation error due to issues related to the l' +
                'ambda expression, type mismatch, and method invocation. The compilation error oc' +
                'curs in this line:\n \ncheck((int height, int width) -> {  return height.equals(widt' +
                'h); }, shape);\n \nThe reasons are as follows: In the lambda expression passed to t' +
                'he check method, you are trying to call the equals method on the height paramete' +
                'r.\n \nHowever, height is of type int, which is a primitive data type, and equals is' +
                ' a method of the Integer class (wrapper class for int). This results in a compil' +
                'ation error.'
    }, {
        id: 233,
        category: 9,
        isImage: false,
        title: "What can be inserted for code to compile?",
        image: javaCode233,
        answers: [
            {
                id: 1,
                content: '(Human h) -> return h.age > 18;',
                correct: false
            }, {
                id: 2,
                content: '(Human h) -> {return h.age > 18 ;}',
                correct: true
            }, {
                id: 3,
                content: ' h -> {h.age > 18}',
                correct: false
            }, {
                id: 4,
                content: ' h -> h.age > 18',
                correct: true
            }, {
                id: 5,
                content: '(Human h) -> h.age > 18',
                correct: true
            }, {
                id: 6,
                content: 'Human h -> h.age > 18',
                correct: false
            }
        ],
        explanation: 'What is allowed (Object o), o, (o),\n \nwhat is not allowed Object o (without bracke' +
                'ts).\n \nWhat is allowed  h.age > 18,{return h.age > 18 ;}.\n \nreturn statement if used' +
                ' has to be inside of brackets and ended by ; -> so return h.age > 18; not allowe' +
                'd since there is no brackets.'
    }, {
        id: 234,
        category: 9,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode234,
        answers: [
            {
                id: 1,
                content: '[3, 7, 5]',
                correct: true
            }, {
                id: 2,
                content: '[12]',
                correct: false
            }, {
                id: 3,
                content: ' [12, 3, 7, 5]',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }
        ],
        explanation: 'The removeIf method is used to remove elements from the list that meet a give' +
                'n condition.\n \nIn this case, a lambda expression (n -> n % 2 == 0) is used as the ' +
                'condition.\n \nThis lambda expression checks if a number is even (n % 2 == 0). If th' +
                'e condition is true, the number is removed from the list. -> [3, 7, 5]'
    }
]