import React, {Component} from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity
} from 'react-native';
import {MaterialCommunityIcons} from '@expo/vector-icons';
import {Video, ResizeMode} from 'expo-av';


interface HelperState {
    newExam : boolean;
    oldExamId : number;
    oldExamTitle : string;
}

interface CustomModalWindowProps {
    closeModal?: () => void;
    percentage : number;
}


class Procents extends Component < CustomModalWindowProps, HelperState > {

    constructor(props) {
        super(props);
        this.state = {
            newExam: true,
            oldExamId: 0,
            oldExamTitle: '',
        };
    }

    componentDidMount(): void {

    }

    closeModal = () => {
        this.props.closeModal();
    }

    render() {
        const {percentage} = this.props;
        return (
            <View style={styles.container}>
                <Video
                    source={require('../../media/scores.mp4')}
                    style={styles.imgBackground}
                    isMuted={true}
                    isLooping={true}
                    shouldPlay={true}
                    resizeMode={ResizeMode.COVER}
                    rate={1.0}
                />
                <View style={styles.form}>
                    <View style={styles.avatarContainer}>
                        {percentage < 65
                            ? <MaterialCommunityIcons name='emoticon-frown-outline' size={100} color={'white'}/>
                            : <MaterialCommunityIcons
                                name='emoticon-happy-outline'
                                size={100}
                                color={'white'}/>
}
                    </View>
                    <View style={styles.nameContainer}>
                        <Text style={styles.name}>Scores</Text>
                    </View>
                    <View style={styles.infoContainer}>
                        <Text style={styles.infoLabel}>Correct %</Text>
                        <Text style={styles.infoText}>{percentage.toFixed(2)}</Text>
                    </View>
                    <View style={styles.infoContainer}>
                        <Text style={styles.infoLabel}>Wrong %</Text>
                        <Text style={styles.infoText}>{(100 - percentage).toFixed(2)}</Text>
                    </View>
                    <View style={styles.buttonContainer}>
                        <TouchableOpacity style={styles.button} onPress={() => this.closeModal()}>
                            <Text style={styles.buttonText}>Close</Text>
                        </TouchableOpacity>
                    </View>

                </View>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        width: '100%',
        backgroundColor: 'black',
        justifyContent: 'center',
        alignItems: 'center'
    },
    imgBackground: {
        height: '100%',
        width: '100%',
        position: 'absolute'
    },
    form: {
        width: '100%',
        marginHorizontal: 15,
        padding: '8%',
        justifyContent: 'center',
        alignItems: 'center'
    },
    avatarContainer: {
        borderRadius: 70,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000000',
        shadowOffset: {
            width: 0,
            height: 3
        },
        shadowRadius: 6
    },
    exam: {
        fontWeight: 'bold'
    },
    buttonContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around'
    },
    button: {
        marginTop: 20,
        marginLeft: 10,
        marginRight: 10,
        backgroundColor: '#215e9d',
        borderRadius: 5,
        paddingVertical: 10,
        paddingHorizontal: 20
    },
    buttonText: {
        color: '#fff',
        fontSize: 20,
        fontFamily: 'LatoRegular'
    },
    nameContainer: {
        marginTop: 24,
        alignItems: 'center'
    },
    name: {
        fontSize: 24,
        fontWeight: '600',
        fontFamily:'AmorriaBrush',
        color:'white'
    },
    infoContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 12
    },
    infoLabel: {
        fontSize: 22,
        fontWeight: '600',
        color: 'white',
        marginRight: 8,
        fontFamily:'LatoRegular',
    },
    infoText: {
        fontSize: 20,
        color: 'white',
        fontFamily:'LatoRegular',
    }
});

export default Procents;