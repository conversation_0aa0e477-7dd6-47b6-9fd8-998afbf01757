import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { task167, task168, task169, task170 } from "./snips/basicSnips";

export const controlFlow : Questions[] = [
    {
        id: 167,
        category: 5,
        isImage: false,
        title: "What will be printed in console?",
        image: task167,
        answers: [
            {
                id: 1,
                content: '9',
                correct: false
            }, {
                id: 2,
                content: '11',
                correct: true
            }, {
                id: 3,
                content: '33',
                correct: false
            }, {
                id: 4,
                content: '39',
                correct: false
            }, {
                id: 5,
                content: '55',
                correct: false
            }
        ],
        explanation: 'There is nothing wrong with the code and the loop will keep executing as long as' +
                ' a is greater than b.\n \nWith each iteration, the values of a and b are modifie' +
                'd and added together to update x.\nLoop will stop when a = 5 and b = 6, whose su' +
                'm will give 11'
    }, {
        id: 168,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task168,
        answers: [
            {
                id: 1,
                content: '5 / 15',
                correct: false
            }, {
                id: 2,
                content: '6 / 16',
                correct: false
            }, {
                id: 3,
                content: '5 / 17',
                correct: false
            }, {
                id: 4,
                content: '6 / 17',
                correct: true
            }
        ],
        explanation: 'We are approaching from left to right. x++ operator in this case returns 5, but ' +
                'changes the value of x into 6. The sequantial order of this operation is below:o' +
                'n\n \nx++ + 2 * x\n \n5 + 2 * x \n \n5 + 2 * 6 = 17\n \n'
    }, {
        id: 169,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task169,
        answers: [
            {
                id: 1,
                content: 'Under 30',
                correct: false
            }, {
                id: 2,
                content: 'More than 30',
                correct: false
            }, {
                id: 3,
                content: 'Equals 30',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: true
            }, {
                id: 5,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'The code contains two else blocks, which is not valid syntax in Java.\n \nThe se' +
                'cond else block following the first if statement will cause a compilation error ' +
                'because each else must correspond to a single if statement.'
    }, {
        id: 170,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: task170,
        answers: [
            {
                id: 1,
                content: '10',
                correct: false
            }, {
                id: 2,
                content: '11',
                correct: true
            }, {
                id: 3,
                content: '12',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }, {
                id: 5,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'This code uses the ternary conditional operator ? :.\nIn this case, x is 7, mak' +
                'ing x > 5 evaluate into true and going to the next case where x < 7 marking this' +
                ' as false, returning the second option = 11.'
    }
]