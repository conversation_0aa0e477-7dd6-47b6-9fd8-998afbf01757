import React, {Component, ReactElement, RefObject } from 'react';
import {
    Text,
    View,
    StyleSheet,
    ScrollView,
    ActivityIndicator,
    Dimensions
} from 'react-native';
import {<PERSON>vide<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>Bar, IconButton} from "@react-native-material/core";
import {AntD<PERSON>, Feather, Ionicons, MaterialIcons} from "@expo/vector-icons";
import { QuestionService } from '../../service/QuestionService';
import {Answer, Questions} from '../models/Questions';
import { getCategory} from '../models/Categories';
import { generateRandomCode } from '../../service/RandomService';
import { MarkedQuestionsService } from '../../service/MarkedQuestionsService';
import { AnswerSheet, QuestionContentService } from '../../service/QuestionContentService';
import { BannerAd, BannerAdSize, TestIds } from 'react-native-google-mobile-ads';
import Modal from "react-native-modal";
import { SharedCheckbox } from '../shared/SharedCheckbox';
import ReportQuestion from '../../helpers/modals/ReportQuestion';
import { scale } from 'react-native-size-matters';
import GradientContainer from '../../helpers/GradientContainer';
import { appColors } from '../../utils/appColors';
import CodeEditor, { CodeEditorSyntaxStyles } from '@rivascva/react-native-code-editor';

interface AnswerState {
    dataset : AnswerSheet[],
    currentAnswerSheet : AnswerSheet,
    currentQuestion:Questions,
    marked:boolean,
    position:number,
    isModalVisible : boolean,
    codeFontSize:number;
}


const deviceHeight = Dimensions.get("window").height;
const deviceWidth = Dimensions.get("window").width;


const adUnitId = __DEV__ ? TestIds.BANNER : 'ca-app-pub-5981144475529351/3452174828';

class Response extends Component < {}, AnswerState > {

    private scrollViewRef: RefObject<ScrollView> = React.createRef<ScrollView>();

    constructor(props : {}) {
        super(props);
        this.state = {
            dataset: [],
            currentQuestion: null,
            currentAnswerSheet:null,
            marked:false,
            position:1,
            isModalVisible: false,
            codeFontSize:13,
        }
    }


    componentDidMount() {
        this.loadQuestion = this.loadQuestion.bind(this);
        this.loadQuestion();
        this.props.navigation.addListener('focus', this.loadQuestion);
    }

    async loadQuestion() {
        if (this.props.route.params) {
            const {id} = this.props.route.params;
            if (id!== undefined || id !==null) {
                var datasetAnswerFormPromise:Promise<AnswerSheet[]> = QuestionContentService.getAllAnswers();
                var answerFormPromise = QuestionContentService.getAnAnswerSheet(id);

                answerFormPromise.then((currentForm) => {
                    datasetAnswerFormPromise.then( (allForms) => {
                        this.setState({dataset:allForms, position:currentForm.serialNumber,currentAnswerSheet:currentForm});
                        this.setState({currentQuestion:QuestionService.getQuestionById(currentForm.questionId,currentForm.examId)});
                    })
                })

            } else {
                this.props.navigation.navigate('Result');
            }
        }
    }

    scrollToTop = () => {
        if (this.scrollViewRef.current) {
          this.scrollViewRef.current.scrollTo({ x: 0, y: 0, animated: false });
        }
    };

    renderImage(reactComponent: ReactElement){
        return reactComponent;
    }

    clickPrev = () => {
        var {dataset, position} = this.state;
        if(position!=0){
            this.setState({currentAnswerSheet:dataset[position-1], marked:false});
            this.setState({currentQuestion:QuestionService.getQuestionById(dataset[position-1].questionId,dataset[position].examId), position:position-1});
            this.scrollToTop();
            return;            
        }

    }
    clickNext = () => {
        var {dataset, position} = this.state;
        if(position+1<dataset.length){
            this.setState({currentAnswerSheet:dataset[position+1], marked:false});
            this.setState({currentQuestion:QuestionService.getQuestionById(dataset[position+1].questionId,dataset[position].examId), position:position+1});
            this.scrollToTop();
            return;
        }
    }

    clickIncreaseFontSize = () => {
        let {codeFontSize} = this.state;
        this.setState({codeFontSize:codeFontSize + 1});
    }

    clickDecreaseFontSize = () => {
        let {codeFontSize} = this.state;
        this.setState({codeFontSize:codeFontSize - 1});
    }

    clickMark = () => {
        var {currentQuestion,marked} = this.state;
        if(marked){
            MarkedQuestionsService.removeMarkQuestions(currentQuestion.id);
        }else{
            MarkedQuestionsService.updateMarkQuestions(currentQuestion.id);
        }
        this.setState({marked:!marked})

    }
    isSelectedAnswer = (id: number) => {
        var {currentAnswerSheet} = this.state;
        return currentAnswerSheet.selectedAnswers.map((answer) => answer.id).find((element) => element === id)!==undefined ? true : false;
    }


    renderItem = (answer : Answer) => {
        return (
        <React.Fragment key={generateRandomCode()}> 
            <SharedCheckbox
                answer={answer}
                isChecked = {this.isSelectedAnswer(answer.id)}
            />
            < Divider style = {{ marginVertical: 15 }}/>
        </React.Fragment>
         );
    };

    clickReport = () => {
        this.setState({isModalVisible: true});
   };

   closeModal = () => {
       this.setState({isModalVisible: false});
   };

    render() {
        const {currentQuestion, dataset, marked, position, codeFontSize, isModalVisible} = this.state;        
        if (currentQuestion === null) {
            return   <ActivityIndicator size="large" color="black" />
        }
        var title =getCategory(currentQuestion.category).title;
        var subtitle = position+1+"/"+dataset.length;
        return (
            <GradientContainer>
                <AppBar
                    title={title + " "+subtitle}
                    transparent={true}
                    titleStyle={{ fontFamily: "AmorriaBrush", fontSize:scale(25)}}
                    contentContainerStyle={{ marginTop:scale(35) }}          
                    leading={() => (
                        <IconButton
                            onPress={() => this.props.navigation.navigate("Result",{params: {}})}
                            icon={(props) => (<Feather name="arrow-left-circle" size={props.size+20} color={appColors.white} />)}
                        />
                    )}
                />
                <Modal
                    isVisible={isModalVisible}
                    animationInTiming={250}
                    animationOutTiming={500}
                    coverScreen={false}
                    deviceHeight={deviceHeight}
                    deviceWidth={deviceWidth}
                    style={{
                    marginTop: '15%'
                }}>
                    <ReportQuestion
                        currentQuestion={currentQuestion}
                        closeModal={() => this.closeModal()}/>
                </Modal>
            <ScrollView contentContainerStyle={{flexGrow: 1}}  ref={this.scrollViewRef}>
                <View style={styles.header}>
                    <View style={styles.topBar}>
                        <View style={styles.img}>
                            <Text key={generateRandomCode()} style={styles.subText}>#{currentQuestion.id} {currentQuestion.title}</Text>
                            <Divider style = {{ marginVertical: 5, backgroundColor:'black' }}/>
                            {currentQuestion.isImage
                                ? this.renderImage(currentQuestion.image)
                                : currentQuestion.image!=undefined
                                ?
                                <View style={styles.columnOne}>
                                    <View style={styles.iconRow}>
                                        <IconButton 
                                            icon={props => <Feather name="zoom-in" size={40} color="black" />} 
                                            onPress={ () => this.clickIncreaseFontSize()}
                                        />
                                        <IconButton 
                                            icon={props => <Feather name="zoom-out" size={40} color="black" />} 
                                            onPress={ () => this.clickDecreaseFontSize()}
                                        />
                                    </View>
                                    <ScrollView>
                                        <CodeEditor
                                            style={{
                                                fontSize: codeFontSize,
                                            }}
                                            readOnly={true}
                                            initialValue={String(currentQuestion.image)}
                                            language="java"
                                            syntaxStyle={CodeEditorSyntaxStyles.googlecode}
                                        />
                                    </ScrollView>
                                </View>                                
                                :null
                            }
                        </View>
                    </View>
                    <View style={styles.bottomBar}>
                        {currentQuestion.answers.map(this.renderItem)}
                    </View>
                    <View style={styles.explanationBar}>
                            <Text style={styles.explanationText}>{currentQuestion.explanation}</Text>
                    </View>
                    <View style={styles.buttonBar2}>
                        {   marked
                            ?
                            <Button
                                title="Unmark"
                                titleStyle={styles.buttonTitle}
                                style={styles.buttonStyle}
                                onPress={() => this.clickMark()}
                                leading={props => <Ionicons name="bookmarks-outline" {...props} />}
                            />
                            :
                            <Button
                                title="Mark"
                                titleStyle={styles.buttonTitle}
                                style={styles.buttonStyle}
                                onPress={() => this.clickMark()}
                                leading={props => <Ionicons name="bookmarks" {...props} />}
                            />
                        }

                        <Button
                                title="Report"
                                titleStyle={styles.buttonTitle}
                                style={styles.buttonStyle}
                                onPress={() => this.clickReport()}
                                leading={(props) => (<MaterialIcons name="bug-report" {...props}/>)}/>

                    </View>
                </View>
                <AppBar
                    variant="bottom"
                    color={appColors.blue}
                    leading={props => (
                    <IconButton icon={props => <AntDesign name="leftcircleo"  {...props} />} {...props} onPress={() => this.clickPrev()}/>
                    )}
                    trailing={props => (
                    <IconButton
                        icon={props => <AntDesign name="rightcircleo"  {...props} />}
                        onPress={() => this.clickNext()}
                        {...props}
                    />
                    )}
                    style={{ position: "absolute", start: 0, end: 0, bottom: 0 }}
                >
                </AppBar>
            </ScrollView>
            <View style={styles.ads}>
                <BannerAd 
                    unitId={adUnitId}
                    size={BannerAdSize.INLINE_ADAPTIVE_BANNER}
                    requestOptions={{
                        requestNonPersonalizedAdsOnly:false
                    }}                        
                />
            </View>
            </GradientContainer>
        );
    }
}

const styles = StyleSheet.create({
    white:{
        backgroundColor: 'white'
    },
    fullScreen:{ 
        flexDirection: 'row', 
        alignItems: 'center',
        height:'100%', 
        width:'100%' 
    },
    codeStyle:{
        fontFamily:'MonocodeRegular'
    },
    columnOne:{
        flexDirection:'column',
    },
    iconRow:{
        flexDirection:'row',
        justifyContent:'space-evenly',
    },
    rightIcon:{
        alignSelf:'flex-end',
    },
    loadedImage:{

    },
    header: {
        flex: 1,
        alignItems: 'center',
        backgroundColor: 'white',
        paddingHorizontal:10,
        paddingBottom:'20%',
    },
    topBar: {
        width: '100%',
        flexDirection: 'column',
    },
    img: {},
    explanationBar:{
        width: '95%',
        flex: 1
    },
    explanationText:{
        color: 'black',
        fontSize: 15,
        fontFamily: "LatoLight",
        textAlign:'justify'
    },
    bottomBar: {
        marginTop: scale(10),
        flexDirection: 'column',
        padding: 5,
        width: '95%',
        flex: 1
    },
    title: {
        color: 'black',
        fontSize: 20,
        fontFamily: 'monospace'
    },
    subText: {
        marginTop: '3%',
        color: 'black',
        fontSize: 17,
        fontWeight:'bold',
        fontFamily: "LatoRegular"
    },
    buttonBar: {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        flex: 1,
        padding: 10
    },
    buttonBar2: {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-around',
        backgroundColor: 'white',
        paddingVertical: scale(15),
        maxHeight: scale(60),
		flex: 1,
    },
    buttonStyle: {
        backgroundColor: appColors.blue,
    },
    buttonTitle:{
        fontFamily:'LatoLight'
    },
    ads:{
        maxHeight:'10%',
        width:'100%',
    },
});

export default Response;
