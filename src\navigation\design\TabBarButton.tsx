import React from 'react';
import {StyleSheet, Pressable} from 'react-native';
import { FontAwesome, FontAwesome5, MaterialCommunityIcons } from '@expo/vector-icons';
import { scale } from 'react-native-size-matters';
import { appColors } from '../../utils/appColors';

type Props={
    onPress: Function,
    onLongPress: Function,
    isFocused: boolean,
    routeName: string,
}

export const icon = {
    ExamType: ({color, size} : {color: string, size: number}) => (
        <FontAwesome name="graduation-cap" color={color} size={size}/>
    ),
    Statistics: ({color, size} : {color: string, size: number}) => (
        <FontAwesome5 name="chart-pie" color={color} size={size}/>
    ),
    ListMarked: ({color, size} : {color: string, size: number}) => (
        <FontAwesome name='bookmark' color={color} size={size}/>
    ),
    Chapters: ({color, size} : {color: string, size: number}) => (
        <FontAwesome name="book" color={color} size={size}/>
    )
};

const TabBarButton = (props: Props) => {
    const {isFocused, onLongPress, onPress, routeName} = props;
    return (    
    <Pressable
        onPress={onPress}
        onLongPress={onLongPress}
        style={styles.container}
    >
        {icon[routeName]({
            color:isFocused ? appColors.blue : appColors.blueLight,
            size: isFocused ? scale(30) : scale(20)
        })}
    </Pressable>
    )
}

export default TabBarButton;

const styles = StyleSheet.create({
    container:{
        flex: 1,
        justifyContent:'center',
        alignItems:'center',
        gap: 5,
    }
});





