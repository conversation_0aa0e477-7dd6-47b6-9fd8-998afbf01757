import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { javaCode212, javaCode213, javaCode214 } from "../exam/snips/basicSnips";
export const methodsDB : Questions[] = [
    {
        id: 212,
        category: 6,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode212,
        answers: [
            {
                id: 1,
                content: 'There is no compilation errors',
                correct: false
            }, {
                id: 2,
                content: 'There is 1 compilation error',
                correct: true
            }, {
                id: 3,
                content: 'There are 2 compilation errors',
                correct: false
            }, {
                id: 4,
                content: 'If compilation error line is/are removed, code will print StudyPlaySleep',
                correct: false
            }, {
                id: 5,
                content: 'If compilation error line is/are removed, code will print StudySleep',
                correct: true
            }, {
                id: 6,
                content: 'If compilation error line is/are removed, code will throw Exception',
                correct: false
            }
        ],
        explanation: 'This code has 1 compilation error.\n \nMethod summer makes a static reference to' +
                ' the non-static method play, if we comment reference to method play in summer me' +
                'thod, code will work just fine.\n \nSince the methods sleep(), study(), and summ' +
                'er() are declared as static, they can be called using the class name without cre' +
                'ating an instance of the class.  '
    }, {
        id: 213,
        category: 6,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode213,
        answers: [
            {
                id: 1,
                content: 'No compilation errors',
                correct: false
            }, {
                id: 2,
                content: '1 compilation error',
                correct: false
            }, {
                id: 3,
                content: '2 compilation errors',
                correct: false
            }, {
                id: 4,
                content: '3 compilation errors',
                correct: true
            }, {
                id: 5,
                content: '4 compilation errors',
                correct: false
            }
        ],
        explanation: 'This code snippet has 3 compilation errors. \n \nTwo compilation errors related ' +
                'to variable subject, since it is constant and static it should be initialized ei' +
                'ther at the beginning or in static initialization block, so subject="Art" and de' +
                'claration of subject causes compiler errors. \n \nFinally, compilation error occ' +
                'urs in the third static block, we cannot reassign final variables second time, o' +
                'ur lastName is final and should initialized only once.'
    }, {
        id: 214,
        category: 6,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode214,
        answers: [
            {
                id: 1,
                content: '0',
                correct: false
            }, {
                id: 2,
                content: '70',
                correct: true
            }, {
                id: 3,
                content: '4900',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }, {
                id: 5,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'This is a valid java code snippet, which donot throw any exceptions or has any c' +
                'ompilation errors. You initialize the variable debt with a value of 70.\n \nYou call' +
                ' the square method with the value of debt (which is 70).\n \nInside the square metho' +
                'd: You calculate newDebt as the square of the debt parameter, which is 70 * 70 =' +
                ' 4900.\n \nYou then set the local variable debt to 0, but this doesnot affect the or' +
                'iginal debt variable in the main method. You return the calculated newDebt, whic' +
                'h is 4900.\n \nBack in the main method: The newDebt variable is assigned the value 4' +
                '900 from the return value of the square method.\n \nYou print the value of the debt ' +
                'variable, which is still 70 because the local variable debt in the square method' +
                ' does not affect the original debt variable in the main method.'
    }
];