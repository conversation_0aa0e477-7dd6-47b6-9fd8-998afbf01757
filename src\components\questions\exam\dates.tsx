import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { javaCode215, javaCode216, javaCode217 } from "./snips/basicSnips";
//we are done
export const dates : Questions[] = [
    {
        id: 215,
        category: 10,
        isImage: false,
        title: "What code will lead to 7th of October 2007?",
        image: javaCode215,
        answers: [
            {
                id: 1,
                content: 'new LocalDate(2007, 10, 7)',
                correct: false
            }, {
                id: 2,
                content: 'new LocalDate(2007, 9, 7)',
                correct: false
            }, {
                id: 3,
                content: 'LocalDate.of(2007, 10, 7)',
                correct: true
            }, {
                id: 4,
                content: 'LocalDate.of(2007, 9, 7)',
                correct: false
            }, {
                id: 5,
                content: 'LocalDate.of(2007, Month.OCTOBER, 7)',
                correct: true
            }
        ],
        explanation: "LocalDate, LocalTime and LocalDateTime do not have public constructors. \n \nIt " +
                "is also important to note that calculation of month starts from 1. \n " +
                "\nMonth is an enum which is used in this class and therefore applicable."
    }, {
        id: 216,
        category: 10,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode216,
        answers: [
            {
                id: 1,
                content: '15:25',
                correct: false
            }, {
                id: 2,
                content: '16:25',
                correct: false
            }, {
                id: 3,
                content: '17:25',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: true
            }, {
                id: 5,
                content: 'Runtime exception is thrown',
                correct: false
            }
        ],
        explanation: 'LocalTime do not have plusDays() method.\nTherefore this code has compilation err' +
                'or'
    }, {
        id: 217,
        category: 10,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode217,
        answers: [
            {
                id: 1,
                content: '10 / JANUARY / 2001',
                correct: false
            }, {
                id: 2,
                content: '1 / JANUARY / 2001',
                correct: true
            }, {
                id: 3,
                content: '2 / JANUARY / 2001',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }, {
                id: 5,
                content: 'Runtime exception is thrown',
                correct: false
            }
        ],
        explanation: 'There is nothing wrong with this code, it compiles and prints 1 of January.\nConf' +
                'using part might be the date, which is provided as binary value which parsed int' +
                'o 1 afterwards.'
    }
]