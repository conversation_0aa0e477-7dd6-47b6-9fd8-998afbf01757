import StorageService, { markedData } from "./StorageService";

export class MarkedQuestionsService{

    static async getAllQuestions():Promise<number[]>{
        var allMarkedQuestions =  StorageService.getData(markedData);
        return allMarkedQuestions;
    }

    static async generalUpdate(updatedQuestions: number[]){
        await StorageService.post(markedData, updatedQuestions);
    }

    static updateMarkQuestions(newQuestion: number){
        this.getAllQuestions().then((questionIds) => {
            if(questionIds===null){
                questionIds = [];
            }
            questionIds.push(newQuestion);            
            this.generalUpdate(Array.from(new Set(questionIds)));
        })
    }


    static async removeMarkQuestions(removeQuestion: number){
        this.getAllQuestions().then((questionIds) => {
            if(questionIds===null){
                questionIds = [];
            }
            this.generalUpdate( questionIds.filter(id => id !== removeQuestion));
        })
    }

    static async removeAllMarkedQuestions(){
        await StorageService.delete(markedData);
    } 


} 