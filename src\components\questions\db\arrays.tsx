import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import {javaCode195, javaCode203, javaCode204, javaCode205} from "../exam/snips/basicSnips";
export const arrayDB : Questions[] = [
    {
        id: 194,
        category: 5,
        isImage: false,
        title: 'What is/are true?',
        answers: [
            {
                id: 1,
                content: 'Ehnanced for loop cannot be used within regular for loop',
                correct: false
            }, {
                id: 2,
                content: 'Ehnanced for loop cannot be used within regular while loop',
                correct: false
            }, {
                id: 3,
                content: 'Ehnanced for loop can be used within regular do-while loop',
                correct: true
            }, {
                id: 4,
                content: 'Ehnanced for loop cannot be used within regular switch',
                correct: false
            }, {
                id: 5,
                content: 'Everything is wrong',
                correct: false
            }
        ],
        explanation: 'Foreach loop can be used within any operator, as well as do while.'
    }, {
        id: 195,
        category: 3,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode195,
        answers: [
            {
                id: 1,
                content: 'DefaultAverage',
                correct: false
            }, {
                id: 2,
                content: 'AverageA',
                correct: false
            }, {
                id: 3,
                content: 'DefaultAverageA',
                correct: false
            }, {
                id: 4,
                content: 'Compilation error',
                correct: true
            }, {
                id: 5,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'This code produces compilation error, since bestGrade and passingGrade have the ' +
                'same value which is violation of switch operator rules. \n \nIt is not allowed t' +
                'o have same values twice. One should also note that this rules is regardless if ' +
                'variables were constants.'
    }, {
        id: 205,
        category: 4,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode205,
        answers: [
            {
                id: 1,
                content: '0 / 0 / true',
                correct: true
            }, {
                id: 2,
                content: '0 / 1 / true',
                correct: false
            }, {
                id: 3,
                content: '1 / 1 / true',
                correct: false
            }, {
                id: 4,
                content: 'Exception is thrown',
                correct: false
            }
        ],
        explanation: 'boolean bl1 = list1.contains(new Integer("3")); Checks if the list contains the ' +
                'value 3. Since we added 3 to the list, this will be true. \n \nint indexVal = li' +
                'st1.indexOf(1); Finds the index of the value 1 in the list. The value 1 is at in' +
                'dex 0, so indexVal will be 0.\n \nlist1.clear(); Clears the list, removing all e' +
                'lements from it.\n \nint size = list1.size(); Gets the size of the cleared list,' +
                ' which is now 0. \n \nHence the values of indexVal (0), size (0), and bl1 (true)' +
                '.'
    }, {
        id: 204,
        category: 4,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode204,
        answers: [
            {
                id: 1,
                content: '1234',
                correct: false
            }, {
                id: 2,
                content: '1234null',
                correct: false
            }, {
                id: 3,
                content: '1234 and Exception',
                correct: true
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }
        ],
        explanation: 'An attempt to iterate through the elements in list1 using an enhanced for loop (' +
                'for-each loop) throws an exception during runtime, however code snippet itself h' +
                'as no compilation errors.\n \nThis loop should work fine for the integers, but i' +
                't will throw a NullPointerException when it encounters the null value because yo' +
                'u are trying to unbox a null value into an int, which is not allowed.\n \nIt is ' +
                'should also be noted that primitive types cannot be null, if int would be change' +
                'd into Integer, this code wouldnot throw any exceptions.'
    }, {
        id: 203,
        category: 4,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode203,
        answers: [
            {
                id: 1,
                content: 'false / true',
                correct: false
            }, {
                id: 2,
                content: 'false / false',
                correct: false
            }, {
                id: 3,
                content: 'Compilation error',
                correct: false
            }, {
                id: 4,
                content: 'Exception is thrown',
                correct: true
            }
        ],
        explanation: 'This code snippet throws an \nIndexOutOfBoundsException because of the set metho' +
                'd -> list2.set(0, 7); which tries to set the element at index 0 of list2 to 7. ' +
                '\n \nHowever, since list2 is empty, there is no element at index 0, which result' +
                's in an IndexOutOfBoundsException being thrown at runtime.'
    }
];