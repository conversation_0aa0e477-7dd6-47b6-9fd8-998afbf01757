import {
  Feather,
  MaterialCommunityIcons,
  MaterialIcons,
} from "@expo/vector-icons";
import React, { Component } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  Text,
  TouchableOpacity,
} from "react-native";
import {
  AppBar,
  IconButton,
} from "@react-native-material/core";
import { generateRandomCode } from "../../service/RandomService";
import { QuestionService } from "../../service/QuestionService";
import { AnswerSheet } from "../../service/QuestionContentService";
import {
  BannerAd,
  BannerAdSize,
  TestIds,
} from "react-native-google-mobile-ads";
import { StatisticsService } from "../../service/StatisticsService";
import { scale, verticalScale } from "react-native-size-matters";
import { appColors } from "../../utils/appColors";
import GradientContainer from "../../helpers/GradientContainer";
import LoadingDots from "react-native-loading-dots";

interface GeneralState {
  answerSheets: AnswerSheet[];
  loading: boolean;
}

const adUnitId = __DEV__
  ? TestIds.BANNER
  : "ca-app-pub-5981144475529351/3452174828";

class ListStatisticQuestions extends Component<{}, GeneralState> {
  constructor(props: {}) {
    super(props);
    this.state = {
      answerSheets: [],
      loading: true,
    };
  }

  componentDidMount(): void {
    this.loadResults();
    this.loadResults = this.loadResults.bind(this);    
    this.props.navigation.addListener("focus", this.loadResults);
  }

  async loadResults() {
    try {
      if (this.props.route.params) {
        const { statisticsID } = this.props.route.params;
        StatisticsService.getStatisticsById(statisticsID).then((stats) =>{
          if(stats.length===1){
            this.setState({ answerSheets: stats[0].answerSheets, loading: false });
          }          
        });        
      }
    } catch (error) {
      console.log(error);
    }
  }





  renderItem = (currentSheet: AnswerSheet) => {
    const { answerSheets } = this.state;
    var pos = currentSheet.serialNumber;
    var question = QuestionService.getQuestionById(
      currentSheet.questionId,
      currentSheet.examId
    );
    let meta = "\u2714";
    if(currentSheet.isWrong){
      meta = '\u274C';
    }
    const { statisticsID } = this.props.route.params;
    return (
        <TouchableOpacity
              style={styles.containerCustom}
              key={generateRandomCode()}
              onPress={() =>
                this.props.navigation.navigate("SpecificQuestion", {
                  position: pos,
                  currentQuestion: question,
                  dataset: answerSheets,
                  currentAnswerSheet: answerSheets[pos],
                  statisticsID: statisticsID
                })
              }
              >

        <View style={styles.rowDirection}>
          <View style={styles.iconContainer}>
              {
                currentSheet.isWrong ? (
                  <MaterialIcons name={"do-not-disturb-on"} color={"#e64526"} size={scale(35)} />
                ) : (
                  <MaterialCommunityIcons name={"checkbox-marked"} color={appColors.white} size={scale(35)} />
                )
              }
          </View>
          <View style={styles.content}>
            <View style={styles.mainContent}>
              <View style={styles.text}>                     
                <Text style={[styles.groupName]}>{"#"+question.id} {meta}</Text>
              </View>
              <Text style={styles.timeAgo}>{question.title}</Text>
            </View>
          </View>
          <View style={styles.iconContainer}>
              <MaterialCommunityIcons 
              name="chevron-right"
              color={appColors.white}
              size={scale(35)}
              style={styles.iconCenter}
            />
          </View>
        </View>
      </TouchableOpacity>
     
    );
  };

  render() {
    const { answerSheets, loading } = this.state;
    return (
      <GradientContainer>
          <AppBar
            transparent={true}
            titleStyle={{ fontFamily: "AmorriaBrush", fontSize:scale(25)}}
            contentContainerStyle={{ marginTop:scale(35) }} 
            title={"Questions"}
            leading={(props) => (
              <IconButton
                onPress={() => this.props.navigation.navigate("MyTabsNew", {screen: 'Statistics'})}
                icon={() => ( <Feather name="arrow-left-circle" color={appColors.white} size={scale(30)} /> )}
              />
            )}
          />
        <ScrollView>
        {loading ? (
            <View style={styles.dotsContainer}>
                <LoadingDots
                    size={scale(35)}
                    colors={[appColors.white, appColors.white, appColors.white]}
                    dots={3}
                    borderRadius={scale(15)}/>
              </View>
          ) : (
            answerSheets.map(this.renderItem)
          )}
        </ScrollView>
        <View style={styles.ads}>
          <BannerAd
            unitId={adUnitId}
            size={BannerAdSize.INLINE_ADAPTIVE_BANNER}
            requestOptions={{
              requestNonPersonalizedAdsOnly: false,
            }}
          />
        </View>
      </GradientContainer>
    );
  }
}

const styles = StyleSheet.create({
  rowDirection: {
    flexDirection: "row",
  },
  iconContainer: {
    flexDirection: "column",
    margin: scale(5),
    alignSelf: "center",
  },
  content: {
    flex: 1,
    marginLeft: scale(3),
    marginTop: scale(5),
    flexDirection: "column",
  },
  mainContent: {
    marginRight: scale(5),
  },
  iconCenter: {},
  text: {
    marginBottom: scale(5),
    flexDirection: "row",
    flexWrap: "wrap",
  },
  groupName: {
    fontSize: scale(16),
    fontFamily: "AmorriaBrush",
    color:appColors.white
  },
  timeAgo: {
    marginBottom: "1%",
    fontSize: scale(14),
    color: appColors.white,
    fontFamily: "LatoRegular",
  },
  containerCustom: {
    padding: scale(8),
    width: "100%",
    flexDirection: "row",
    borderBottomWidth: scale(1),
    borderColor: "#FFFFFF",
    alignItems: "flex-start",
  },
  container: {
    flex: 1,
    flexDirection: "column",
    backgroundColor: "white",
    height: "90%",
  },
  itemContainer: {
    alignItems: "center",
    marginBottom: 20,
    shadowColor: "#cccccc",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 1,
    marginHorizontal: 20,
    backgroundColor: "#fff",
    borderRadius: 10,
    paddingBottom: 20,
  },
  coverPhoto: {
    width: "100%",
    height: 60,
    resizeMode: "cover",
  },
  avatarContainer: {
    alignItems: "center",
    marginTop: -35,
  },
  avatar: {
    backgroundColor: "white",
  },
  name: {
    marginTop: 5,
    fontSize: 14,
    fontWeight: "bold",
  },
  ads: {
    maxHeight: "10%",
    width: "100%",
  },
  dotsContainer: {
    marginHorizontal: scale(40),
    marginTop: verticalScale(190),
    width: '40%',
    alignContent: 'center',
    alignSelf: 'center',
    height: '15%',
    justifyContent: 'center',
  },
});

export default ListStatisticQuestions;
