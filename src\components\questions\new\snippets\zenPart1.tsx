export const task300 = `
public class Test {
    public static void main(String[] args) {
        int x = 5;
        int y = x++ + ++x;
        System.out.println(y);
    }
}
`;

export const task301 = `
public class LoopTest {
    public static void main(String[] args) {
        int count = 0;
        for (int i = 0; i < 5; i++) {
            count++;
        }
        System.out.println(count);
    }
}
`;

export const task302 = `
public class DataTypeTest {
    public static void main(String[] args) {
        byte a = 127;
        a++;
        System.out.println(a);
    }
}
`;

export const task303 = `
public class ExceptionTest {
    public static void main(String[] args) {
        try {
            throw new NullPointerException("Custom message");
        } catch (NullPointerException e) {
            System.out.println("Caught: " + e.getMessage());
        } finally {
            System.out.println("Finally executed");
        }
    }
}`;

export const task304 = `public class MethodOverloading {
    void display(int a) {
        System.out.print("Integer: " + a);
    }

    void display(double a) {
        System.out.print("Double: " + a);
    }

    void display(String a) {
        System.out.print("String: " + a);
    }

    public static void main(String[] args) {
        MethodOverloading obj = new MethodOverloading();
        obj.display(5);
        obj.display(5.5);
        obj.display("Hello");
    }
}`;

export const task305 = `class Parent {
    void show() {
        System.out.println("Parent class method");
    }
}

public class Child extends Parent {
    void show() {
        System.out.println("Child class method");
    }

    public static void main(String[] args) {
        Parent obj1 = new Parent();
        Parent obj2 = new Child();
        obj1.show();
        obj2.show();
    }
}`;

export const task306 = `public class ArrayTest {
    public static void main(String[] args) {
        int[][] arr = {
            {1, 2, 3},
            {4, 5, 6},
            {7, 8, 9}
        };
        int sum = 0;
        for (int i = 0; i < arr.length; i++) {
            for (int j = 0; j < arr[i].length; j++) {
                if (arr[i][j] % 2 == 0) {
                    sum += arr[i][j];
                }
            }
        }
        System.out.println(sum);
    }
}`;

export const task307 = `import java.util.Arrays;
import java.util.List;

public class LambdaTest {
    public static void main(String[] args) {
        List<String> names = Arrays.asList("John", "Jane", "Alex", "Sophia");
        names.forEach(name -> {
            if (name.length() > 4) {
                System.out.println(name);
            }
        });
    }
}`;

export const task308 = `import java.time.LocalDate;

public class DateTimeTest {
    public static void main(String[] args) {
        LocalDate date = LocalDate.of(2025, 3, 4);
        System.out.println("Date: " + date);
        System.out.println("Year: " + date.getYear());
        System.out.println("Month: " + date.getMonth());
        System.out.println("Hours: " + date.getHours());
    }
}`;

export const task309 = `public class DataTypeTest {
    public static void main(String[] args) {
        Integer a = 5;
        Double b = 7.5;
        a = (int) (b + a);
        Double c = (double) (a + b);
        System.out.println("a: " + a);
        System.out.println("b: " + b);
        System.out.println("c: " + c);
    }
}`;

export const task310 = `class Animal {
    void eat() {
        System.out.println("Animal is eating");
    }
}

class Dog extends Animal {
    void bark() {
        System.out.println("Dog is barking");
    }
}

public class Main {
    public static void main(String[] args) {
        Animal myDog = new Dog();
        myDog.eat();
        // myDog.bark(); // Uncommenting this line will cause a compilation error
    }
}`;

export const task311 = `public class DataTypeExample {
    public static void main(String[] args) {
        int a = 10;
        double b = 5.5;
        float c = 15;
        boolean d = true;

        System.out.println(a + b);
        System.out.println(c + a);
        System.out.println(d);
    }
}`;

export const task312 = `
public class OperatorExample {
    public static void main(String[] args) {
        int x = 10;
        int y = 5;
        int z = x++ + --y + ++x + y--;
        
        System.out.println("x = " + x);
        System.out.println("y = " + y);
        System.out.println("z = " + z);
    }
}`;

export const task313 = `
public class ExceptionExample {
    public static void main(String[] args) {
        try {
            int[] numbers = {1, 2, 3};
            System.out.println(numbers[3]); // This will throw an ArrayIndexOutOfBoundsException
        } catch (ArrayIndexOutOfBoundsException e) {
            System.out.println("Caught an ArrayIndexOutOfBoundsException");
        } catch (Exception e) {
            System.out.println("Caught a generic Exception");
        } finally {
            System.out.println("Finally block executed");
        }
    }
}`;

export const task314 = `
public class ScopeExample {
    static int x = 10;

    public static void main(String[] args) {
        int x = 20;
        System.out.print(x + ScopeExample.x + m1(x));
    }

    static int m1(int x) {
        return x + ScopeExample.x;
    }
}`;

export const task315 = `
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

public class DateTimeExample {
    public static void main(String[] args) {
        LocalDate date = LocalDate.of(2025, 3, 4);
        LocalTime time = LocalTime.of(15, 30);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm");

        System.out.println("Formatted Date and Time: " + date.atTime(time).format(formatter));
    }
}`;

export const task316 = `
class Animal {
    void makeSound() {
        System.out.println("Animal is making a sound");
    }
}

class Dog extends Animal {
    @Override
    void makeSound() {
        System.out.println("Dog is barking");
    }

    void fetch() {
        System.out.println("Dog is fetching");
    }
}

public class InheritanceExample {
    public static void main(String[] args) {
        Animal myDog = new Dog();
        myDog.makeSound();
    }
}`;

export const task317 = `
public class ArrayExample {
    public static void main(String[] args) {
        int[] numbers = {1, 2, 3, 4, 5};
        int[] copy = new int[numbers.length];

        for (int i = 0; i < numbers.length; i++) {
            copy[i] = numbers[numbers.length - 1 - i];
        }

        System.out.println("Original Array: " + java.util.Arrays.toString(numbers));
        System.out.println("Reversed Copy: " + java.util.Arrays.toString(copy));
    }
}`;
export const task318 = `
import java.util.Arrays;
import java.util.List;

public class LambdaExample {
    public static void main(String[] args) {
        List<String> names = Arrays.asList("Alice", "Bob", "Charlie", "David", "Chuck");
        names.stream()
             .filter(name -> name.startsWith("C") || name.endsWith("e"))
             .forEach(System.out::println);
    }
}`;

export const task319 = `
public class CompoundOperatorExample {
    public static void main(String[] args) {
        int x = 5;
        int y = 3;
        int z = 2;

        x += y++ * --z + x / z;
        System.out.println("x: " + x);
        System.out.println("y: " + y);
        System.out.println("z: " + z);
    }
}`;

export const task320 = `
import java.util.ArrayList;
import java.util.List;

public class ArrayListExample {
    public static void main(String[] args) {
        List<Integer> numbers = new ArrayList<>();
        numbers.add(1);
        numbers.add(2);
        numbers.add(3);
        numbers.add(4);

        numbers.removeIf(n -> n % 2 == 0);
        numbers.replaceAll(n -> n * 2);

        System.out.println("Modified List: " + numbers);
    }
}`;

export const task321 = `
public class NestedLoopExample {
    public static void main(String[] args) {
        int sum = 0;
        outer: for (int i = 1; i <= 3; i++) {
            for (int j = 1; j <= 3; j++) {
                if (i == j) {
                    continue outer;
                }
                sum += i + j;
            }
        }
        System.out.println("Sum: " + sum);
    }
}`;

export const task322 = `
public class RecursiveMethodExample {
    public static void main(String[] args) {
        int result = factorial(2);
        System.out.println("Factorial: " + result);
    }

    public int factorial(int n) {
        if (n == 0 || n == 1) {
            return 1;
        }
        return n * factorial(n - 1);
    }
}`;

export const task323 = `
class Vehicle {
    void start() {
        System.out.println("Vehicle is starting");
    }
}

class Car extends Vehicle {
    @Override
    void start() {
        System.out.println("Car is starting");
    }

    void drive() {
        System.out.println("Car is driving");
    }
}

public class InheritanceExample {
    public static void main(String[] args) {
        Vehicle myCar = new Car();
        myCar.start();
        ((Car) myCar).drive();
    }
}`;

export const task324 = `
public class ExceptionExample {
    public static void main(String[] args) {
        try {
            int result = divide(10, 0);
            System.out.println("Result: " + result);
            throw new Exception();
        } catch (ArithmeticException e) {
            System.out.println("Caught ArithmeticException");
        } catch (Exception e) {
            System.out.println("Caught Exception");
        } finally {
            System.out.println("Finally block executed");
        }
    }

    static int divide(int a, int b) {
        return a / b;
    }
}`;

export const task325 = `
import java.util.Arrays;
import java.util.List;

public class LambdaExample {
    public static void main(String[] args) {
        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5);
        int sum = numbers.stream()
                         .filter(n -> n % 2 == 1)
                         .mapToInt(n -> n * 2)
                         .sum();

        System.out.println("Sum: " + sum);
    }
}`;

export const task328 = `
public class Main {
    public static void main(String[] args) {
        Short s1 = 200;
        Integer s2 = 400;
        Long s3 = (Long) s1 + s2;    //line n1
        String s4 = (String) (s3 * s2);    //line n2
        System.out.println("Sum is " + s4);
    }
}`;

export const task329 = `
public class Test{
    
    public static void main (String[] args){
        int arr1[] = {1, 2, 3};
        int arr2[] = {1, 2, 3};
        if (Arrays.equals(arr1, arr2))
            System.out.println("Same");
        else
            System.out.println("Not same");
    }

}
`;

export const task330 = `
public class Main{
    public static void main(String[] args){
        for(int i = 0; i<5; i++)
        System.out.println("Hello");
        i++;
        i--;        
    }
}
`;

export const task331 = `
public class MethodOverloadingExample {
    public static void main(String[] args) {
        int result1 = calculate(1);
        int result2 = calculate(1, 2);
        int result3 = calculate(1, 2, 3);

        System.out.println("Result 1: " + result1);
        System.out.println("Result 2: " + result2);
        System.out.println("Result 3: " + result3);
    }

    static int calculate(int a) {
        return a * 2;
    }

    static int calculate(int a, int b) {
        return calculate(a) + b;
    }

    static int calculate(int a, int b, int c) {
        return calculate(a, b) * c;
    }
}`;

export const task332 = `
public class Main{
    public static void main(String[] args){
        for(int i = 0; i>5; )
        System.out.println("Hello");     
    }
}`;

export const task336 = `
public class Main {
	
    public static void test(int a, int b) {
        System.out.println("Method 1: int, double");
    }

    public static void test(Integer a, Integer b) {
        System.out.println("Method 2: Integer, Integer");
    }
    
    public static void test(Object a, Object b) {
        System.out.println("Method 3: Object, Object");
    }

    public static void main(String[] args) {
    	Integer in = 5;
    	int i = 1;
        test(in, i);   
    }
}
`;

export const task337 = `public class DataTypeTest {
    public static void main(String[] args) {
        byte b = 127;
        b += 1;

        float f = 5.0f;
        double d = 10.0;
        d = f * d;

        int x = (int) 2.9;
        int y = 2;
        boolean result = (x == y);
        
        System.out.println(b);
        System.out.println(d);
        System.out.println(result);
    }
}`;

export const task338 = `public class DataTypesTest {
    public static void main(String[] args) {
        double d = 10.5;
        int i = 4;
        double result = d / i;
        
        float f = 7.5f;
        int x = (int) (f + i);
        
        System.out.println(result);
        System.out.println(x);
    }
}`;

export const task339 = `public class OperatorsTest {
    public static void main(String[] args) {
        int a = 10;
        int b = 20;
        int c = 30;
        boolean result = (a < b && b < c) || !(a == 10);
        
        System.out.println(result);
    }
}`;

export const task340 = `public class ArrayTest {
    public static void main(String[] args) {
        int[] arr = {1, 2, 3, 4, 5};
        int[] newArr = arr;
        newArr[0] = 10;
        
        System.out.println(arr[0]);
        System.out.println(newArr[0]);
    }
}`;

export const task341 = `public class LoopTest {
    public static void main(String[] args) {
        int i = 1;
        int sum = 0;
        
        while (i <= 5) {
            sum += i;
            i++;
        }
        
        System.out.println(sum);
    }
}`;

export const task342 = `public class StringComparisonTest {

    public static void main(String[] args) {
        String str1 = new String("Java");
        String str2 = new String("Java");
        String str3 = "Java";
        System.out.println(str1 == str2);
        System.out.println(str1.equals(str2));
        System.out.println(str1 == str3);
    }

}`;

export const task343 = `public class CastingTest {

    public static void main(String[] args) {
        long l = 1000L;
        int i = (int) l;
        short s = (short) i;
        byte b = (byte) s;
        System.out.println("Long: " + l);
        System.out.println("Int: " + i);
        System.out.println("Short: " + s);
        System.out.println("Byte: " + b);
    }

}`;

export const task344 = `public class StringBuilderTest {
    public static void main(String[] args) {
        StringBuilder sb = new StringBuilder("Hello");
        sb.append(" World").insert(5, ",");
        sb.delete(0, 7);
        System.out.println(sb);
    }
}`;

export const task345 = 
`public class Main {
    public static void main(String[] args) {
        String str = null;
        if (str.equals(null)) {
            System.out.println("String matches!");
        } else {
            System.out.println("String don't match!");
        }
    }
}`;

export const task346=`
public static void main(String[] args){
    try{
        int a[]= {1, 2, 3, 4};
        for (int i = 1; i <= 4; i++){
            System.out.println ("a[" + i + "]=" + a[i] + "\\n");
        }
    }catch (Exception e){
        System.out.println ("error = " + e);
    } catch (ArrayIndexOutOfBoundsException e) {
        System.out.println ("ArrayIndexOutOfBoundsException");
    }
}
`

export const task354=`
class A {
    void print(int x) {
        System.out.println("A");
    }
}

class B extends A {
    void print(double x) {
        System.out.println("B");
    }
}

public class Main {
    public static void main(String[] args) {
        A obj = new B();
        obj.print(5);
    }
}
`

export const task355=`
public class Main {
    public static void main(String[] args) {
        try {
            System.out.println("A");
            throw new RuntimeException();
        } catch (RuntimeException e) {
            System.out.println("B");
            return;
        } finally {
            System.out.println("C");
        }
    }
}`;

export const task356=`
class Animal {
    String sound = "Animal Sound";
    void makeSound() {
        System.out.println(sound);
    }
}

class Dog extends Animal {
    String sound = "Bark";
    void makeSound() {
        System.out.println(sound);
    }
}

public class Main {
    public static void main(String[] args) {
        Animal myDog = new Dog();
        System.out.println(myDog.sound);
        myDog.makeSound();
    }
}
`

export const task357=`
public static void main (String[] args){
    try{
        int a = 0;
        System.out.println ("a = " + a);
        int b = 20 / a;
        System.out.println ("b = " + b);
    }catch(ArithmeticException e){
        System.out.println ("Divide by zero error");
    }finally{
        System.out.println ("inside the finally block");
    }
}
`

export const task358=`
class Base {
    public void Print() {
        System.out.println("Base");
    }         
}

class Derived extends Base {    
    public void Print() {
        System.out.println("Derived");
    }
}

class Main{
    public static void DoPrint( Base o ) {
        o.Print();   
    }
    public static void main(String[] args) {
        Base x = new Base();
        Base y = new Derived();
        Derived z = new Derived();
        DoPrint(x);
        DoPrint(y);
        DoPrint(z);
    }
}
`

export const task361=`
class Base {
    public void foo() { 
        System.out.println("Base"); 
    }
}
 
class Derived extends Base {
    private void foo() { 
        System.out.println("Derived"); 
    } 
}
 
public class Main {
    public static void main(String args[]) {
        Base b = new Derived();
        b.foo();
    }
} 
`;

export const task365=`
class Base {
    final public void show() { 
        System.out.println("Base"); 
    }
}
 
class Derived extends Base {
    public void show() { 
        System.out.println("Derived"); 
    } 
}
 
public class Main {
    public static void main(String args[]) {
        Base b = new Derived();
        b.show();
    }
} 
`;