import {Questions} from "../../models/Questions";
import {Image} from 'react-native';
import { javaCode239, javaCode240, javaCode241, javaCode242, javaCode243 } from "../exam/snips/basicSnips";
export const excepDB : Questions[] = [
    {

        id: 243,
        category: 8,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode243,
        answers: [
            {
                id: 1,
                content: 'AB',
                correct: false
            }, {
                id: 2,
                content: 'AD',
                correct: true
            }, {
                id: 3,
                content: 'ABC',
                correct: false
            }, {
                id: 4,
                content: 'ADC',
                correct: false
            }, {
                id: 5,
                content: 'ADCG',
                correct: false
            }
        ],
        explanation: 'The string "A" is printed to the console using System.out.print("A");.\n \nThe c' +
                'ode enters the try block. Within the try block, an attempt is made to calculate ' +
                'the result of dividing a by b. However, both a and b are 0, which results in an ' +
                'arithmetic exception: division by zero.\n \nSince an exception is thrown, the co' +
                'ntrol flow is transferred to the catch block.\n \nIn the catch block, the string' +
                ' "D" is printed to the console using System.out.print("D");.\n \nThe System.exit' +
                '(0); statement is executed, causing the program to exit immediately.\n \nIt is i' +
                'mportant to note that the finally block is skipped because of System.exit(0);.\n' +
                ' \nLeading to the answer of AD.'
    }, {
        // what is the result
        id: 242,
        category: 8,
        isImage: false,
        title: "What will be the output if we ran danger(0,1)?",
        image: javaCode242,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '-1',
                correct: false
            }, {
                id: 3,
                content: '0',
                correct: false
            }, {
                id: 4,
                content: '2',
                correct: false
            }, {
                id: 5,
                content: 'Compilation error',
                correct: true
            }
        ],
        explanation: 'This code snippet has unreachable ArithmeticException statement. Since Arithmeti' +
                'cException is a subclass of Exception, any ArithmeticException would be caught b' +
                'y the Exception block, making ArithmeticException block unreachable.\n \nAny unr' +
                'eachable code creates compilation error.'
    }, {
        // what is the result
        id: 241,
        category: 8,
        isImage: false,
        title: "What is the result of this method?",
        image: javaCode241,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '-1',
                correct: false
            }, {
                id: 3,
                content: '0',
                correct: true
            }, {
                id: 4,
                content: 'Compilation error',
                correct: false
            }
        ],
        explanation: 'This method will always return 0 due to the finally block, with or without excep' +
                'tion thrown finally block will always be executed returning 0 as a response'
    }, {
        // what is the result
        id: 240,
        category: 8,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode240,
        answers: [
            {
                id: 1,
                content: '1',
                correct: false
            }, {
                id: 2,
                content: '-1',
                correct: false
            }, {
                id: 3,
                content: '0',
                correct: false
            }, {
                id: 4,
                content: '2',
                correct: false
            }, {
                id: 5,
                content: 'Compilation error',
                correct: true
            }
        ],
        explanation: 'This code snippet has unreachable return 2;'
    }, {
        // what is the result
        id: 239,
        category: 8,
        isImage: false,
        title: "What will be printed in console?",
        image: javaCode239,
        answers: [
            {
                id: 1,
                content: 'ADFGB',
                correct: false
            }, {
                id: 2,
                content: 'AEFGB',
                correct: true
            }, {
                id: 3,
                content: 'AEFB',
                correct: false
            }, {
                id: 4,
                content: 'ACDFGB',
                correct: false
            }, {
                id: 5,
                content: 'ADEFGB',
                correct: false
            }
        ],
        explanation: 'We start from entering method go() -> danger() -> A is printed. \n \nWe execute the' +
                ' code in try{} block and then ArrayIndexOutOfBoundsException exception is thrown' +
                ' -> E is printed.\n \nFinally block is called -> F is printed.\n \nSince everyt' +
                'hing is covered in try{} catch(){} block we go further -> G is printed.\n \ndanger' +
                '() method is completed -> B is printed.\n \nLeading us to the following output -> AEFGB.'
    }
];