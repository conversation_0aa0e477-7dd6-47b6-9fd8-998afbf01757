import { AnswerSheet } from "./QuestionContentService";
import StorageService, { statsData } from "./StorageService";

export interface StatisticsI{
    id:number;
    examId:number;
    attemptNumber:number;
    isFailed:boolean;
    answerSheets:AnswerSheet[];
    date:Date;
}


export class StatisticsService{

    public static threshold:number = 65;

    static async uploadNewStatistic(answerSheets: AnswerSheet[], procent:number):Promise<void>{        
        this.getStatisticsByExamId(answerSheets[0].examId).then((examSpecificDataSet) => {
            this.getAllStatistics().then(
                (data) => {                    
                    if(data === null){
                        data = [];
                    }
    
                    var statistics: StatisticsI = {
                        id:data.length + 1,
                        examId:answerSheets[0].examId,
                        attemptNumber: examSpecificDataSet.length + 1,
                        isFailed: procent< this.threshold ? true : false,
                        answerSheets:answerSheets,
                        date:new Date(),
                    } 
    
    
                    data.push(statistics)
                    StorageService.post(statsData, data);   
            }); 
        })

         
    }
    
    static async getAllStatistics():Promise<StatisticsI[]> {
        return StorageService.getData(statsData);
    }    

    static async deleteStatistics():Promise<void>{
        await StorageService.delete(statsData);
    }

    static async getStatisticsByExamId(examId):Promise<StatisticsI[]>{
        return this.getAllStatistics().then(
            (data) => {
                try{

                    var filteredStatistics = [];

                    if(data !== null){
                        filteredStatistics = data.filter((statistic) => {
                            return statistic.examId === examId;
                        });
                        filteredStatistics.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());                    
                 
                    }
                return filteredStatistics;
                }catch(error){
                    console.log("Error1 = " ,error)
                }


            })
    }

    static async getStatisticsById(id):Promise<StatisticsI[]>{
        return this.getAllStatistics().then(
            (data) => {
                try{

                    var filteredStatistics = [];

                    if(data !== null){
                        filteredStatistics = data.filter((statistic) => {
                            return statistic.id === id;
                        });
                        filteredStatistics.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());                    
                 
                    }
                return filteredStatistics;
                }catch(error){
                    console.log("Error1 = " ,error)
                }


            });
    }

    static async getStatisticsByExamIdAndId(examId:number, id:number):Promise<StatisticsI>{
        return this.getAllStatistics().then(
            (data) => {
                try{
                    var filteredStatistics = null;
                    
                    if(data !== null){
                        filteredStatistics = data.filter((statistic) => {
                            return statistic.examId === examId && statistic.id === id;
                        });                        
                    }
                    return filteredStatistics;
                }catch(error){
                    console.log("Error = " ,error)
                }


            })
    }
    
}
