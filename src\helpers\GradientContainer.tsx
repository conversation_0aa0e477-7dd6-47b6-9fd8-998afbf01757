import React, {ReactNode} from 'react';
import { StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { appColors } from '../utils/appColors';

interface ContainerProps {
    children : ReactNode;
}

class GradientContainer extends React.Component < ContainerProps > {
    render() {
        const {children} = this.props;
        return <LinearGradient colors={[appColors.blue, appColors.blueLight]} style={styles.container}>{children}</LinearGradient>;
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1
    },
});

export default GradientContainer;
