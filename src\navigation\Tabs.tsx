import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';

import Statistics from '../components/statistics/Statistics';
import Chapters from '../components/chapters/Chapters';
import ExamType from '../components/exams/ExamType';
import { TabBar } from './design/TabBar';
import ListMarked from '../components/marked/ListMarked';

const TabNew = createBottomTabNavigator();

export const MyTabsNew = () => (
    <TabNew.Navigator tabBar={(props) => <TabBar {...props}/>}>
        <TabNew.Screen
            name="ExamType"
            component={ExamType}
            options={({navigation}) => ({
            headerShown: false,
            headerLeft: () => null
        })}/>
        <TabNew.Screen
            name="Statistics"
            component={Statistics}
            options={{
            headerShown: false,
            headerLeft: () => null
        }}/>
        <TabNew.Screen
            name="ListMarked"
            component={ListMarked}
            options={{
            headerShown: false,
            headerLeft: () => null
        }}/>
        <TabNew.Screen
            name="Chapters"
            component={Chapters}
            options={{
            headerShown: false,
            headerLeft: () => null
        }}/>
    </TabNew.Navigator>
);